# Git
.git
.gitignore
.github

# Documentation
README.md
*.md

# Development files
.vscode
.idea

# Node modules (we copy specific ones)
node_modules
*/node_modules

# Source files (we only need the build)
backend/src
backend/tests
backend/test
backend/__tests__

# Build artifacts we don't need
backend/build/**/*.map
backend/build/**/*.d.ts

# Environment files we don't copy
backend/env
!backend/env/lookenv.*.js

# Logs
*.log
logs

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Dependencies
.pnp
.pnp.js

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Functions and other directories we don't need in the image
functions/
infrastructure/
templates/
triggers/

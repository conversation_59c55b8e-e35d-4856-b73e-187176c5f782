name: Test Backend
on:
  pull_request:
    branches:
      - develop
      - main
      - dev
    paths:
      - "backend/**"
    types: [labeled]

jobs:
  test:
    timeout-minutes: 15
    defaults:
      run:
        working-directory: backend
    env:
      PUPPETEER_SKIP_CHROMIUM_DOWNLOAD: "true"
      PUPPETEER_SKIP_DOWNLOAD: "true"
      HUSKY: 0
      CI: true

      AWS_ACCESS_KEY_ID: TEST
      AWS_SECRET_ACCESS_KEY: test
      AWS_S3_BUCKET: test
      AWS_S3_BUCKET_REGION: test
      AWS_S3_SIGNED_URL_EXPIRATION: 0
      MAIL_API: test
      MAIL_DOMAIN: test
      MAIL_FROM_EMAIL: test
      CHAT_SUBSCRIBE_KEY: test
      CHAT_PUBLISH_KEY: test
      CHAT_SECRET_KEY: test
      BOX_CLIENT_ID: test
      BOX_CLIENT_SECRET: test
      BOX_PUBLIC_KEY_ID: test
      BOX_PRIVATE_KEY: test
      BOX_PASSPHRASE: 0
      BOX_ENTERPRISE_ID: TEST
      GOOGLE_PLACE_API_KEY: test
      DROMO_FRONTEND_LICENCE_KEY: test
      DROMO_BACKEND_LICENCE_KEY: test
      DB_CONNECTION_STRING: mongodb://localhost:27017/makula-test-db
      QR_APP_URI: http://localhost:8008
      DEEPL_AUTH_KEY: ${{ secrets.DEEPL_AUTH_KEY }}

      MONGOMS_VERSION: 7.3.4
      MONGOMS_DOWNLOAD_URL: https://fastdl.mongodb.org/linux/mongodb-linux-x86_64-ubuntu2004-7.3.4.tgz

    runs-on: ubuntu-latest

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: "backend/.nvmrc"
          cache: "yarn"
          cache-dependency-path: "backend/yarn.lock"

      # Enhanced caching for build artifacts and binaries
      - name: Cache node modules and binaries
        uses: actions/cache@v4
        with:
          path: |
            backend/node_modules
            ~/.cache/yarn
            ~/.cache/mongodb-memory-server
            ~/.cache/puppeteer
            backend/node_modules/.cache
          key: ${{ runner.os }}-deps-${{ hashFiles('backend/yarn.lock') }}-${{ hashFiles('backend/.nvmrc') }}
          restore-keys: |
            ${{ runner.os }}-deps-${{ hashFiles('backend/yarn.lock') }}-
            ${{ runner.os }}-deps-

      - name: Install libcrypto1.1 (cached)
        uses: actions/cache@v4
        id: libcrypto-cache
        with:
          path: /tmp/libssl1.1_1.1.1f-1ubuntu2_amd64.deb
          key: libssl-1.1.1f-ubuntu

      - name: Download and install libcrypto1.1
        if: steps.libcrypto-cache.outputs.cache-hit != 'true'
        run: |
          wget http://archive.ubuntu.com/ubuntu/pool/main/o/openssl/libssl1.1_1.1.1f-1ubuntu2_amd64.deb
          sudo dpkg -i libssl1.1_1.1.1f-1ubuntu2_amd64.deb
        working-directory: /tmp

      - name: Install libcrypto1.1 (from cache)
        if: steps.libcrypto-cache.outputs.cache-hit == 'true'
        run: sudo dpkg -i /tmp/libssl1.1_1.1.1f-1ubuntu2_amd64.deb

      - name: Install dependencies
        run: |
          yarn install --immutable --inline-builds

      - name: Run code quality checks
        run: |
          # Type Check
          yarn type-check

          # Circular imports check
          yarn circular-imports --check

          # Translations check
          yarn fix-translations
          if [[ -n $(git status --porcelain) ]]; then
            echo "❌ Run 'yarn fix-translations' first!"
            exit 1
          fi

          # GraphQL types check  
          yarn graphql-codegen
          if [[ -n $(git status --porcelain) ]]; then
            echo "❌ Run 'yarn graphql-codegen' first!"
            exit 1
          fi

      - name: Execute tests
        run: |
          MONGOMS_VERSION=7.3.4 yarn add mongodb-memory-server@9.4.0
          yarn test:validate-env          
          yarn test

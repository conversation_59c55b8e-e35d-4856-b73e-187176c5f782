{"name": "process-3d-zip-file", "version": "1.0.0", "main": "index.mjs", "scripts": {"cleanup": "rm -rf dist && rm -f dist.zip", "zip": "cd dist && zip -r dist.zip * && mv dist.zip ../dist.zip", "build": "npm run cleanup && mkdir dist && cp *.mjs ./dist/ && npm run zip", "build:module-layer": "mkdir nodejs && cp package*.json ./nodejs && cd nodejs && npm i --omit=dev && cd .. && zip -r nodejs.zip nodejs && rm -rf nodejs"}, "author": "", "license": "ISC", "description": "", "dependencies": {"@aws-sdk/lib-storage": "^3.850.0", "aws-sdk": "2.1428.0", "pubnub": "7.2.0", "unzipper": "^0.12.3", "uuid-by-string": "^4.0.0"}}
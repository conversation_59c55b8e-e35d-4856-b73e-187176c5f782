import { GetObjectCommand, S3Client } from "@aws-sdk/client-s3";

import unzipper from "unzipper";
import { Upload } from "@aws-sdk/lib-storage";
import PubNub from "pubnub";
import uuidFromSeed from "uuid-by-string";

const client = new S3Client({
  region: process.env.AWS_S3_BUCKET_REGION || "eu-north-1",
  credentials: {
    accessKeyId: process.env.AWS_S3_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_S3_SECRET_ACCESS_KEY,
  },
});

const uuid = uuidFromSeed(process.env.NAMESPACE);
const pubnub = new PubNub({
  subscribeKey: process.env.CHAT_SUBSCRIBE_KEY,
  publishKey: process.env.CHAT_PUBLISH_KEY,
  uuid,
  secretKey: process.env.CHAT_SECRET_KEY,
});

const getChannelId = (id) => `oem-channel-org-${uuidFromSeed(id?.toString())}`;

export const handler = async (event) => {
  console.log("process-3d-zip-file started");
  // Initializing variables
  const record = event.Records[0];
  const bucket = record.s3.bucket.name;
  const key = record.s3.object.key;

  console.log("file", key);

  const parts = key.split("/");
  const oemId = parts[2];
  console.log("oemId", oemId);
  parts.pop();
  const path = parts.join("/");

  let file = null;
  let readableStream = null;
  let zip = null;

  // Getting S3 file
  try {
    const getCommand = new GetObjectCommand({
      Bucket: bucket,
      Key: key,
    });

    file = await client.send(getCommand);

    readableStream = file.Body;
  } catch (e) {
    await informOrganizationOf3dZipFileUpdate({
      oemId,
      path: key,
      success: false,
      stackTrace: e.toString(),
    });
    console.log(e);
  }

  // Creating a Readable Stream from the Zipfile and parsing it.
  zip = readableStream?.pipe(unzipper.Parse({ forceStream: true }));

  if (zip?.readable == true) {
    // Looping over ALL files in the ZIP to extract them one by one and upload them afterwards to S3
    for await (const e of zip) {
      const entry = e;
      const filePath = `${path}/${entry.path}`;
      const type = entry.type;

      console.log("Uploading file: ", filePath);

      if (
        type === "File" &&
        !entry.path.startsWith("__MACOSX") &&
        isValidFile(filePath)
      ) {
        let readableStream = entry;
        const uploadParams = {
          Bucket: bucket,
          Key: filePath.normalize("NFC"),
          Body: readableStream,
        };
        try {
          // Here we use the AWS SDK for lib-storage Upload operation
          const parallelUploads3 = new Upload({
            client: client,
            queueSize: 6,
            leavePartsOnError: false,
            params: uploadParams,
          });

          parallelUploads3.on("httpUploadProgress", (progress) => {});

          await parallelUploads3.done();
        } catch (e) {
          await informOrganizationOf3dZipFileUpdate({
            oemId,
            path: key,
            success: false,
            stackTrace: e.toString(),
          });
          console.log(e);
        }
      } else {
        entry.autodrain();
      }
    }
    console.log("Upload complete");
    await informOrganizationOf3dZipFileUpdate({
      oemId,
      path: key,
      success: true,
    });
  }
};

const informOrganizationOf3dZipFileUpdate = async ({
  oemId,
  path,
  success,
  stackTrace,
}) => {
  const data = {
    message: {
      text: "process3dZipFile",
      success: success,
      payload: {
        path: path,
        errorMessage: success ? "" : "Failed to process 3D zip file.",
        stackTrace: success ? "" : stackTrace,
      },
    },
  };

  const channel = getChannelId(oemId.toString());

  try {
    const response = await pubnub.publish({ ...data, channel: channel });

    console.log("PubNub response", response);
  } catch (error) {
    console.log("PubNub error", error);
  }
};

const isValidFile = (path) => {
  if (!path) return false;

  const fileName = path.split("/").pop();
  if (!fileName) return false;

  const parts = fileName.split(".");

  return (
    parts.length >= 2 &&
    parts[0].length > 0 &&
    parts[parts.length - 1].length > 0
  );
};

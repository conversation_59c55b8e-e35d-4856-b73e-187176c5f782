if templateType === "procedure"
  if sections["FACILITY_INFORMATION"]
    section.section
      div.info
        each value, label in sections["FACILITY_INFORMATION"]
          div.procedure-info-row
            img(src=value.icon)
            p
              span= `${label}:`
              != ` ${value.text}`

  if sections["PROCEDURE_INFORMATION"]
    section.section
      div
        h1.info-name-heading= data.name
        each value, label in sections["PROCEDURE_INFORMATION"]
          p
            span= `${label}:`
            != ` ${value}`
        if data.description
          p= data.description


if templateType === "workOrder"
  section.section
    h1.info-name-h1= data.title
    if sections["WORK_ORDER_INFORMATION"]
      div.info
        h2.info-name-h2 General Information
        each value, label in sections["WORK_ORDER_INFORMATION"]
          p
            span= `${label}:`
            != ` ${value}`

import { roles } from "~/directives";
import { buildQueryParams, getEnums } from "~/utils";
import BoardFor from "$/settings/enums/kanban/_boardFor.json";
import BoardTypes from "$/settings/enums/kanban/_type.json";
import { prepareFilterQuery } from "~/utils/customers";
import {
  getAllCustomers,
  getCustomer,
  listOemCustomersInArea,
  getSharedOrganizationDetails as getSharedOrganizationDetailsService,
  getConnectionHistory,
} from "~/services/customer/fetch";

const boardForEnum = getEnums(BoardFor, "reference");
const boardTypesEnum = getEnums(BoardTypes, "reference");

export const queryTypes = `#graphql
  type Query {
    listAllOwnOemCustomers(params: InputQueryParams): PaginatedCustomers @${roles.is.technician}
    listOwnOemKanbanCustomers(params: InputQueryParams): PaginatedKanbanCustomers @${roles.is.technician}
    getOwnOemCustomerById(id: ID!): Customer @${roles.is.technician}
    listOwnOemCustomersMissingClientIds(clientIds: [String!]!): [String] @${roles.is.technician}
    listAllOemCustomersInArea(params: InputQueryParams): [CustomerMarker] @${roles.is.technician}
    getSharedOrganizationDetails(connectionId: ID!): SharedOrganizationDetails @${roles.is.technician}
    listOwnOemConnectionHistory(params: InputQueryParams): PaginatedConnectionHistory @${roles.is.gmu}
  }
`;

export const queryResolvers = {
  Query: {
    listAllOwnOemCustomers: async (_, args, { dataSources, user }) =>
      await getAllCustomers({ args, dataSources, user }),
    listOwnOemKanbanCustomers: async (_, args, { dataSources, user }) => {
      const sort = args.params?.sort;

      const { filterQuery, query, kanbanBoard } = await prepareFilterQuery({
        args,
        user,
        dataSources,
      });
      const queryPayload = {
        ...(filterQuery?.length ? { $and: filterQuery } : {}),
        ...buildQueryParams(query),
        oem: user.organization,
        sort: sort || ["updated_at:des"],
      };

      const singleSelectCustomField = await dataSources.CustomAdditionalField.getOne({
        _id: kanbanBoard,
        oem: user.organization,
        fieldType: "singleSelect",
        deleted: false,
      });

      if (
        !singleSelectCustomField ||
        !singleSelectCustomField.options ||
        !singleSelectCustomField.options.length
      ) {
        return {
          totalCount: 0,
          columns: [],
        };
      }

      const kanbanColumnQueries = [];
      const columns = [];
      let totalCustomers = 0;
      let index = 0;

      singleSelectCustomField.options.forEach(column => {
        const q = {
          ...queryPayload,
          $and: [
            ...(queryPayload.$and ? queryPayload.$and : []),
            {
              customFields: {
                $elemMatch: {
                  fieldId: kanbanBoard,
                  value: column.value,
                },
              },
            },
          ],
        };
        kanbanColumnQueries.push(dataSources.Customer.getMany(q));
        kanbanColumnQueries.push(
          dataSources.Customer.totalCount({
            ...q,
            limit: -1,
            skip: 0,
          }),
        );
      });

      const kanbanColumnResults = await Promise.all(kanbanColumnQueries);

      for (let i = 0; i < kanbanColumnResults.length; i += 1) {
        // count result is at alternate indexes
        if (i % 2 === 1) {
          totalCustomers += kanbanColumnResults[i];
        } else {
          const column = singleSelectCustomField.options[i / 2];
          columns.push({
            _id: index + 1,
            boardFor: boardForEnum.facility,
            type: boardTypesEnum.custom_field,
            customField: kanbanBoard,
            column: column.value,
            columnIndex: index,
            cards: kanbanColumnResults[i],
            totalCount: kanbanColumnResults[i + 1],
            limit: args.params.limit,
            skip: args.params.skip,
            currentPage: args.params.skip / args.params.limit + 1,
          });
          index += 1;
        }
      }

      return {
        totalCount: totalCustomers,
        columns,
      };
    },
    getOwnOemCustomerById: (_, args, { dataSources, user }) =>
      getCustomer({ args: { input: { id: args.id } }, dataSources, user }),
    listAllOemCustomersInArea: (_, args, { dataSources, user }) =>
      listOemCustomersInArea({ args, dataSources, user }),
    listOwnOemCustomersMissingClientIds: async (_, args, { dataSources, user }) => {
      const { clientIds = [] } = args || {};
      if (!clientIds?.length) return null;
      const existings = await dataSources.Customer.getMany({
        facilityIdentifier: { $in: clientIds.map(i => i.trim()) },
        oem: user.organization,
      });
      const existingsIds = existings?.map?.(d => d.facilityIdentifier) ?? [];
      return new Set(clientIds.filter(cid => !existingsIds.includes(cid)));
    },
    getSharedOrganizationDetails: (_, args, { dataSources, user, req: { t: translate } }) =>
      getSharedOrganizationDetailsService({
        args: { input: { connectionId: args.connectionId } },
        dataSources,
        user,
        translate,
      }),
    listOwnOemConnectionHistory: async (_, args, { dataSources, user }) =>
      getConnectionHistory({
        args,
        dataSources,
        user,
      }),
  },
};

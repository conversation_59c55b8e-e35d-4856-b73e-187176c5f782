// eslint-disable-next-line import/no-extraneous-dependencies
import { describe, expect, test, vi } from "vitest";
import { queryResolvers } from "./_query";

const { Query } = queryResolvers;

const OEM_ID = "6499a52b5dfe76648ff67a81";
const kanbanBoard = "64a7a9b88d1ef7e5ec5176e6";
const user = {
  organization: OEM_ID,
};

describe("Query Customer", () => {
  let dataSources;
  beforeEach(() => {
    dataSources = {
      Customer: {
        getMany: vi.fn(),
        totalCount: vi.fn(),
      },
      CustomAdditionalField: {
        getOne: vi.fn(),
      },
    };
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe("listOwnOemKanbanCustomers", async () => {
    const sort = ["updated_at:des"];
    const totalLimitAndSkip = {
      limit: -1,
      skip: 0,
    };

    test("empty data when single select not found ", async () => {
      const { totalCount, columns } = await Query.listOwnOemKanbanCustomers(
        null,
        {
          params: {
            where: {
              kanbanBoard,
            },
            limit: 4,
          },
        },
        {
          dataSources,
          user,
        },
      );

      expect(totalCount).toBe(0);
      expect(columns.length).toBe(0);
    });

    test("get all customers in columns ", async () => {
      dataSources.CustomAdditionalField.getOne.mockResolvedValueOnce({
        options: [
          {
            value: "Yes",
          },
          {
            value: "No",
          },
          {
            value: "Maybe",
          },
        ],
      });
      dataSources.Customer.getMany.mockResolvedValueOnce([
        {
          _id: "6499a52b5dfe76648ff67a81",
          name: "customer 1",
          customFields: [
            {
              fieldId: "64a7a9b88d1ef7e5ec5176e6",
              value: "Yes",
            },
          ],
        },
        {
          _id: "6499a52b5dfe76648ff67a82",
          name: "customer 2",
          customFields: [
            {
              fieldId: "64a7a9b88d1ef7e5ec5176e6",
              value: "Yes",
            },
          ],
        },
      ]);
      dataSources.Customer.getMany.mockResolvedValueOnce([
        {
          _id: "6499a52b5dfe76648ff67a83",
          name: "customer 3",
          customFields: [
            {
              fieldId: "64a7a9b88d1ef7e5ec5176e6",
              value: "No",
            },
          ],
        },
        {
          _id: "6499a52b5dfe76648ff67a86",
          name: "customer 6",
          customFields: [
            {
              fieldId: "64a7a9b88d1ef7e5ec5176e6",
              value: "No",
            },
          ],
        },
      ]);
      dataSources.Customer.getMany.mockResolvedValueOnce([
        {
          _id: "6499a52b5dfe76648ff67a84",
          name: "customer 4",
          customFields: [
            {
              fieldId: "64a7a9b88d1ef7e5ec5176e6",
              value: "Maybe",
            },
          ],
        },
        {
          _id: "6499a52b5dfe76648ff67a85",
          name: "customer 5",
          customFields: [
            {
              fieldId: "64a7a9b88d1ef7e5ec5176e6",
              value: "Maybe",
            },
          ],
        },
        {
          _id: "6499a52b5dfe76648ff67a86",
          name: "customer 7",
          customFields: [
            {
              fieldId: "64a7a9b88d1ef7e5ec5176e6",
              value: "Maybe",
            },
          ],
        },
      ]);
      dataSources.Customer.totalCount.mockResolvedValueOnce(2);
      dataSources.Customer.totalCount.mockResolvedValueOnce(2);
      dataSources.Customer.totalCount.mockResolvedValueOnce(3);
      const { totalCount, columns } = await Query.listOwnOemKanbanCustomers(
        null,
        {
          params: {
            where: {
              kanbanBoard,
            },
            limit: 4,
          },
        },
        {
          dataSources,
          user,
        },
      );
      const query = {
        oem: user.organization,
      };

      expect(totalCount).toBe(7);
      expect(columns.length).toBe(3);

      expect(columns[0]._id).toBe(1);
      expect(columns[0].totalCount).toBe(2);

      expect(columns[1]._id).toBe(2);
      expect(columns[1].totalCount).toBe(2);

      expect(columns[2]._id).toBe(3);
      expect(columns[2].totalCount).toBe(3);

      expect(dataSources.CustomAdditionalField.getOne).toHaveBeenCalledOnce();
      expect(dataSources.CustomAdditionalField.getOne).toHaveBeenCalledWith({
        _id: kanbanBoard,
        oem: user.organization,
        fieldType: "singleSelect",
        deleted: false,
      });
      expect(dataSources.Customer.getMany).toHaveBeenCalledTimes(3);
      expect(dataSources.Customer.getMany).toHaveBeenCalledWith(
        expect.objectContaining({
          ...query,
          sort,
        }),
      );
      expect(dataSources.Customer.totalCount).toHaveBeenCalledTimes(3);
      expect(dataSources.Customer.totalCount).toHaveBeenCalledWith(
        expect.objectContaining({
          ...query,
          ...totalLimitAndSkip,
        }),
      );
    });
  });
});

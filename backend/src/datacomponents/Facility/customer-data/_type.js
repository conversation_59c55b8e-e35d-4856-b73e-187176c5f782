import ROUTES_SETTINGS from "$/settings/routes-resolvers.json";
import { EnumRoles } from "~/graphql/types";

export const types = `#graphql
  type CustomerAddress {    
    coordinates: [Float]
  }

  type AssetAccess {
    documentation: Boolean
    subAssets: Boolean
    parts: Boolean
    preventiveMaintenance: Boolean
    _3dModel: Boolean
    qrCodes: Boolean
    history: Boolean
  }

  type SharedOrganizationDetails {
    assetAccess: AssetAccess
    oem: Oem
  }

  type ConnectionHistory {
    _id: ID
    machine: ID
    type: String
    resourceId: ID,
    resource: MJSON
    customer: Customer
    ticket: Ticket
    createdAt: DateTime  
    year: Int
    month: Int
    updatedBy: User
    createdBy: User
  }

  type PaginatedConnectionHistory {
    totalCount: Int
    limit: Int
    skip: Int
    currentPage: Int
    machineCount: Int
    history: [MachineHistory]
  }

  type Customer {
    _id: ID
    name: String
    oem: Oem
    machines: [Asset]
    urlOemFacility: String
    description: String
    totalMachines: Int
    totalUsersWithAccess: Int
    totalUsers: Int
    totalOpenTickets: Int
    isQRCodeEnabled: Boolean
    qrCodeAccess: String
    isMachineDocumentationEnabled: Boolean
    isPreventiveMaintenanceEventsEnabled: Boolean
    generalAccessUrl: String
    facilityId: String
    facilityIdentifier: String
    customFields: [CustomField]
    createdAt: DateTime
    updatedAt: DateTime
    type: String
    teams: [Team]
    numberOfTeams: Int
    customerAddress: CustomerAddress
    createdBy: ID
    linkedOrg: ID
    assetAccess: AssetAccess
    hasCustomerPortal: Boolean
  }

  type CustomerMarker {
    _id: ID!      
    customerAddress: CustomerAddress
  }

  type PaginatedCustomers {
    totalCount: Int
    limit: Int
    skip: Int
    currentPage: Int
    customers: [Customer]
  }

  type KanbanColumnCustomer {
    _id: Int
    boardFor: String
    type: String
    customField: ID
    column: String
    columnIndex: Int
    cards: [Customer]
    totalCount: Int
    limit: Int
    skip: Int
    currentPage: Int
  }
  
  type PaginatedKanbanCustomers {
    columns: [KanbanColumnCustomer],
    totalCount: Int
  }
`;

const {
  CLIENT: {
    ROUTES: { FACILITY_LOGIN },
  },
} = ROUTES_SETTINGS;

const facilityAppUrl = process.env.FACILITY_APP_URI;
const routes = {
  facility: FACILITY_LOGIN.replace("/", "en/"),
};

export const typeResolvers = {
  Customer: {
    oem: ({ oem }, _, { dataSources }) => dataSources.Oem.loadOne(oem),
    urlOemFacility: async ({ oem }, _, { dataSources }) => {
      const oemData = await dataSources.Oem.loadOne(oem);
      const urlOem = oemData?.urlOem;
      return `${facilityAppUrl}/${routes.facility}/${urlOem}`;
    },
    machines: ({ _id }, _, { dataSources }) =>
      dataSources.Machine.loadManyByQuery({
        customer: _id,
      }),
    // TODO: Denormalize totalMachines and totalOpenTickets
    totalMachines: ({ _id }, __, { dataSources }) =>
      dataSources.Machine.countDocuments({
        customer: _id,
      }),
    totalOpenTickets: async ({ _id, oem }, __, { dataSources }) => {
      const oemData = await dataSources.Oem.loadOne(oem);
      const closedStatusId = oemData?.statuses?.[oemData.statuses.length - 1]?._id;
      return dataSources.Ticket.countDocuments({
        facility: _id,
        status: { $ne: closedStatusId },
      });
    },
    totalUsersWithAccess: async ({ _id, oem }, __, { dataSources }) =>
      dataSources.User.countDocuments({
        facility: _id,
        oem,
        role: EnumRoles.User,
        access: true,
      }),
    totalUsers: async ({ _id, oem }, __, { dataSources }) =>
      dataSources.User.countDocuments({
        facility: _id,
        oem,
        role: EnumRoles.User,
      }),
    isQRCodeEnabled: () => false,
    generalAccessUrl: async ({ _id, oem }, _, { dataSources }) => {
      const oemData = await dataSources.Oem.loadOne(oem);
      const urlOem = oemData?.urlOem;
      return `${facilityAppUrl}/${routes.facility}/${urlOem}/${_id}`;
    },
    facilityId: async ({ facilityIdentifier }) => facilityIdentifier,
    description: ({ description }) => description || "",
    // eslint-disable-next-line camelcase
    createdAt: async ({ created_at }) => created_at,
    // eslint-disable-next-line camelcase
    updatedAt: async ({ updated_at }) => updated_at,
    teams: async ({ teams, oem }, __, { dataSources }) => {
      const customerTeams = await dataSources.Team.loadManyByQuery({
        _id: { $in: teams || [] },
        oem,
      });
      return customerTeams;
    },
    numberOfTeams: ({ teams }) => teams?.length || 0,
    hasCustomerPortal: async ({ _id }, __, { dataSources }) =>
      !!(await dataSources.CustomerPortal.getOne({ connection: _id })),
  },
};

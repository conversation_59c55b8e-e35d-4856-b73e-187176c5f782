import { Types } from "mongoose";
import { DEFAULT_DOCUMENT_LANGUAGE_OPTION } from "~/constants/document";
import type { IDocument } from "~/datamodels/Document/interface";
import type { IDocumentChunk } from "~/datamodels/DocumentChunk/interface";
import type { IDocumentPage } from "~/datamodels/DocumentPage/interface";
import type { IContext } from "~/types/common";

export default `#graphql
  type Author {
    name: String
    confidence: String
  }

  type Document {
    _id: ID!
    authors: [Author]
    customFields: [CustomField]
    contributors: [Author]
    editors: [Author]
    language: String
    boxDocumentId: String
    ocrGeneratedContent: String
    documentChunks: [DocumentChunk]
    documentPages: [DocumentPage]
    aiGeneratedTitle: String
    title: String
    size: Int
    createdBy: User
    updatedBy: User
    status: Int
    date: String
    results: [DocumentChunk]
  }

  type DocumentTranslation {
    language: String
    content: String
    status: Int
  }

  type DocumentPage {
    _id: ID!
    content: String
    translations: [DocumentTranslation]
    documentId: ID
    pageIndex: Int
  }

  type DocumentChunk {
    _id: ID!
    boxDocumentId: String
    content: String
    chunkIndex: Int
    distance: Float
    documentCustomFields: [CustomField]
    documentId: ID
    documentTitle: String
    documentAITitle: String
    documentDate: String
    ocrGeneratedContent: String
    pageContent: String
    pageNumber: Int
    documentPage: DocumentPage
  }

  type PaginatedDocuments {
    totalCount: Int
    limit: Int
    skip: Int
    currentPage: Int
    documents: [Document]
  }

  type QueryResponse {
    groupedResults: [Document]
    jobId: String
  }

  type Citation {
    id: String
    source: String
    highlight: String
    keywords: [String]
    pageNumber: Int
  }

  type DocumentAnswers {
    answer: String
    documentId: String
    citations: [Citation]
    chunkID: String
    highlight: String
    keywords: [String]
    pageNumber: Int
  }
`;

export const typeResolvers = {
  Document: {
    createdBy: (
      { createdBy }: IDocument,
      _: unknown,
      { dataSources }: { dataSources: IContext["dataSources"] },
    ) => dataSources.User.loadOne(new Types.ObjectId(createdBy)),
    documentChunks: async (
      { _id }: IDocument,
      _: unknown,
      { dataSources }: { dataSources: IContext["dataSources"] },
    ) => {
      const documentChunks = await dataSources.DocumentChunk.getMany({
        documentId: _id,
      });
      return documentChunks;
    },
    documentPages: async (
      { _id }: IDocument,
      _: unknown,
      {
        dataSources,
        req,
      }: {
        dataSources: IContext["dataSources"];
        req: { body: { variables: { pages: number[]; language: string } } };
      },
    ) => {
      const getSpecificPages = !!req.body?.variables?.pages;
      const language = req.body?.variables?.language || DEFAULT_DOCUMENT_LANGUAGE_OPTION;
      const documentPages = await dataSources.DocumentPage.getMany({
        sort: ["pageIndex:asc"],
        documentId: _id,
        ...(getSpecificPages ? { pageIndex: { $in: req.body.variables.pages } } : {}),
      });

      return documentPages.map((page: IDocumentPage) => {
        if (language === DEFAULT_DOCUMENT_LANGUAGE_OPTION) {
          return {
            content: page.content,
            documentId: page.documentId,
            pageIndex: page.pageIndex,
            _id: page._id,
          };
        }
        return {
          documentId: page.documentId,
          pageIndex: page.pageIndex,
          _id: page._id,
          translations: (page.translations = (page.translations || []).filter(
            translation => translation.language === language,
          )),
        };
      });
    },
    updatedBy: (
      { updatedBy }: IDocument,
      _: unknown,
      { dataSources }: { dataSources: IContext["dataSources"] },
    ) => dataSources.User.loadOne(new Types.ObjectId(updatedBy)),
  },

  DocumentChunk: {
    boxDocumentId: async (
      { documentId }: IDocumentChunk,
      _: unknown,
      { dataSources }: { dataSources: IContext["dataSources"] },
    ) => {
      const document: IDocument = await dataSources.Document.loadOne(documentId);
      return document.boxDocumentId;
    },
    content: ({ content }: IDocumentChunk) => content,
    documentCustomFields: async (
      { documentId }: IDocumentChunk,
      _: unknown,
      { dataSources }: { dataSources: IContext["dataSources"] },
    ) => {
      const document: IDocument = await dataSources.Document.loadOne(documentId);
      return document.customFields;
    },
    documentPage: async (
      { documentId, pageNumber }: IDocumentChunk,
      _: unknown,
      { dataSources }: { dataSources: IContext["dataSources"] },
    ) => {
      const documentPage: IDocumentPage = await dataSources.DocumentPage.getOne({
        documentId,
        pageIndex: pageNumber,
      });
      return documentPage;
    },
    ocrGeneratedContent: async (
      { documentId }: IDocumentChunk,
      _: unknown,
      { dataSources }: { dataSources: IContext["dataSources"] },
    ) => {
      const document: IDocument = await dataSources.Document.loadOne(documentId);
      return document?.ocrGeneratedContent || "";
    },
    documentTitle: async (
      { documentId }: IDocumentChunk,
      _: unknown,
      { dataSources }: { dataSources: IContext["dataSources"] },
    ) => {
      const document: IDocument = await dataSources.Document.loadOne(documentId);
      return document.title;
    },
    documentAITitle: async (
      { documentId }: IDocumentChunk,
      _: unknown,
      { dataSources }: { dataSources: IContext["dataSources"] },
    ) => {
      const document: IDocument = await dataSources.Document.loadOne(documentId);
      return document.aiGeneratedTitle;
    },
    documentDate: async (
      { documentId }: IDocumentChunk,
      _: unknown,
      { dataSources }: { dataSources: IContext["dataSources"] },
    ) => {
      const document: IDocument = await dataSources.Document.loadOne(documentId);
      return document.date;
    },
  },
};

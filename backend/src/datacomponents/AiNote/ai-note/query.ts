import { GraphQLResolveInfo } from "graphql";
import type { ExecutionContext } from "graphql/execution/execute";

import { roles, features } from "~/directives";
import { InputQueryParams } from "~/graphql/types";
import { IContext } from "~/types/common";
import { getOwnAiNote, listOwnAiNotes } from "~/services/aiNote/fetch";

const { technician } = roles.is;
const { aiNotetaker } = features.oemOwns;

export const queryTypes = `#graphql
  type Query {
    getOwnAiNote(id: ID!): AiNote
    listOwnAiNotes(params: InputQueryParams): PaginatedAiNotes @${aiNotetaker} @${technician}
  }
`;

export const queryResolvers = {
  Query: {
    getOwnAiNote: async (
      _: ExecutionContext["contextValue"],
      args: { id: string },
      { dataSources }: IContext,
      __: GraphQLResolveInfo,
    ) =>
      // @ts-ignore
      await getOwnAiNote({ args, dataSources }),

    listOwnAiNotes: async (
      _: ExecutionContext["contextValue"],
      args: { params: InputQueryParams },
      { dataSources, user }: IContext,
      __: GraphQLResolveInfo,
    ) =>
      // @ts-ignore
      await listOwnAiNotes({ args, dataSources, user }),
  },
};

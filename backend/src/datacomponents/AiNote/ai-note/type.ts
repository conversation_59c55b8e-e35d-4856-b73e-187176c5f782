import { IAiNote } from "~/datamodels/AiNote/interface";
import { IMachine } from "~/datamodels/Machine/interface";
import { IContext } from "~/types/common";

export const types = `#graphql
  type PublishedMachine {
    machine: Asset
    machineHistoryNoteId: ID
    publishedAt: DateTime
    publishedBy: ID
  }

  type TranscriptionError {
    code: Int
    message: String
  }

  type AiNote {
    _id: ID
    audioUrl: String
    createdAt: DateTime
    createdBy: User
    languageCode: String
    oem: ID
    publishedMachines: [PublishedMachine]
    summary: String
    title: String
    transcript: String
    transcriptionStatus: String
    transcriptionError: TranscriptionError
    updatedBy: ID
  }

  type PaginatedAiNotes {
    totalCount: Int
    limit: Int
    skip: Int
    currentPage: Int
    aiNotes: [AiNote]
  }

  type TranscriptionResult {
    summary: String
    title: String
    transcript: String
  }
`;

export const typeResolvers = {
  AiNote: {
    // eslint-disable-next-line camelcase
    createdAt: async ({ created_at }: IAiNote) => created_at,
    createdBy: ({ createdBy }: IAiNote, _: any, { dataSources }: IContext) =>
      dataSources.User.loadOne(createdBy),
    publishedMachines: async (
      { publishedMachines }: IAiNote,
      _: any,
      { dataSources, user }: IContext,
    ) => {
      if (!publishedMachines.length) return [];

      const machines: Array<IMachine> = await dataSources.Machine.getMany(
        {
          _id: { $in: publishedMachines.map(({ machine }) => machine) },
          oem: user.organization,
        },
        { _id: 1, image: 1, name: 1, serialNumber: 1 },
      );

      const machineMap: { [key: string]: IMachine } = {};
      machines.forEach(machine => {
        machineMap[machine._id.toString()] = machine;
      });

      return publishedMachines.map(
        async ({ machine, machineHistoryNoteId, publishedAt, publishedBy }) => {
          const machineData = machineMap[machine._id.toString()];

          return {
            machine: machineData,
            machineHistoryNoteId,
            publishedAt: publishedAt ? new Date(publishedAt).toISOString() : null,
            publishedBy,
          };
        },
      );
    },
  },
};

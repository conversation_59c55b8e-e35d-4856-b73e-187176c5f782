import { typeResolvers, types } from "~/datacomponents/AiNote/ai-note/type";
import inputTypes from "~/datacomponents/AiNote/ai-note/input";
import { mutationResolvers, mutationTypes } from "~/datacomponents/AiNote/ai-note/mutation";
import { queryResolvers, queryTypes } from "~/datacomponents/AiNote/ai-note/query";

export default {
  types: `
    ${inputTypes}
    ${mutationTypes}
    ${queryTypes}
    ${types}
  `,
  resolvers: Object.assign(mutationResolvers, queryResolvers, typeResolvers),
};

import { GraphQLResolveInfo } from "graphql";
import { ExecutionContext } from "graphql/execution/execute";
import { roles, features } from "~/directives";
import {
  InputCreateAiNote,
  InputPublishAiNote,
  InputTranscribeAiNote,
  InputUpdateAiNoteTitle,
  InputUpdateAiNoteSummary,
  InputTranslateAiNoteSummary,
} from "~/graphql/types";
import { IContext } from "~/types/common";
import { createAiNote } from "~/services/aiNote/create";
import { deleteAiNote } from "~/services/aiNote/delete";
import { updateAiNoteSummary, updateAiNoteTitle } from "~/services/aiNote/update";
import {
  detectLanguage,
  publishAiNote,
  transcribeAiNote,
  translateSummary,
} from "~/services/aiNote/utils";

const { technician } = roles.is;
const { aiNotetaker } = features.oemOwns;

export const mutationTypes = `#graphql
  type Mutation {
    createAiNote(input: InputCreateAiNote!): AiNote @${aiNotetaker} @${technician}
    deleteAiNote(id: ID!): String @${aiNotetaker} @${technician}
    detectLanguage(audioUrl: String!): String @${aiNotetaker} @${technician}
    publishAiNote(input: InputPublishAiNote!): AiNote @${aiNotetaker} @${technician}
    transcribeAiNote(input: InputTranscribeAiNote!): TranscriptionResult @${aiNotetaker} @${technician}
    translateAiNoteSummary(input: InputTranslateAiNoteSummary!): String @${aiNotetaker} @${technician}
    updateAiNoteSummary(input: InputUpdateAiNoteSummary!): AiNote @${aiNotetaker} @${technician}
    updateAiNoteTitle(input: InputUpdateAiNoteTitle!): AiNote @${aiNotetaker} @${technician}
  }
`;

export const mutationResolvers = {
  Mutation: {
    createAiNote: async (
      _: ExecutionContext["contextValue"],
      args: { input: InputCreateAiNote },
      { dataSources, user }: IContext,
      __: GraphQLResolveInfo,
    ) =>
      // @ts-ignore
      await createAiNote({ args, dataSources, user }),

    deleteAiNote: async (
      _: ExecutionContext["contextValue"],
      args: { id: string },
      { dataSources, user }: IContext,
      __: GraphQLResolveInfo,
    ) =>
      // @ts-ignore
      await deleteAiNote({ args, dataSources, user }),

    detectLanguage: async (
      _: ExecutionContext["contextValue"],
      args: { audioUrl: string },
      { dataSources, user }: IContext,
      __: GraphQLResolveInfo,
    ) =>
      // @ts-ignore
      await detectLanguage({ args, dataSources, user }),

    publishAiNote: async (
      _: ExecutionContext["contextValue"],
      args: { input: InputPublishAiNote },
      { dataSources, user }: IContext,
      __: GraphQLResolveInfo,
    ) =>
      // @ts-ignore
      await publishAiNote({ args, dataSources, user }),

    transcribeAiNote: async (
      _: ExecutionContext["contextValue"],
      args: { input: InputTranscribeAiNote },
      { dataSources, user }: IContext,
      __: GraphQLResolveInfo,
    ) => {
      // @ts-ignore
      transcribeAiNote({ args, dataSources, user });

      return "transcribing";
    },

    translateAiNoteSummary: async (
      _: ExecutionContext["contextValue"],
      args: { input: InputTranslateAiNoteSummary },
      { dataSources, req: { t: translate }, user }: IContext,
      __: GraphQLResolveInfo,
    ) =>
      // @ts-ignore
      await translateSummary({ args, dataSources, translate, user }),

    updateAiNoteSummary: async (
      _: ExecutionContext["contextValue"],
      args: { input: InputUpdateAiNoteSummary },
      { dataSources, user }: IContext,
      __: GraphQLResolveInfo,
    ) =>
      // @ts-ignore
      await updateAiNoteSummary({ args, dataSources, user }),

    updateAiNoteTitle: async (
      _: ExecutionContext["contextValue"],
      args: { input: InputUpdateAiNoteTitle },
      { dataSources, user }: IContext,
      __: GraphQLResolveInfo, // @ts-ignore
    ) => await updateAiNoteTitle({ args, dataSources, user }),
  },
};

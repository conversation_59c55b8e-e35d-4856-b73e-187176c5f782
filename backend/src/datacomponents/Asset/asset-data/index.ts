import { typeResolvers, types } from "~/datacomponents/Asset/asset-data/type";
import { queryResolvers, queryTypes } from "~/datacomponents/Asset/asset-data/query";
import inputTypes from "~/datacomponents/Asset/asset-data/input";
import { mutationResolvers, mutationTypes } from "~/datacomponents/Asset/asset-data/mutation";

export default {
  types: `
    ${types}
    ${queryTypes}
    ${inputTypes}
    ${mutationTypes}
  `,
  resolvers: Object.assign(queryResolvers, mutationResolvers, typeResolvers),
};

import createUuidFromSeed from "uuid-by-string";
import { Types } from "mongoose";
import { roles } from "~/directives";
import { saveDocumentationFolderIds, getUserOemId, belongsToUserTeams } from "~/utils";
import { IMachine } from "~/datamodels/Machine/interface";
import { IContext } from "~/types/common";
import { MACHINE_DOCUMENTATION_STATUSES } from "~/constants/machine";
import { ICustomer } from "~/datamodels/Customer/interface";
import { AssistantType } from "~/datamodels/AiAssistant/constants";
import { IMachineTemplate } from "~/datamodels/MachineTemplate/interface";
import { ICustomAdditionalField } from "~/datamodels/CustomAdditionalField/interface";
import { VisibilityScopeEnum } from "~/datamodels/CustomAdditionalField/schema";

export const types = `#graphql
  type CustomField {
    _id: ID
    fieldId: CustomAdditionalField
    value: JSON
  }

  type AssetParent {
    id: ID
    name: String
    serialNumber: String
  }

  type AssetPart {
    part: InventoryPart
    addedBy: User
  }

  type AssetDetachedFromTemplate {
    description: Boolean
    image: Boolean
    inventoryParts: Boolean
    documentation: String
  }

  type Asset {
    _id: ID
    name: String!
    serialNumber: String
    description: String
    documentationFiles: Int
    issues: Int
    customer: Customer
    customFields: [CustomField]
    oem: Oem
    image: String
    thumbnail: String
    totalOpenTickets: Int
    slug: String
    folderId: String
    isBoxFoldersDisabled: Boolean
    template: AssetTemplate
    createdAt: DateTime
    updatedAt: DateTime
    inventoryParts: [AssetPart]
    isQRCodeEnabled: Boolean
    generalAccessUrl: String
    uuid: String
    documentFolders: DocumentFolders
    teams: [Team]
    numberOfTeams: Int
    _3dModelUrl: String
    _3dModelUrlUploadedBy: User
    detachedFromTemplate: AssetDetachedFromTemplate
    createdBy: ID
    isAsset3dModelDeletable: Boolean
    hierarchy: [AssetParent]
    assetType: ID
    childrenCount: Int
    aiAssistant: AiAssistant
    sharedAssistant: AiAssistant
    totalChildrenCount: Int
    showParent: Boolean
    isSharedAsset: Boolean
    isOwnAsset: Boolean
    isQRCodeFlowEnabled: Boolean
    sharedAssetAccess: AssetAccess
  }

  type DocumentFolders {
    _id: ID
    internalId: ID @${roles.is.technician}
    externalId: ID
  }

  type PaginatedAssets {
    totalCount: Int
    limit: Int
    skip: Int
    currentPage: Int
    assets: [Asset]
  }

  union AssetOrTemplate = Asset | AssetTemplate

  type PaginatedModels {
    totalCount: Int
    limit: Int
    skip: Int
    currentPage: Int
    models: [AssetOrTemplate]
  }

  type KanBanAssetType {
    _id: ID
    label: String
  }

  type KanbanColumnAsset {
    _id: Int
    boardFor: String
    type: String
    customField: ID
    column: String
    columnIndex: Int
    cards: [Asset]
    totalCount: Int
    limit: Int
    skip: Int
    currentPage: Int
    assetType: KanBanAssetType
  }
  
  type PaginatedKanbanAssets {
    columns: [KanbanColumnAsset],
    totalCount: Int
  }

  type PaginatedSharedAssetsOem {
    totalCount: Int
    limit: Int
    skip: Int
    currentPage: Int
    oems: [Oem]
  }
`;

const qrAppUrl = process.env.QR_APP_URI;
const assetUrlModifier = process.env.MACHINE_URL_MODIFIER;

export const typeResolvers = {
  AssetOrTemplate: {
    __resolveType(obj: IMachine | IMachineTemplate) {
      if ((obj as IMachine).serialNumber !== undefined) {
        return "Asset";
      }
      if ((obj as IMachineTemplate).templateId !== undefined) {
        return "AssetTemplate";
      }
      return null;
    },
  },
  Asset: {
    totalChildrenCount: async (
      { _id, totalChildrenCount }: IMachine,
      _: any,
      { dataSources }: IContext,
    ) => {
      if (typeof totalChildrenCount === "number") return totalChildrenCount;

      return await dataSources.Machine.countDocuments({
        hierarchy: {
          $elemMatch: { id: _id.toString() },
        },
      });
    },
    showParent: async ({ hierarchy }: IMachine, _: any, { dataSources, user }: IContext) => {
      if (!hierarchy?.length) return false;

      const parent = await dataSources.Machine.getOne({
        _id: hierarchy[0].id,
        ...belongsToUserTeams(user),
      });

      return !!parent;
    },
    documentFolders: async (
      { _id, documentFolders, oem }: IMachine,
      _: any,
      { dataSources, user }: IContext,
    ) => {
      const { internalId, externalId } = documentFolders || {};

      if ((!internalId || !externalId) && user) {
        const oemId = await getUserOemId({ user, dataSources });

        saveDocumentationFolderIds({
          oemId,
          userId: user.id!,
          documentId: _id,
          dataSources,
          collectionName: "Machine",
        });
      }

      const assetDocsResponse = {
        internalId: documentFolders?.internalId,
        externalId: documentFolders?.externalId,
        _id: `${oem}__${_id}__FOLDERS`,
      };

      return assetDocsResponse;
    },

    uuid: async ({ _id, uuid }: IMachine, _: any, { dataSources }: IContext) => {
      let uuidValue;

      if (!uuid && _id) {
        uuidValue = createUuidFromSeed(_id?.toString());
        await dataSources.Machine.save({
          _id,
          uuid: uuidValue,
        });
      }

      return uuidValue ?? uuid;
    },

    generalAccessUrl: async ({ _id, uuid }: IMachine, _: any, { dataSources }: IContext) => {
      let path = uuid;

      if (!uuid && _id) {
        const uuidValue = createUuidFromSeed(_id?.toString());
        await dataSources.Machine.save({
          _id,
          uuid: uuidValue,
        });
        path = uuidValue;
      }

      return `${qrAppUrl}/${assetUrlModifier}/${path}`;
    },

    isQRCodeEnabled: async () => true,

    isBoxFoldersDisabled: async ({ oem }: IMachine, _: any, { dataSources }: IContext) => {
      const oemData = await dataSources.Oem.loadOne(oem);
      return oemData?.isBoxFoldersDisabled;
    },

    customer: ({ customer }: IMachine, _: any, { dataSources }: IContext) =>
      dataSources.Customer.loadOne(customer),

    oem: ({ oem }: IMachine, _: any, { dataSources }: IContext) => {
      if (!oem) return null;

      return dataSources.Oem.loadOne(oem);
    },

    totalOpenTickets: async ({ _id, oem }: IMachine, _: any, { dataSources }: IContext) => {
      const oemData = await dataSources.Oem.loadOne(oem);

      const closedStatusId = oemData?.statuses?.[oemData.statuses.length - 1]?._id;

      return dataSources.Ticket.countDocuments({
        machine: _id,
        status: { $ne: closedStatusId },
      });
    },

    // eslint-disable-next-line camelcase
    createdAt: async ({ created_at }: IMachine) => created_at,

    // eslint-disable-next-line camelcase
    updatedAt: async ({ updated_at }: IMachine) => updated_at,

    _3dModelUrlUploadedBy: (
      { _3dModelUrlUploadedBy }: IMachine,
      _: any,
      { dataSources }: IContext,
    ) => dataSources.User.loadOne(_3dModelUrlUploadedBy),

    isAsset3dModelDeletable: async ({ _id, oem }: IMachine, _: any, { dataSources }: IContext) => {
      const guidesMadeThroughAsset = await dataSources.Guide.getOne({
        machine: _id,
        oem,
      });

      return !guidesMadeThroughAsset;
    },

    template: async ({ template }: IMachine, _: any, { dataSources }: IContext) => {
      if (!template) return null;

      const tmpl = await dataSources.MachineTemplate.loadOne(template);

      return tmpl;
    },

    teams: async ({ teams, oem }: IMachine, _: any, { dataSources }: IContext) => {
      const assetTeams = await dataSources.Team.loadManyByQuery({
        _id: { $in: teams || [] },
        oem,
      });
      return assetTeams;
    },

    numberOfTeams: ({ teams }: IMachine) => teams?.length || 0,

    inventoryParts: async (
      { detachedFromTemplate, inventoryParts, template }: IMachine,
      _: any,
      { dataSources }: IContext,
    ) => {
      if (template && !detachedFromTemplate?.inventoryParts) {
        const assetTemplate = await dataSources.MachineTemplate.getById(template, {
          select: { inventoryParts: 1 },
        });

        return assetTemplate?.inventoryParts || [];
      }

      return inventoryParts;
    },

    sharedAssistant: async (
      { _id, template, detachedFromTemplate }: IMachine,
      _: any,
      { dataSources }: IContext,
    ) => {
      const assistant = await dataSources.AiAssistant.getOne(
        {
          ...(!!template &&
          detachedFromTemplate?.documentation === MACHINE_DOCUMENTATION_STATUSES.ATTACHED
            ? { templateId: template, assistantType: AssistantType.EXTERNAL }
            : { machineID: _id, assistantType: AssistantType.EXTERNAL }),
        },
        {
          projection: { _id: 1, assistantName: 1 },
        },
      );
      return assistant;
    },
    description: async (
      { detachedFromTemplate, description, template }: IMachine,
      _: any,
      { dataSources }: IContext,
    ) => {
      if (template && !detachedFromTemplate?.description) {
        const assetTemplate = await dataSources.MachineTemplate.getById(template, {
          select: { description: 1, type: 1 },
        });

        if (assetTemplate?.description) {
          return assetTemplate?.description;
        }
      }

      return description;
    },

    image: async (
      { detachedFromTemplate, image, template }: IMachine,
      _: any,
      { dataSources }: IContext,
    ) => {
      if (template && !detachedFromTemplate?.image) {
        const assetTemplate = await dataSources.MachineTemplate.getById(template, {
          select: { image: 1, type: 1 },
        });

        if (assetTemplate?.image) {
          return assetTemplate?.image;
        }
      }

      return image;
    },

    aiAssistant: async (
      { _id, template, detachedFromTemplate }: IMachine,
      _: any,
      { dataSources }: IContext,
    ) => {
      const internalAiAssistant = await dataSources.AiAssistant.getOne(
        {
          ...(!!template &&
          detachedFromTemplate?.documentation === MACHINE_DOCUMENTATION_STATUSES.ATTACHED
            ? { templateId: template, assistantType: AssistantType.INTERNAL }
            : { machineID: _id, assistantType: AssistantType.INTERNAL }),
        },
        {
          projection: { _id: 1, assistantName: 1 },
        },
      );

      if (!internalAiAssistant)
        return await dataSources.AiAssistant.getOne(
          {
            ...(!!template &&
            detachedFromTemplate?.documentation === MACHINE_DOCUMENTATION_STATUSES.ATTACHED
              ? { templateId: template, assistantType: AssistantType.EXTERNAL }
              : { machineID: _id, assistantType: AssistantType.EXTERNAL }),
          },
          {
            projection: { _id: 1, assistantName: 1 },
          },
        );
      return internalAiAssistant;
    },
    detachedFromTemplate: ({ detachedFromTemplate, template }: IMachine) => ({
      description: !!detachedFromTemplate?.description,
      image: !!detachedFromTemplate?.image,
      inventoryParts: !!detachedFromTemplate?.inventoryParts,
      documentation:
        detachedFromTemplate?.documentation ||
        (template
          ? MACHINE_DOCUMENTATION_STATUSES.ATTACHED
          : MACHINE_DOCUMENTATION_STATUSES.DETACHED),
    }),

    sharedAssetAccess: async ({ customer }: IMachine, _: any, { dataSources }: IContext) => {
      const sharedOrganizationDetails: ICustomer = await dataSources.CustomerPortal.getOne({
        connection: customer,
      });
      if (!sharedOrganizationDetails)
        return {
          documentation: true,
          parts: true,
          preventiveMaintenance: true,
          _3dModel: true,
          qrCodes: true,
          history: true,
        };
      return sharedOrganizationDetails?.assetAccess;
    },
    customFields: async (parent: IMachine, args: any, { dataSources, user }: IContext) => {
      if (!parent.customFields || parent.customFields.length === 0 || !user) {
        return [];
      }

      if (parent.oem?.toString() === user.organization?.toString()) {
        return parent.customFields;
      }

      const fieldIds = parent.customFields.map((field: { fieldId: any }) => field.fieldId);

      const customFieldDocs = await dataSources.CustomAdditionalField.getManyByQuery({
        _id: { $in: fieldIds },
        visibilityScope: VisibilityScopeEnum.EXTERNAL,
      });

      const externalFieldIds = customFieldDocs.map((doc: ICustomAdditionalField) =>
        doc._id.toString(),
      );

      return parent.customFields.filter(field =>
        externalFieldIds.includes(field.fieldId.toString()),
      );
    },
  },

  AssetPart: {
    part: async ({ part }: { part: Types.ObjectId }, _: any, { dataSources }: IContext) => {
      if (!part) return null;

      return await dataSources.InventoryPart.loadOne(part);
    },
    addedBy: async (
      { addedBy }: { addedBy: Types.ObjectId },
      _: any,
      { dataSources }: IContext,
    ) => {
      if (!addedBy) return null;

      return await dataSources.User.loadOne(addedBy);
    },
  },
};

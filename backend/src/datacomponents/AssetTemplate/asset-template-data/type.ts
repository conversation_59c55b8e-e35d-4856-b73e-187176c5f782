import { Types } from "mongoose";
import { CONTACT_ACCESS_STATUS } from "~/constants/contacts";
import { AssistantType } from "~/datamodels/AiAssistant/constants";
import type { IContact } from "~/datamodels/Contact/interface";
import type { IMachineTemplate } from "~/datamodels/MachineTemplate/interface";
import { AssetTemplate } from "~/graphql/types";
import { getAssetTemplateBoxFolderAccessToken } from "~/services/assetTemplate/fetch";
import type { IContext } from "~/types/common";
import { saveDocumentationFolderIds, getUserOemId } from "~/utils";

export const types = `#graphql
  type AssetTemplatePart {
    part: InventoryPart
    addedBy: User
  }

  type AssetTemplateProcedure {
    procedureId: ID
    addedBy: User
    procedure: Procedure
  }
  
  type AssetTemplate {
    _3dModelUrl: String
    _3dModelUrlUploadedBy: User
    _id: ID
    aiAssistant: AiAssistant
    title: String!
    description: String
    image: String
    thumbnail: String
    assets: [Asset]
    oem: Oem
    createdBy: ID
    createdAt: DateTime
    sharedBoxToken: String
    updatedAt: DateTime
    templateId: String
    documentFolders: DocumentFolders
    inventoryParts: [AssetTemplatePart]
    visibility: Int
    procedures: [AssetTemplateProcedure]
    productAccess: ProductAccess
  }

  type PaginatedSharedAssetTemplates {
    totalCount: Int
    limit: Int
    skip: Int
    currentPage: Int
    templates: [AssetTemplate]
  }
`;

export const typeResolvers = {
  AssetTemplate: {
    aiAssistant: async ({ _id }: IMachineTemplate, _: any, { dataSources }: IContext) => {
      const internalAiAssistant = await dataSources.AiAssistant.getOne(
        { templateId: _id, assistantType: AssistantType.INTERNAL },
        {
          projection: { _id: 1, assistantName: 1 },
        },
      );

      if (!internalAiAssistant)
        return await dataSources.AiAssistant.getOne(
          { templateId: _id, assistantType: AssistantType.EXTERNAL },
          {
            projection: { _id: 1, assistantName: 1 },
          },
        );
      return internalAiAssistant;
    },
    createdAt: ({ created_at }: { created_at: string }) =>
      created_at ? new Date(created_at)?.toISOString() : null,
    documentFolders: async (
      { _id, documentFolders }: AssetTemplate,
      _: any,
      { dataSources, user }: IContext,
    ) => {
      const { internalId, externalId } = documentFolders || {};
      if (!internalId || !externalId) {
        const oemId = await getUserOemId({ user, dataSources });

        saveDocumentationFolderIds({
          oemId,
          userId: user.id!,
          // @ts-ignore
          documentId: _id,
          dataSources,
          collectionName: "MachineTemplate",
        });
      }

      return documentFolders;
    },
    assets: async ({ _id, oem }: AssetTemplate, _: any, { dataSources }: IContext) => {
      const assets = await dataSources.Machine.getMany({
        template: _id,
        oem,
      });

      return assets;
    },
    _3dModelUrlUploadedBy: (
      { _3dModelUrlUploadedBy }: IMachineTemplate,
      _: any,
      { dataSources }: IContext,
    ) => dataSources.User.loadOne(_3dModelUrlUploadedBy),
    oem: async ({ oem }: AssetTemplate, _: any, { dataSources }: IContext) =>
      // @ts-ignore
      await dataSources.Oem.loadOne(oem),
    productAccess: async ({ oem }: AssetTemplate, _: any, { dataSources, user }: IContext) => {
      const correspondingContact: IContact = await dataSources.Contact.getOne({
        user: user.id,
        oem,
        accessStatus: CONTACT_ACCESS_STATUS.ACTIVE,
      });
      const customerPortal = await dataSources.CustomerPortal.getOne({
        connection: correspondingContact.connection,
      });
      return customerPortal.productAccess;
    },
    sharedBoxToken: async (
      { _id, oem }: AssetTemplate,
      _: any,
      { dataSources, user }: IContext,
    ) => {
      if (oem !== user.organization) {
        return await getAssetTemplateBoxFolderAccessToken({
          // @ts-ignore
          args: { input: { assetTemplateId: _id.toString() } },
          dataSources,
          user,
        });
      }
      const dbUser = dataSources.User.loadOne(user.id);
      return dbUser.foldersAccessToken;
    },
    updatedAt: ({ updated_at }: { updated_at: string }) =>
      updated_at ? new Date(updated_at)?.toISOString() : null,
  },
  AssetTemplatePart: {
    part: async ({ part }: { part: Types.ObjectId }, _: any, { dataSources }: IContext) => {
      if (!part) return null;
      return await dataSources.InventoryPart.loadOne(part);
    },
    addedBy: async (
      { addedBy }: { addedBy: Types.ObjectId },
      _: any,
      { dataSources }: IContext,
    ) => {
      if (!addedBy) return null;
      return await dataSources.User.loadOne(addedBy);
    },
  },
  AssetTemplateProcedure: {
    procedure: async (
      { procedureId }: { procedureId: Types.ObjectId },
      _: any,
      { dataSources }: IContext,
    ) => {
      if (!procedureId) return null;
      return await dataSources.ProcedureTemplate.loadOne(procedureId);
    },
  },
};

import { ExecutionContext } from "graphql/execution/execute";
import { roles } from "~/directives";
import { InputQueryParams } from "~/graphql/types";
import {
  getAllAssetTemplates,
  getAssetTemplate,
  getSharedAssetTemplates,
  listAssetTemplatesMissingTemplateIds,
} from "~/services/assetTemplate/fetch";
import { IContext } from "~/types/common";

export const queryTypes = `
  type Query {
    getOwnOemAssetTemplate(templateId: ID!): AssetTemplate @${roles.is.technician}
    listOwnOemAssetTemplates(params: InputQueryParams!): [AssetTemplate]! @${roles.is.technician}
    listSharedAssetTemplates(params: InputQueryParams!): PaginatedSharedAssetTemplates @${roles.is.technician}
    listOwnOemAssetTemplatesMissingTemplateIds(templateIds: [String!]!): [String] @${roles.is.technician}
  }
`;

export const queryResolvers = {
  Query: {
    getOwnOemAssetTemplate: async (
      _: ExecutionContext["contextValue"],
      args: { templateId: string },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await getAssetTemplate({
        args: { input: { id: args.templateId }, files: null, headers: {}, query: {}, params: {} },
        dataSources,
        user,
        translate,
      }),

    listOwnOemAssetTemplates: async (
      _: ExecutionContext["contextValue"],
      args: { params: InputQueryParams },
      { dataSources, user, req: { t: translate } }: IContext,
    ) => {
      const result = await getAllAssetTemplates({
        args: { input: {}, files: null, headers: {}, query: {}, params: args.params },
        dataSources,
        user,
        translate,
      });

      if (result && result?.templates) {
        return result.templates;
      }
      return result;
    },

    listOwnOemAssetTemplatesMissingTemplateIds: async (
      _: ExecutionContext["contextValue"],
      args: { templateIds: string[] },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await listAssetTemplatesMissingTemplateIds({
        args: { input: args, files: null, headers: {}, query: {}, params: {} },
        dataSources,
        user,
        translate,
      }),

    listSharedAssetTemplates: async (
      _: ExecutionContext["contextValue"],
      args: { params: InputQueryParams },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await getSharedAssetTemplates({
        // @ts-ignore
        args: { input: {}, files: null, headers: {}, query: {}, params: args.params },
        dataSources,
        user,
        translate,
      }),
  },
};

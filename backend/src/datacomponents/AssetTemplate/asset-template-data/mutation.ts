import { ExecutionContext } from "graphql/execution/execute";
import { Types } from "mongoose";
import { roles } from "~/directives";
import { IContext } from "~/types/common";
import {
  InputAssignAssetTemplateInventoryParts,
  InputCreateAssetTemplate,
  InputRemoveAssetTemplateInventoryPart,
  InputUpdateAssetTemplate,
} from "~/graphql/types";
import { createAssetTemplate } from "~/services/assetTemplate/create";
import {
  assignInventoryPartsToAssetTemplate,
  removeInventoryPartsFromAssetTemplate,
  updateAssetTemplate,
  attachProceduresToTemplate as _attachProceduresToTemplate,
  detachProcedureFromTemplate as _detachProcedureFromTemplate,
  addAssetTemplate3DModel,
} from "~/services/assetTemplate/update";
import { deleteAssetTemplate, deleteAssetTemplate3DModel } from "~/services/assetTemplate/delete";

export const mutationTypes = `#graphql
  type Mutation {
    addOwnOemAssetTemplate3DModel(_id: ID!, _3dModelUrl: String!): AssetTemplate @${roles.is.technician}
    assignOwnOemInventoryPartsToAssetTemplate(input: InputAssignAssetTemplateInventoryParts): AssetTemplate @${roles.is.staff}
    attachProceduresToTemplate(procedures: [ID], templateId: ID): AssetTemplate @${roles.is.staff}
    deleteOwnOemAssetTemplate3DModel(templateId: ID!): AssetTemplate @${roles.is.technician}
    createOwnOemAssetTemplate(input: InputCreateAssetTemplate!): AssetTemplate @${roles.is.staff}
    deleteOwnOemAssetTemplate(templateId: ID!): AssetTemplate @${roles.is.staff}
    detachProcedureFromTemplate(procedureId: ID, templateId: ID!): AssetTemplate @${roles.is.staff}
    removeOwnOemInventoryPartFromAssetTemplate(input: InputRemoveAssetTemplateInventoryPart): AssetTemplate @${roles.is.staff}
    updateOwnOemAssetTemplate(input: InputUpdateAssetTemplate!): AssetTemplate @${roles.is.staff}
  }
`;

export const mutationResolvers = {
  Mutation: {
    addOwnOemAssetTemplate3DModel: async (
      _: ExecutionContext["contextValue"],
      args: { _id: string; _3dModelUrl: string },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await addAssetTemplate3DModel({
        args: {
          input: {
            _id: args._id,
            _3dModelUrl: args._3dModelUrl,
          },
          files: null,
          headers: {},
          query: {},
          params: {},
        },
        dataSources,
        user,
        translate,
      }),
    assignOwnOemInventoryPartsToAssetTemplate: async (
      _: ExecutionContext["contextValue"],
      args: { input: InputAssignAssetTemplateInventoryParts },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await assignInventoryPartsToAssetTemplate({
        args: { input: args.input, files: null, headers: {}, query: {}, params: {} },
        dataSources,
        user,
        translate,
      }),

    attachProceduresToTemplate: async (
      _: ExecutionContext["contextValue"],
      args: { procedures: Types.ObjectId[]; templateId: Types.ObjectId },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await _attachProceduresToTemplate({
        args: {
          input: args,
          files: null,
          headers: {},
          query: {},
          params: {},
        },
        dataSources,
        user,
        translate,
      }),

    detachProcedureFromTemplate: async (
      _: ExecutionContext["contextValue"],
      args: { procedureId: Types.ObjectId; templateId: Types.ObjectId },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await _detachProcedureFromTemplate({
        args: {
          input: args,
          files: null,
          headers: {},
          query: {},
          params: {},
        },
        dataSources,
        user,
        translate,
      }),

    createOwnOemAssetTemplate: async (
      _: ExecutionContext["contextValue"],
      args: { input: InputCreateAssetTemplate },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await createAssetTemplate({
        args: { input: args.input, files: null, headers: {}, query: {}, params: {} },
        dataSources,
        user,
        translate,
      }),

    deleteOwnOemAssetTemplate: async (
      _: ExecutionContext["contextValue"],
      args: { templateId: string },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await deleteAssetTemplate({
        args: {
          input: {
            templateId: args.templateId,
          },
          files: null,
          headers: {},
          query: {},
          params: {},
        },
        dataSources,
        user,
        translate,
      }),

    removeOwnOemInventoryPartFromAssetTemplate: async (
      _: ExecutionContext["contextValue"],
      args: { input: InputRemoveAssetTemplateInventoryPart },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await removeInventoryPartsFromAssetTemplate({
        args: { input: args.input, files: null, headers: {}, query: {}, params: {} },
        dataSources,
        user,
        translate,
      }),

    updateOwnOemAssetTemplate: async (
      _: ExecutionContext["contextValue"],
      args: { input: InputUpdateAssetTemplate },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await updateAssetTemplate({
        args: { input: args.input, files: null, headers: {}, query: {}, params: {} },
        dataSources,
        user,
        translate,
      }),
    deleteOwnOemAssetTemplate3DModel: async (
      _: ExecutionContext["contextValue"],
      args: { templateId: string },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await deleteAssetTemplate3DModel({
        args: {
          input: {
            templateId: args.templateId,
          },
          files: null,
          headers: {},
          query: {},
          params: {},
        },
        dataSources,
        user,
        translate,
      }),
  },
};

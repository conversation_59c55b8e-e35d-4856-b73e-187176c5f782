import { describe, expect, test, vi, afterEach } from "vitest";
import moment from "moment";
import { throwIfError } from "~/utils";
import { generateCsv, getRow, getRows, validateGetDataCsvParams } from "./_helpers"; // Import getRows method
import { IAggregationData, ICustomAdditionalFieldWithIndex } from "./_types";
import { IOem, IOemStatus, IOemTicketType } from "~/datamodels/Oem/interface";
import { DATE_FORMAT_CREATED_AT, DATE_FORMAT_SCHEDULING } from "./_consts";
import { PRODUCT_TYPES, TIER_TYPES } from "~/constants/plans";
import { mockAppConfig } from "#/tests/mocks/AppConfig";

const enabledFeatures = { teams: true, procedures: true };

const qrAppUrl = process.env.QR_APP_URI;

const machineUrlModifier = process.env.MACHINE_URL_MODIFIER;

vi.mock("~/utils", async () => ({
  throwIfError: vi.fn(),
  getEnums: () => [],
}));

const TICKET = "Ticket";
const MACHINE = "Machine";
const CUSTOMER = "Customer";
const INVENTORY_PART = "InventoryPart";

const mockData: any = {
  id: 1,
  title: "Test Ticket",
  ticketType: "Incident",
  ticketVisibility: "External",
  teams: ["Team A", "Team B"],
  facility: "Facility A",
  facilityId: 123,
  machine: "Machine 1",
  serialNumber: "SN123",
  countProceduresAttached: 5,
  user: "User A",
  assignees: ["Assignee A", "Assignee B"],
  created_at: moment().toISOString(),
  status: "Open",
  schedule: {
    startTime: moment().toISOString(),
    endTime: moment().add(1, "day").toISOString(),
  },
  countPartsRequested: 10,
  lineId: 2,
  name: "Production Line 1",
  countMachinesAttached: 3,
  qrCode: `${qrAppUrl}/${machineUrlModifier}/1234`,
  uuid: "1234",
  isQRCodeEnabled: true,
  countPartsAttached: 20,
  countPreventiveMaintenanceEvents: 8,
  facilityIdentifier: "Facility 123",
  articleNumber: "Article 123",
  oem: [
    {
      _id: "12312312312312",
      statuses: [{ label: "Closed" }],
    },
  ],
};

describe("Exporter _helpers.ts", () => {
  afterEach(() => {
    vi.restoreAllMocks();
  });
  describe("getRow method", () => {
    afterEach(() => {
      // Any cleanup code after each test if needed
    });

    test("getRow for TICKET resource should generate correct CSV row", () => {
      const index = 0;
      const expectedRow = [
        index + 1,
        mockData.id,
        mockData.title,
        mockData.ticketType,
        mockData.ticketVisibility,
        mockData.user,
        mockData.status,
        moment.utc(mockData.schedule.startTime).format(DATE_FORMAT_SCHEDULING),
        moment.utc(mockData.schedule.endTime).format(DATE_FORMAT_SCHEDULING),
        moment.utc(mockData.created_at).format(DATE_FORMAT_CREATED_AT),
        mockData.assignees.join(", "),
        mockData.facilityId,
        mockData.facility,
        mockData.serialNumber,
        mockData.machine,
        moment.utc(mockData.schedule.startTime).format(DATE_FORMAT_SCHEDULING),
        "-",
        mockData.teams.join(", "),
        mockData.countProceduresAttached,
      ];

      const generatedRow = getRow[TICKET](mockData, index, enabledFeatures);
      expect(generatedRow).toEqual(expectedRow);
    });

    test("getRow for MACHINE resource should generate correct CSV row", () => {
      const index = 0;
      const expectedRow = [
        index + 1,
        mockData.serialNumber,
        mockData.name,
        "-",
        mockData.facilityId,
        mockData.facility,
        mockData.countPartsAttached,
        mockData.countPreventiveMaintenanceEvents,
        mockData.qrCode,
        mockData.templateId,
        mockData.teams.join(", "),
        "-",
        "-",
        0,
      ];

      const generatedRow = getRow[MACHINE](mockData, index, enabledFeatures);
      expect(generatedRow).toEqual(expectedRow);
    });

    test("getRow for CUSTOMER resource should generate correct CSV row", () => {
      const index = 0;
      const expectedRow = [
        index + 1,
        mockData.facilityIdentifier,
        mockData.name,
        mockData.countMachinesAttached,
        mockData.teams.join(", "),
      ];

      const generatedRow = getRow[CUSTOMER](mockData, index, enabledFeatures);
      expect(generatedRow).toEqual(expectedRow);
    });

    test("getRow for INVENTORY_PART resource should generate correct CSV row", () => {
      const index = 0;
      const expectedRow = [index + 1, mockData.articleNumber, mockData.name];

      const generatedRow = getRow[INVENTORY_PART](mockData, index, enabledFeatures);
      expect(generatedRow).toEqual(expectedRow);
    });
  });

  describe("getRows method in _helpers.ts", async () => {
    // @ts-ignore
    const oem: IOem = {
      statuses: [{ _id: "1", label: "OPEN" }] as IOemStatus[], // after processing, first letter should be capital, rest small
      ticketTypes: [{ _id: "2", name: "Incident", isInternal: false }] as IOemTicketType[],
      paidFeatures: ["teams", "procedures"],
      installedProducts: [{ type: PRODUCT_TYPES.CMMS, tier: TIER_TYPES.ENTERPRISE }],
    };

    const data: IAggregationData[] = [
      {
        id: "1",
        title: "Test Ticket",
        ticketType: "2",
        ticketVisibility: "External",
        teams: ["Team A", "Team B"],
        facility: "Facility A",
        facilityId: "123",
        machine: "Machine 1",
        serialNumber: "SN123",
        countProceduresAttached: 5,
        user: "User A",
        assignees: ["Assignee A", "Assignee B"],
        created_at: moment().toISOString(),
        status: "1",
        schedule: {
          startTime: moment().toISOString(),
          endTime: moment().add(1, "day").toISOString(),
        },
        countPartsRequested: 10,
        countPartsAttached: 5,

        customFields: [
          // @ts-ignore
          { fieldId: "3", value: "Custom Value" },
          // @ts-ignore
          { fieldId: "4", value: "Custom Value 2" },
        ],

        oem: [
          {
            _id: "12312312312312",
            statuses: [{ label: "Closed" }],
          },
        ],
      },
    ];

    const customAdditionalFields: ICustomAdditionalFieldWithIndex[] = [
      // @ts-ignore
      {
        _id: "3",
        label: "Custom Field",
        index: 0,
      },
      // @ts-ignore
      {
        _id: "4",
        label: "Custom Field 2",
        index: 0,
      },
    ];

    test("getRows should generate correct rows for TICKET resource", async () => {
      const expectedRows = [
        [
          "#",
          "ID",
          "Title",
          "Type",
          "Visibility",
          "Reporter",
          "Status",
          "Scheduled From (UTC)",
          "Scheduled To (UTC)",
          "Created Date",
          "Assignees",
          "Connection ID",
          "Connection Name",
          "Asset Serial Number",
          "Asset Name",
          "Created Time (UTC)",
          "Resolution Time (h:m)",
          "Teams",
          "Number of Procedures Attached",
          "Custom Field",
          "Custom Field 2",
        ],
        [
          1,
          "1",
          "Test Ticket",
          "Incident",
          "External",
          "User A",
          "Open",
          moment.utc(data[0].schedule.startTime).format(DATE_FORMAT_SCHEDULING),
          moment.utc(data[0].schedule.endTime).format(DATE_FORMAT_SCHEDULING),
          moment.utc(data[0].created_at).format(DATE_FORMAT_CREATED_AT),
          "Assignee A, Assignee B",
          "123",
          "Facility A",
          "SN123",
          "Machine 1",
          moment.utc(data[0].schedule.startTime).format(DATE_FORMAT_SCHEDULING),
          "-",
          "Team A, Team B",
          5,
          "Custom Value",
          "Custom Value 2",
        ],
      ];

      const generatedRows = await getRows({
        oem,
        data,
        resource: TICKET,
        customAdditionalFields,
        dataSources: {
          AppConfig: {
            getOne: () => mockAppConfig,
          },
        },
      });

      expect(generatedRows).toEqual(expectedRows);
    });
  });

  describe("generateCsv method in _helpers.ts", () => {
    test("generateCsv should return CSV string for valid input", async () => {
      const rows = [
        ["Index", "Name", "Age"],
        [1, "John Doe", 30],
        [2, "Jane Smith", 25],
      ];

      const expectedCsvString = `Index,Name,Age\n1,John Doe,30\n2,Jane Smith,25\n`;

      const csvString = await generateCsv(rows);

      expect(csvString).toBe(expectedCsvString);
    });

    test("generateCsv should throw error for invalid input", async () => {
      const rows = undefined; // Invalid input

      await expect(generateCsv(rows as any)).rejects.toThrow();
    });
  });

  describe("validateGetDataCsvParams method in _helpers.ts", () => {
    afterEach(() => {
      vi.restoreAllMocks();
    });
    test("validateGetDataCsvParams should not throw error for valid input", () => {
      const params = { resource: "Ticket", uuid: "some-uuid" };

      validateGetDataCsvParams(params);

      expect(throwIfError).not.toHaveBeenCalledOnce();
    });

    test("validateGetDataCsvParams should throw error if resource is missing", () => {
      const params = { resource: "", uuid: "some-uuid" };

      // @ts-ignore
      validateGetDataCsvParams(params);
      expect(throwIfError).toHaveBeenCalledOnce();
      expect(throwIfError).toHaveBeenCalledWith("`resource` is required");
    });

    test("validateGetDataCsvParams should throw error if uuid is missing", () => {
      const params = { resource: "Ticket", uuid: "" };

      validateGetDataCsvParams(params);
      expect(throwIfError).toHaveBeenCalledOnce();
      expect(throwIfError).toHaveBeenCalledWith("`uuid` is required");
    });

    test("validateGetDataCsvParams should throw error if resource is invalid", () => {
      const params = { resource: "INVALID_RESOURCE", uuid: "some-uuid" };

      validateGetDataCsvParams(params);
      expect(throwIfError).toHaveBeenCalledOnce();
      expect(throwIfError).toHaveBeenCalledWith("INVALID_RESOURCE is not a valid resource");
    });
  });
});

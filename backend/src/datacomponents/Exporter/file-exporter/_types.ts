import { AnyExpression, Expression } from "mongoose";
import {
  IAddress,
  ICustomAdditionalField,
  ICustomFieldObject,
} from "~/datamodels/CustomAdditionalField/interface";

import {
  DETAIL_EXPORT,
  DETAIL_EXPORT_VALIDATORS,
} from "~/datacomponents/Exporter/file-exporter/_consts";
import { IOemStatus } from "~/datamodels/Oem/interface";
import { IProcedure } from "~/datamodels/Procedure/interface";
import { ITimeTrackerLog } from "~/datamodels/Ticket/interface";
import { IInventoryPart } from "~/datamodels/InventoryPart/interface";
import { ICustomer } from "~/datamodels/Customer/interface";
import { IPreventiveMaintenance } from "~/datamodels/PreventiveMaintenance/interface";
import { IMachineHistory } from "~/datamodels/MachineHistory/interface";

export interface IProject {
  name?: 1;
  serialNumber?: 1;
  id?: 1;
  title?: 1;
  teams?: 1;
  assetType?: 1;
  created_at?: 1;
  createdAt?: 1;
  status?: 1;
  customFields?: 1;
  machine?: 1;
  facility?: 1;
  user?: 1;
  assignees?: 1;
  ticketType?: 1;
  machineSerialNumber?: 1;
  facilityId?: 1;
  "schedule.startTime"?: 1;
  "schedule.endTime"?: 1;
  countPartsRequested?: 1;
  countProceduresAttached?: 1;
  countMachinesAttached?: 1;
  countPreventiveMaintenanceEvents?: 1;
  countPartsAttached?: 1;
  totalChildrenCount?: 1;
  hierarchy?: 1;
  facilityIdentifier?: 1;
  articleNumber?: 1;
  isQRCodeEnabled?: 1;
  uuid?: 1;
  history?: Expression.ConcatArrays;
  timeTrackerLogs?: 1 | "$$REMOVE";
  procedures?: 1 | "$$REMOVE";
  inventoryParts?: 1 | "$$REMOVE";
  templateId?: 1;
  preventiveMaintenanceEvents?: 1 | "$$REMOVE";
  assetHistory?: 1 | "$$REMOVE";
}

type SortOrder = "asc" | "desc";

export type TypeSort = `${string}:${SortOrder}`[];

export type TypeCustomFieldType = {
  [x: string]: "machines" | "tickets" | "facilities" | "parts";
};

export interface ICustomAdditionalFieldWithIndex extends ICustomAdditionalField {
  index: number;
}

export interface ICustomAdditionalFieldObjectWithIndex extends ICustomFieldObject {
  index: number;
  label: string;
}

export interface IAggregationData {
  index: number;
  id: string;
  name: string;
  title: string;
  assetType: string;
  ticketType: string;
  ticketVisibility: string;
  teams: string[];
  facility: string;
  facilityId: string;
  machine: string;
  machineSerialNumber: string;
  countProceduresAttached: number;
  user: string;
  assignees: string[];
  created_at: string;
  updated_at: string;
  closedOn: string;
  createdAt: string;
  status: string;
  schedule: {
    startTime: string;
    endTime: string;
  };
  countPartsRequested: number;
  countPartsAttached: number;
  countMachinesAttached: number;
  totalChildrenCount: number;
  hierarchy: Array<{ id: string; name: string; serialNumber: string }>;
  countPreventiveMaintenanceEvents: number;
  templateId: string;
  inventoryParts: {
    quantity: string;
    details: IInventoryPart;
  }[];
  preventiveMaintenanceEvents: IPreventiveMaintenance[];
  assetHistory: IMachineHistory[];
  timeTrackerLogs: ITimeTrackerLog[] & { ticketTagLabel: string };
  procedures: IProcedure[];
  customFields: ICustomAdditionalFieldObjectWithIndex[];
  serialNumber: string;
  facilityIdentifier: string;
  articleNumber: string;
  isQRCodeEnabled: Boolean;
  uuid: string;
  type: string;
  resource: {
    title: string;
    id: string;
    name: string;
    procedureId: string;
    state: string;
    status: string | IOemStatus;
    facility?: ICustomer;
  };
  ticket: { title: string; id: string };
  note: { message: string };
  oem: Array<{ _id: string; statuses: Array<{ label: string }> }>;
}

export interface IEnabledFeatures {
  teams: boolean;
  procedures: boolean;
}

export type ICsvRow = (string | number | undefined | null | IAddress)[];

export type DetailExportValidator = (_: any) => boolean;

export type DetailedExportType = typeof DETAIL_EXPORT[keyof typeof DETAIL_EXPORT];

export type OptionalDetailedExportType = DetailedExportType | "";

type DetailedExportDataTypeMap = {
  [K in keyof typeof DETAIL_EXPORT]: IAggregationData[typeof DETAIL_EXPORT[K]];
};

export type DetailedExportDataType<T extends keyof typeof DETAIL_EXPORT> =
  DetailedExportDataTypeMap[T];

export type DetailedExportDataItemType<T extends DetailedExportType> = Parameters<
  typeof DETAIL_EXPORT_VALIDATORS[T]
>[0];

export type AddFieldType = Record<string, AnyExpression>;

import { IUser } from "~/datamodels/User/interface";
import { InputQueryParams } from "~/graphql/types";
import { buildQueryParams } from "~/utils/queries";
import logger from "~/utils/logger";
import { getOemUserChannelId } from "~/utils/chat";
import { getDatedRandomStringForTempUrl } from "~/utils/_util-methods";
import { NOTIFICATION_IDENTIFIERS } from "~/constants/notification-identifiers";
import { prepareFilterQuery as assetPrepareFilterQuery } from "~/utils/assets";
import { prepareFilterQuery as ticketPrepareFilterQuery } from "~/utils/tickets/prepareQueryFilter";
import { prepareFilterQuery as customerPrepareFilterQuery } from "~/utils/customers";
import { prepareFilterQuery as inventoryPartPrepareFilterQuery } from "~/utils/parts";
import { uploadToS3 } from "~/datasources/db/_utils/_s3";
import {
  COLLECTIONS,
  CUSTOM_FIELD_TYPE,
  SORT,
  PROJECT,
  LOOKUP_AND_PROCESSING,
  EXTENDED_PIPELINE,
  CSV_FILE_NAME_PREFIX,
  EXTENDED_PIPELINE_DETAIL_EXPORT,
} from "~/datacomponents/Exporter/file-exporter/_consts";
import { generateCsv, getRows } from "~/datacomponents/Exporter/file-exporter/_helpers";
import { IOem } from "~/datamodels/Oem/interface";
import {
  IAggregationData,
  ICustomAdditionalFieldWithIndex,
  OptionalDetailedExportType,
} from "~/datacomponents/Exporter/file-exporter/_types";

const { TICKET, MACHINE, MACHINE_HISTORY, CUSTOMER, INVENTORY_PART } = COLLECTIONS;
const MACHINE_TEMPLATE = "MachineTemplate";

const placeholderFilterQuery = ({ args }: { args: { params: InputQueryParams } }) => ({
  filterQuery: {},
  query: { ...args },
});

const prepareFilterQuery: {
  [x: string]: any;
} = {
  [MACHINE]: assetPrepareFilterQuery,
  [TICKET]: ticketPrepareFilterQuery,
  [CUSTOMER]: customerPrepareFilterQuery,
  [INVENTORY_PART]: inventoryPartPrepareFilterQuery,
  [MACHINE_HISTORY]: placeholderFilterQuery,
  [MACHINE_TEMPLATE]: placeholderFilterQuery,
};

export const generateCsvAndNotify = async ({
  dataSources,
  args,
  user,
  resource,
  uuid,
  detailExport,
}: {
  dataSources: any;
  args: { params: InputQueryParams };
  user: IUser;
  resource: string;
  uuid: string;
  detailExport: OptionalDetailedExportType;
}) => {
  const publishPayload = {
    message: {
      text: NOTIFICATION_IDENTIFIERS.CSV_DOWNLOAD,
      success: true,
      payload: { user: user.id, uuid, resource, url: "" },
      error: "",
    },
    meta: { isCsvExport: true },
    channel: getOemUserChannelId(user.id),
  };
  try {
    const oem: IOem = await dataSources.Oem.getById(user.organization, {
      select: {
        "statuses.label": 1,
        "statuses._id": 1,
        ticketTypes: 1,
        paidFeatures: 1,
        assetTypes: 1,
      },
    });

    const { filterQuery, query } = await prepareFilterQuery[resource]({
      args,
      dataSources,
      user,
    });

    delete query?.params?.where?.uuid;

    const queryPayload = {
      ...(filterQuery?.length ? { $and: filterQuery } : {}),
      ...buildQueryParams(query),
      oem: user.organization,
      sort: SORT[resource],
    };

    const { $match, $sort } = dataSources[resource].getAggergationOptions(queryPayload);

    const historyAggregateQuery = [
      { $match },
      ...LOOKUP_AND_PROCESSING(detailExport)[resource],
      { $project: PROJECT(detailExport)[resource] },
      ...(EXTENDED_PIPELINE[resource] ?? []),
      ...(EXTENDED_PIPELINE_DETAIL_EXPORT[detailExport] ?? []),
      { $sort },
    ];

    const [customAdditionalFields, data]: [ICustomAdditionalFieldWithIndex[], IAggregationData[]] =
      await Promise.all([
        dataSources.CustomAdditionalField.getMany(
          {
            oem: user.organization,
            type: CUSTOM_FIELD_TYPE[resource],
            ...(resource === CUSTOMER ? {} : { enabled: true }), // need facility address even if it's disabled
            sort: ["order:asc"],
          },
          { select: { label: 1 } },
        ),
        dataSources[resource].aggregate(historyAggregateQuery),
      ]);

    const rows = await getRows({
      oem,
      data,
      resource,
      customAdditionalFields,
      detailExport,
      dataSources,
    });
    const csv = await generateCsv(rows);

    const getCsvFileNamePrefix = async () => {
      if (resource !== MACHINE_HISTORY) return CSV_FILE_NAME_PREFIX[resource];
      const machine = await dataSources.Machine.loadOne(args.params.where.machine);
      return `${machine.name}-${machine.serialNumber}`;
    };
    const url = await uploadToS3({
      bufferData: csv,
      uploadPath: `downloads/oem/${
        user.organization
      }/${await getCsvFileNamePrefix()}-${getDatedRandomStringForTempUrl()}.csv`,
    });

    publishPayload.message.payload.url = url;

    dataSources.PubnubApi.publishMessage(publishPayload);
  } catch (e) {
    logger.error("generateCsvAndNotify", e);
    publishPayload.message.success = false;
    publishPayload.message.error = e?.toString() as string;
    dataSources.PubnubApi.publishMessage(publishPayload);
  }
};

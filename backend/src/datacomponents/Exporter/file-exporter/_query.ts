import { roles } from "~/directives";
import { IUser } from "~/datamodels/User/interface";
import { InputQueryParams } from "~/graphql/types";

import { generateCsvAndNotify } from "~/datacomponents/Exporter/file-exporter/_exporter";
import { validateGetDataCsvParams } from "~/datacomponents/Exporter/file-exporter/_helpers";

const { technician } = roles.is;

type IContext = {
  dataSources: any;
  user: IUser;
};

export const queryTypes = `#graphql
  type Query {
    getDataCsv(params: InputQueryParams): Boolean @${technician}
  }
`;

export const queryResolvers = {
  Query: {
    getDataCsv: async (
      _: any,
      args: { params: InputQueryParams },
      { dataSources, user }: IContext,
    ) => {
      const { where } = args.params || {};
      const { resource, uuid, detailExport = "" } = where || {};

      validateGetDataCsvParams(where);

      generateCsvAndNotify({
        dataSources,
        args,
        user,
        resource,
        uuid,
        detailExport,
      });

      return true;
    },
  },
};

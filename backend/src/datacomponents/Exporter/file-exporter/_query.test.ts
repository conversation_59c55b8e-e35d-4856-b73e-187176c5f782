import { describe, afterEach, expect, test, vi } from "vitest";
import { queryResolvers } from "./_query";
import { IUser } from "~/datamodels/User/interface";

import { generateCsvAndNotify } from "./_exporter";
import { validateGetDataCsvParams } from "./_helpers";

vi.mock("./_exporter", async () => ({
  generateCsvAndNotify: vi.fn(),
}));

vi.mock("./_helpers", async () => ({
  validateGetDataCsvParams: vi.fn(),
}));
const { Query } = queryResolvers;

const _id = "64d1db10aa1d4f0613a9d7d7";

const user: IUser = {
  // @ts-ignore
  organization: _id,
  username: "User",
};

const dataSources = {
  Machine: {
    getMany: vi.fn(),
  },
};

describe("Query Exporter ", () => {
  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe("getDataCsv", async () => {
    const resource = "Machine";
    const uuid = "19ed93db-5c20-4dc1-9296-4f6a6a3297a5";
    const detailExport = "";
    const args = {
      params: {
        where: {
          resource,
          uuid,
          detailExport,
        },
      },
    };
    test("success", async () => {
      const result = await Query.getDataCsv(
        null,
        // @ts-ignore
        args,
        {
          dataSources,
          user,
        },
      );
      expect(validateGetDataCsvParams).toHaveBeenCalledOnce();
      expect(generateCsvAndNotify).toHaveBeenCalledOnce();
      expect(generateCsvAndNotify).toHaveBeenCalledWith({
        dataSources,
        args,
        user,
        resource,
        uuid,
        detailExport,
      });
      expect(result).toBe(true);
    });
  });
});

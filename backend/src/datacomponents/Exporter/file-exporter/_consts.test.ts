import { describe, expect, test } from "vitest";

import {
  COLLECTIONS,
  CUSTOM_FIELD_TYPE,
  ROW_HEADERS,
  SORT,
  PROJECT,
  LOOKUP_AND_PROCESSING,
  <PERSON><PERSON><PERSON><PERSON>_MACHINE,
  <PERSON><PERSON><PERSON><PERSON>_TEAMS,
  <PERSON><PERSON><PERSON><PERSON>_FACILITY,
  <PERSON>O<PERSON>UP_ASSIGNEES,
  <PERSON>O<PERSON>UP_REPORTER,
  LOOKUP_CUSTOMER_MACHINES,
  ADD_FIRST_MACHINE,
  ADD_FIRST_FACILITY,
  ADD_FIRST_USER,
  ADD_FIELD_MACHINE,
  NO_OP_STAGE,
  ADD_FIELD_FACILITY,
  ADD_FIELD_TEAMS,
  ADD_FIELD_REPORTER,
  ADD_FIELD_ASSIGNEES,
  ADD_FIELD_COUNT_PRODCEDURES_ATTACHED,
  ADD_FIELD_COUNT_MACHINES_ATTACHED,
  ADD_FIELD_COUNT_PARTS_ATTACHED,
  DATE_FORMAT_CREATED_AT,
  DATE_FORMAT_SCHEDULING,
  <PERSON><PERSON><PERSON><PERSON>_OEM,
} from "~/datacomponents/Exporter/file-exporter/_consts";

const lookupAndProcessing = (detailExport = "") => LOOKUP_AND_PROCESSING(detailExport as "");
const project = (detailExport = "") => PROJECT(detailExport as "");

describe("Exporter _consts.ts", () => {
  describe("Constants", () => {
    test("DATE_FORMAT_CREATED_AT correct format", () => {
      expect(DATE_FORMAT_CREATED_AT).toEqual("YYYY-MM-DD");
    });
    test("DATE_FORMAT_CREATED_AT correct format", () => {
      expect(DATE_FORMAT_SCHEDULING).toEqual("YYYY-MM-DD hh:mm:ss A");
    });
    test("COLLECTIONS should contain specific collections", () => {
      expect(COLLECTIONS).toBeDefined();
      expect(COLLECTIONS.TICKET).toBe("Ticket");
      expect(COLLECTIONS.CUSTOMER).toBe("Customer");
      expect(COLLECTIONS.USER).toBe("User");
      expect(COLLECTIONS.TEAM).toBe("Team");
      expect(COLLECTIONS.PREVENTIVE_MAINTENANCE).toBe("PreventiveMaintenance");
      expect(COLLECTIONS.INVENTORY_PART).toBe("InventoryPart");
    });

    test("CUSTOM_FIELD_TYPE should map correctly", () => {
      expect(CUSTOM_FIELD_TYPE).toBeDefined();
      expect(Object.keys(CUSTOM_FIELD_TYPE)).toEqual(
        expect.arrayContaining(["Machine", "Ticket", "Customer", "InventoryPart"]),
      );
    });

    test("ROW_HEADERS should have correct structure", () => {
      expect(ROW_HEADERS).toBeDefined();
      expect(Object.keys(ROW_HEADERS)).toEqual(
        expect.arrayContaining(["Ticket", "Machine", "Customer", "InventoryPart"]),
      );
    });

    test("SORT should have sorting criteria for each collection", () => {
      expect(SORT).toBeDefined();
      expect(Object.keys(SORT)).toEqual(
        expect.arrayContaining(["Ticket", "Machine", "Customer", "InventoryPart"]),
      );
    });

    test("PROJECT should have project definitions for each collection", () => {
      expect(project()).toBeDefined();
      expect(Object.keys(project())).toEqual(
        expect.arrayContaining(["Ticket", "Machine", "Customer", "InventoryPart"]),
      );
    });
  });

  describe("ROW_HEADERS", () => {
    test("ROW_HEADERS should have correct headers for `Ticket` collection", () => {
      expect(ROW_HEADERS).toHaveProperty("Ticket");
      expect(ROW_HEADERS.Ticket).toEqual([
        "#",
        "ID",
        "Title",
        "Type",
        "Visibility",
        "Reporter",
        "Status",
        "Scheduled From (UTC)",
        "Scheduled To (UTC)",
        "Created Date",
        "Assignees",
        "Connection ID",
        "Connection Name",
        "Asset Serial Number",
        "Asset Name",
        "Created Time (UTC)",
        "Resolution Time (h:m)",
      ]);
    });

    test("ROW_HEADERS should have correct headers for `Customer` collection", () => {
      expect(ROW_HEADERS).toHaveProperty("Customer");
      expect(ROW_HEADERS.Customer).toEqual(["#", "ID", "Name", "Number of Assets"]);
    });

    test("ROW_HEADERS should have correct headers for `InventoryPart` collection", () => {
      expect(ROW_HEADERS).toHaveProperty("InventoryPart");
      expect(ROW_HEADERS.InventoryPart).toEqual(["#", "Article Number", "Name"]);
    });
  });

  describe("PROJECT", () => {
    test("PROJECT should have correct configuration for `Ticket` collection", () => {
      expect(project()).toHaveProperty("Ticket");
      expect(project().Ticket).toMatchObject({
        name: 1,
        teams: 1,
        created_at: 1,
        customFields: 1,
        id: 1,
        title: 1,
        status: 1,
        machine: 1,
        facility: 1,
        user: 1,
        assignees: 1,
        ticketType: 1,
        serialNumber: 1,
        facilityId: 1,
        "schedule.startTime": 1,
        "schedule.endTime": 1,
        countPartsAttached: 1,
        countProceduresAttached: 1,
      });
    });

    test("PROJECT should have correct configuration for `Customer` collection", () => {
      expect(project()).toHaveProperty("Customer");
      expect(project().Customer).toMatchObject({
        name: 1,
        teams: 1,
        created_at: 1,
        customFields: 1,
        facilityIdentifier: 1,
        countMachinesAttached: 1,
      });
    });

    test("PROJECT should have correct configuration for `InventoryPart` collection", () => {
      expect(project()).toHaveProperty("InventoryPart");
      expect(project().InventoryPart).toMatchObject({
        name: 1,
        teams: 1,
        created_at: 1,
        customFields: 1,
        articleNumber: 1,
        countPartsAttached: 1,
      });
    });
  });

  describe("LOOKUP_AND_PROCESSING", () => {
    test("LOOKUP_AND_PROCESSING should have correct pipeline for `Ticket` collection", () => {
      expect(lookupAndProcessing()).toHaveProperty("Ticket");
      expect(lookupAndProcessing().Ticket).toEqual([
        LOOKUP_MACHINE,
        LOOKUP_TEAMS,
        LOOKUP_FACILITY,
        LOOKUP_ASSIGNEES,
        LOOKUP_REPORTER,
        LOOKUP_OEM,
        NO_OP_STAGE,
        {
          $addFields: {
            ...ADD_FIRST_MACHINE,
            ...ADD_FIRST_FACILITY,
            ...ADD_FIRST_USER,
          },
        },
        {
          $addFields: {
            ...ADD_FIELD_MACHINE,
            ...ADD_FIELD_FACILITY,
            ...ADD_FIELD_TEAMS,
            ...{},
            ...ADD_FIELD_REPORTER,
            ...ADD_FIELD_ASSIGNEES,
            ...ADD_FIELD_COUNT_PARTS_ATTACHED,
            ...ADD_FIELD_COUNT_PRODCEDURES_ATTACHED,
          },
        },
      ]);
    });

    test("LOOKUP_AND_PROCESSING should have correct pipeline for `Customer` collection", () => {
      expect(lookupAndProcessing()).toHaveProperty("Customer");

      expect(lookupAndProcessing().Customer).toEqual([
        LOOKUP_CUSTOMER_MACHINES,
        LOOKUP_TEAMS,
        {
          $addFields: {
            ...ADD_FIELD_COUNT_MACHINES_ATTACHED,
            ...ADD_FIELD_TEAMS,
          },
        },
      ]);
    });

    test("LOOKUP_AND_PROCESSING should have empty pipeline for `InventoryPart` collection", () => {
      expect(lookupAndProcessing()).toHaveProperty("InventoryPart");
      expect(lookupAndProcessing().InventoryPart).toEqual([]);
    });
  });
});

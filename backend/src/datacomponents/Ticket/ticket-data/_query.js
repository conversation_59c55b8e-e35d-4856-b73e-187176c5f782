import to from "await-to-js";
import { saveOem } from "~/services/oem/utils";
import { roles, features } from "~/directives";
import { chat } from "~/config";
import BoardFor from "$/settings/enums/kanban/_boardFor.json";
import BoardTypes from "$/settings/enums/kanban/_type.json";
import {
  maxWaitTimePromise,
  getOemChannelGroupName,
  getFacilityChannelGroupName,
  buildQueryParams,
  throwIfError,
  belongsToUserTeams,
  getEnums,
} from "~/utils";
import { sendPDFUrl } from "~/utils/_sendPdfUrl";
import {
  getAllAssetHierarchyTickets,
  getAllTickets,
  getDuplicateTickets,
  getGroupedTickets,
  getTicket,
  getTicketChatMentionableUsers,
  getCustomersWithTickets,
} from "~/services/ticket/fetch";
import { prepareFilterQuery } from "~/utils/tickets/prepareQueryFilter";
import { TEMPLATE_TYPES } from "~/templates/procedures/partials/constants";
import { populateTicketForPDFExport } from "~/utils/tickets";
import { getAllRequests, getRequest as _getRequest } from "~/services/request/fetch";

const boardForEnum = getEnums(BoardFor, "reference");
const boardTypesEnum = getEnums(BoardTypes, "reference");
const { workManagement } = features.oemOwns;

export const queryTypes = `#graphql
  type Query {
    listOwnOemTickets(params: InputQueryParams): PaginatedTickets @${workManagement} @${roles.is.technician}
    listRequests(params: InputQueryParams): PaginatedRequests @${roles.is.technician}
    listOwnCustomerTickets(params: InputQueryParams): PaginatedTickets @${workManagement} @${roles.is.gmu}
    listOwnOemKanbanTickets(params: InputQueryParams): PaginatedKanbanTickets @${workManagement} @${roles.is.technician}
    listOwnFacilityOpenTickets: [Ticket] @${roles.is.gmu}
    listOwnFacilityClosedTickets: [Ticket] @${roles.is.gmu}
    tickets(params: InputQueryParams!): [Ticket] @${roles.is.admin}
    listTicketTypes: [TicketType] @${roles.is.gmu}
    totalTickets(params: InputQueryParams): Int @${roles.is.admin}
    getOwnFacilityTicketById(id: ID!): Ticket @${roles.is.gmu}
    getOwnOemTicketById(id: ID!): Ticket @${workManagement} @${roles.is.technician}
    getRequest(requestId: ID!): Ticket @${roles.is.technician}
    listOwnOemClosedTickets: [Ticket] @${roles.is.technician}
    listOwnOemOpenTickets(params: InputQueryParams): [Ticket] @${roles.is.technician}
    listOwnOemAllTickets(params: InputQueryParams): [Ticket] @${roles.is.technician}
    listOwnOemUserClosedTickets: [Ticket] @${roles.is.technician}
    listOwnOemUserOpenTickets: [Ticket] @${roles.is.technician}
    listOwnOemDuplicateTickets(ticketIds: [String!]!): [String] @${roles.is.oem}
    checkTicketHasStatus(statusId: ID!): Boolean @${workManagement} @${roles.is.technician}
    checkIfTicketsExistForType(ticketTypeId: ID!): Boolean @${roles.is.technician}
    listOemCustomersInArea(params: InputQueryParams): [CustomerMarker] @${roles.is.technician}
    listGroupedByCustomersTickets(params: InputQueryParams): [GroupedTickets] @${roles.is.technician}
    listCustomersWithTickets(params: InputQueryParams): [String] @${roles.is.technician}
    listOwnTicketMentionableUsers(ticketId: ID!): TicketMentionUsers @${roles.is.technician}
    downloadWOPDF(input: InputDownloadWOPDF): String @${roles.is.technician}
    listOwnAssetHierarchyTickets(params: InputQueryParams): PaginatedTickets @${workManagement} @${roles.is.technician}
  }
`;

export const queryResolvers = {
  Query: {
    listOwnOemOpenTickets: async (_, args, { dataSources, user }) => {
      const oem = await dataSources.Oem.loadOne(user.organization);
      const closedStatus = oem?.statuses?.[oem.statuses.length - 1];
      const { filterQuery, query } = await prepareFilterQuery({
        args,
        dataSources,
        closedStatus,
        user,
      });

      const tickets = await dataSources.Ticket.getMany({
        ...(filterQuery?.length ? { $and: filterQuery } : {}),
        ...buildQueryParams(query),
        oem: user.organization,
        sort: ["lastUpdatedAt:desc"],
      });

      return tickets;
    },

    listOwnOemTickets: async (_, args, { dataSources, user }) =>
      getAllTickets({ args, dataSources, user }),
    listOemCustomersInArea: async (_, args, { dataSources, user }) => {
      const { where } = args?.params || {};
      const { coordinates } = where || {};

      if (!Array.isArray(coordinates) || coordinates.length !== 5) {
        return throwIfError("Missing valid set of coordinates");
      }

      const oem = await dataSources.Oem.loadOne(user.organization);
      const closedStatus = oem?.statuses?.[oem.statuses.length - 1];
      const { filterQuery, query } = await prepareFilterQuery({
        args,
        dataSources,
        closedStatus,
        user,
      });

      const customerIds = await dataSources.Ticket.getDistinct("facility", {
        ...(filterQuery?.length ? { $and: filterQuery } : {}),
        ...buildQueryParams(query),
        oem: user.organization,
        deleted: false,
      });

      const customers = await dataSources.Customer.getMany(
        {
          _id: { $in: customerIds },
        },
        { select: { customerAddress: 1 } },
      );

      return customers;
    },
    listGroupedByCustomersTickets: async (_, args, { dataSources, user }) =>
      await getGroupedTickets({
        args,
        dataSources,
        user,
      }),
    listCustomersWithTickets: async (_, args, { dataSources, user }) =>
      await getCustomersWithTickets({
        args,
        dataSources,
        user,
      }),
    listOwnOemKanbanTickets: async (_, args, { dataSources, user }) => {
      const sort = args.params?.sort;

      const { filterQuery, query, kanbanBoard } = await prepareFilterQuery({
        args,
        dataSources,
        user,
      });

      const queryPayload = {
        ...(filterQuery?.length ? { $and: filterQuery } : {}),
        ...buildQueryParams(query),
        oem: user.organization,
        sort: sort || ["lastUpdatedAt:desc", "_id:asc"],
      };

      const kanbanColumnQueries = [];
      const columns = [];
      const oemStatuses = [];
      let totalTickets = 0;
      let index = 0;

      const [oem, singleSelectCustomField] = await Promise.all([
        dataSources.Oem.getById(user.organization),
        dataSources.CustomAdditionalField.getOne({
          _id: kanbanBoard,
          oem: user.organization,
          fieldType: "singleSelect",
          deleted: false,
        }),
      ]);

      if (kanbanBoard) {
        if (!singleSelectCustomField?.options?.length) return { totalCount: 0, columns: [] };

        singleSelectCustomField.options.forEach(column => {
          const q = {
            ...queryPayload,
            $and: [
              ...(queryPayload.$and ? queryPayload.$and : []),
              {
                customFields: {
                  $elemMatch: {
                    fieldId: kanbanBoard,
                    value: column.value,
                  },
                },
              },
            ],
          };
          kanbanColumnQueries.push(dataSources.Ticket.getMany(q));
          kanbanColumnQueries.push(
            dataSources.Ticket.totalCount({
              ...q,
              limit: -1,
              skip: 0,
            }),
          );
        });
      }
      // the default is ticket statuses
      else {
        if (!oem || !oem?.statuses?.length) return { totalCount: 0, columns: [] };

        oem.statuses.forEach(status => {
          const statusId = status?._id?.toString();
          const q = {
            ...queryPayload,
            $and: [
              ...(queryPayload.$and ? queryPayload.$and : []),
              {
                status: statusId,
              },
            ],
          };
          kanbanColumnQueries.push(dataSources.Ticket.getMany(q));
          kanbanColumnQueries.push(
            dataSources.Ticket.totalCount({
              ...q,
              limit: -1,
              skip: 0,
            }),
          );
          oemStatuses.push(status);
        });
      }

      const kanbanColumnResults = await Promise.all(kanbanColumnQueries);

      for (let i = 0; i < kanbanColumnResults.length; i += 1) {
        // count result is at alternate indexes
        if (i % 2 === 1) {
          totalTickets += kanbanColumnResults[i];
        } else {
          const column = kanbanBoard ? singleSelectCustomField.options[i / 2] : oem.statuses[i / 2];
          columns.push({
            _id: index + 1,
            boardFor: boardForEnum.ticket,
            type: kanbanBoard ? boardTypesEnum.custom_field : boardTypesEnum.status,
            status: kanbanBoard ? {} : oemStatuses[i / 2],
            customField: kanbanBoard ? singleSelectCustomField?._id : "",
            column: kanbanBoard ? column.value : column.label,
            columnIndex: index,
            cards: kanbanColumnResults[i],
            totalCount: kanbanColumnResults[i + 1],
            limit: args.params.limit,
            skip: args.params.skip,
            currentPage: args.params.skip / args.params.limit + 1,
          });
          index += 1;
        }
      }

      return {
        totalCount: totalTickets,
        columns,
      };
    },

    listOwnOemAllTickets: async (_, args, { dataSources, user }) => {
      try {
        const oem = await dataSources.Oem.loadOne(user.organization);
        const closedStatus = oem?.statuses?.[oem.statuses.length - 1];

        const { filterQuery, query } = await prepareFilterQuery({
          args,
          dataSources,
          closedStatus,
          skipClosedTickets: false,
          user,
        });

        // Here this added for old users and backward competability should remove in future
        const tickets = await dataSources.Ticket.getMany({
          ...(filterQuery?.length ? { $and: filterQuery } : {}),
          ...buildQueryParams(query),
          oem: user.organization,
          sort: ["lastUpdatedAt:desc"],
        });

        // Here this added for old users and backward competability should remove in future
        if (!oem?.channelsAddedInGroup) {
          const channels = tickets?.map(
            ticket => `channel-${ticket.oem}-${ticket.facility}-${ticket._id}`,
          );

          const oemChannelsGroup = getOemChannelGroupName(oem._id);
          // ticket channels to oem group

          const slicedChan = [];
          while (channels?.length > 0) {
            let curLen = channels.length;
            if (curLen > 100) curLen = 100;

            slicedChan.push(channels.splice(0, curLen));
          }

          for (const chanSliced of slicedChan) {
            await Promise.race([
              maxWaitTimePromise(),
              chat.channelGroups.addChannels({
                channels: chanSliced,
                channelGroup: oemChannelsGroup,
              }),
            ]);
          }

          const ticketGroupByFacility = tickets.reduce((acc, item) => {
            if (acc[item.facility?.toString()]) {
              const ch = `channel-${item.oem?.toString()}-${item.facility?.toString()}-${item._id?.toString()}`;
              acc[item.facility?.toString()].push(ch);
            }
            if (!acc[item.facility?.toString()]) {
              const ch = `channel-${item.oem?.toString()}-${item.facility?.toString()}-${item._id?.toString()}`;
              acc[item.facility?.toString()] = [ch];
            }
            return acc;
          }, {});

          for (const facility of Object.keys(ticketGroupByFacility)) {
            const facilityChannelsGroup = getFacilityChannelGroupName(facility);
            await Promise.race([
              maxWaitTimePromise(),
              chat.channelGroups.addChannels({
                channels: ticketGroupByFacility[facility],
                channelGroup: facilityChannelsGroup,
              }),
            ]);
          }

          // const r = await chat.channelGroups.listChannels({
          //   channelGroup: channelsGroup,
          // });

          // const rd = await chat.channelGroups.removeChannels({
          //   channelGroup: channelsGroup,
          //   channels: r.channels,
          // });

          // Update channelsAddedInGroup to true after adding all tickets channels to oem channel group so that we can listen for channelgroup
          await saveOem({
            args: {
              input: {
                _id: oem._id,
                channelsAddedInGroup: true,
              },
            },
            dataSources,
            user,
          });
        }

        return tickets;
      } catch (err) {
        return throwIfError(err);
      }
    },
    listOwnOemClosedTickets: async (_, __, { dataSources, user }) => {
      const oem = await dataSources.Oem.loadOne(user.organization);
      const closedStatusId = oem?.statuses?.[oem.statuses.length - 1]?._id;

      dataSources.Ticket.getMany({
        status: closedStatusId,
        oem: user.organization,
        ...belongsToUserTeams(user),
        sort: ["lastUpdatedAt:desc"],
      });
    },
    listOwnOemUserOpenTickets: async (_, __, { dataSources, user }) => {
      if (!user?.id) return null;
      const oem = await dataSources.Oem.loadOne(user.organization);
      const closedStatusId = oem?.statuses?.[oem.statuses.length - 1]?._id;

      return dataSources.Ticket.getMany({
        $and: [
          { status: { $exists: true } },
          { status: { $ne: closedStatusId } }, // any status other than closed
        ],
        $or: [{ assignee: user?.id }, { assignees: user?.id }, { followers: user?.id }],
        oem: user.organization,
        ...belongsToUserTeams(user),
        sort: ["lastUpdatedAt:desc"],
      });
    },
    listOwnOemUserClosedTickets: async (_, __, { dataSources, user }) => {
      if (!user.id) return null;
      const oem = await dataSources.Oem.loadOne(user.organization);
      const closedStatusId = oem?.statuses?.[oem.statuses.length - 1]?._id;

      return dataSources.Ticket.getMany({
        status: closedStatusId,
        oem: user.organization,
        $or: [{ assignee: user?.id }, { assignees: user?.id }, { followers: user?.id }],
        ...belongsToUserTeams(user),
        sort: ["lastUpdatedAt:desc"],
      });
    },
    listOwnAssetHierarchyTickets: async (_, args, { dataSources, user }) =>
      getAllAssetHierarchyTickets({ args, dataSources, user }),
    getOwnFacilityTicketById: async (_, args, { dataSources, user }) => {
      const { id } = args;
      const customer = await dataSources.Customer.getById(user.organization);
      if (!customer) return throwIfError("Customer not found");
      const oem = await dataSources.Oem.getById(customer.oem);
      if (!oem) return throwIfError("Oem not found");

      const externalWorkOrderTypeIds = oem.ticketTypes
        ? oem.ticketTypes.filter(tt => !tt.isInternal).map(tt => tt._id)
        : [];

      if (!id) return null;
      return dataSources.Ticket.getOne({
        _id: id,
        facility: customer._id,
        oem: oem._id,
        ticketType: { $in: externalWorkOrderTypeIds },
      });
    },
    getOwnOemTicketById: async (_, args, { dataSources, user }) =>
      getTicket({
        args: { input: { id: args.id }, files: null, headers: {}, query: {}, params: {} },
        dataSources,
        user,
      }),
    getRequest: async (_, args, { dataSources, user }) =>
      _getRequest({
        args: {
          input: { requestId: args.requestId },
          files: null,
          headers: {},
          query: {},
          params: {},
        },
        dataSources,
        user,
      }),
    totalTickets: async (_, args, { dataSources, user }) => {
      const { params } = args || {};
      if (!params) return null;

      const tickets = await dataSources.Ticket.getMany({
        oem: user.organization,
        ...buildQueryParams({
          ...args,
          params: {
            ...args.params,
            skip: 0,
            limit: -1,
          },
        }),
        ...belongsToUserTeams(user),
      });
      return tickets?.length;
    },
    tickets: (_, args, { dataSources, user }) => {
      const { params } = args || {};
      if (!params) return null;

      return dataSources.Ticket.getMany({
        oem: user.organization,
        ...buildQueryParams(args),
        ...belongsToUserTeams(user),
      });
    },
    listOwnCustomerTickets: async (_, args, { dataSources, user }) => {
      const query = { ...args };
      const { skip, limit } = args.params || {};

      const customer = await dataSources.Customer.getById(user.organization);
      if (!customer) return throwIfError("Customer not found");
      const oem = await dataSources.Oem.getById(customer.oem);
      if (!oem) return throwIfError("Oem not found");

      const externalWorkOrderTypeIds = oem.ticketTypes
        ? oem.ticketTypes.filter(tt => !tt.isInternal).map(tt => tt._id)
        : [];

      const closedStatusId = oem?.statuses?.[oem.statuses.length - 1]?._id?.toString();

      let workOrderStatusQuery = { status: { $ne: closedStatusId } };
      if (query?.params?.where?.myClosedWorkOrders) {
        workOrderStatusQuery = {
          status: closedStatusId,
        };

        delete query?.params?.where?.myClosedWorkOrders;
      }

      const queryPayload = {
        status: { $exists: true },
        ...buildQueryParams(query),
        facility: customer._id,
        oem: oem._id,
        sort: ["lastUpdatedAt:desc", "_id:asc"],
        ticketType: { $in: externalWorkOrderTypeIds },
        ...workOrderStatusQuery,
      };
      const [tickets, totalCount] = await Promise.all([
        dataSources.Ticket.getMany(queryPayload),
        dataSources.Ticket.totalCount({
          ...queryPayload,
          limit: -1,
          skip: 0,
        }),
      ]);

      return {
        tickets,
        limit,
        skip,
        currentPage: skip && limit ? Math.floor(skip / limit + 1) : 1,
        totalCount,
      };
    },
    listOwnFacilityOpenTickets: async (_, __, { dataSources, user }) => {
      const [userError, currentUser] = await to(dataSources.User.loadOne(user.id));
      throwIfError(userError);

      const [err, oem] = await to(dataSources.Oem.loadOne(currentUser.oem));
      throwIfError(err);

      const internalTicketTypeIds = oem.ticketTypes
        .filter(ticketType => ticketType.isInternal)
        .map(ticketType => ticketType._id);

      // in custom status, closed is the last element.
      const closedStatusId = oem.statuses?.[oem.statuses.length - 1]?._id;

      return dataSources.Ticket.getMany({
        $and: [
          { status: { $exists: true } },
          { status: { $ne: closedStatusId.toString() } }, // any status other than closed
          { ticketType: { $nin: internalTicketTypeIds } },
        ],
        facility: user.organization,
        sort: ["lastUpdatedAt:desc"],
      });
    },
    listOwnFacilityClosedTickets: async (_, __, { dataSources, user }) => {
      const [userError, currentUser] = await to(dataSources.User.loadOne(user.id));
      throwIfError(userError);

      const [err, oem] = await to(dataSources.Oem.loadOne(currentUser.oem));
      throwIfError(err);

      const internalTicketTypeIds = oem.ticketTypes
        .filter(ticketType => ticketType.isInternal)
        .map(ticketType => ticketType._id);

      // in custom status, closed is the last element.
      const closedStatusId = oem.statuses?.[oem.statuses.length - 1]?._id;

      return dataSources.Ticket.getMany({
        status: closedStatusId.toString(),
        facility: user.organization,
        ticketType: { $nin: internalTicketTypeIds },
        sort: ["lastUpdatedAt:desc"],
      });
    },
    listTicketTypes: async (_, __, { dataSources, user }) => {
      const oem = await dataSources.Oem.loadOne(user.organization);

      const ticketTypes = Array.isArray(oem.ticketTypes) ? oem.ticketTypes : [];

      const externalTicketTypes = ticketTypes.filter(ticketType => !ticketType.isInternal);

      return externalTicketTypes;
    },
    checkTicketHasStatus: async (_, args, { dataSources, user }) => {
      const { statusId } = args;

      if (!statusId) return null;

      const tickets = await dataSources.Ticket.getMany({
        status: statusId,
        oem: user.organization,
        ...belongsToUserTeams(user),
      });

      return !!tickets?.length;
    },
    checkIfTicketsExistForType: async (_, args, { dataSources, user }) => {
      const { ticketTypeId } = args;
      if (!ticketTypeId) return null;

      const ticketsCount = await dataSources.Ticket.totalCount({
        ticketType: ticketTypeId,
        oem: user.organization,
      });

      return !!ticketsCount;
    },
    listOwnTicketMentionableUsers: async (_, args, { dataSources, user, req: { t: translate } }) =>
      await getTicketChatMentionableUsers({
        args: {
          input: { ticketId: args.ticketId },
          files: null,
          headers: {},
          query: {},
          params: {},
        },
        user,
        dataSources,
        translate,
      }),

    listOwnOemDuplicateTickets: async (_, args, { dataSources, user, req: { t: translate } }) =>
      await getDuplicateTickets({
        args: {
          input: { ticketIds: args.ticketIds },
          files: null,
          headers: {},
          query: {},
          params: {},
        },
        user,
        dataSources,
        translate,
      }),

    downloadWOPDF: async (_, { input }, { dataSources, user, req }) => {
      const { id, uuid, sectionsToShow, procedureIdsToInclude, timezone } = input || {};
      const { t: translate } = req || {};

      if (!id) return null;
      const [oem, ticket] = await Promise.all([
        dataSources.Oem.loadOne(user.organization),
        dataSources.Ticket.getOne({ _id: id }),
      ]);

      if (!ticket) return throwIfError(translate("work_order.not_found"));

      const language = translate("work_order_pdf", { returnObjects: true }) || {};

      const finalTicket = await populateTicketForPDFExport({
        dataSources,
        language,
        procedureIdsToInclude,
        sectionsToShow,
        ticket,
        timezone,
      });

      sendPDFUrl({
        data: finalTicket,
        oem,
        userId: user.id,
        dataSources,
        uuid,
        language,
        templateType: TEMPLATE_TYPES.WORK_ORDER,
        timezone,
      });

      return "preparing pdf...";
    },
    listRequests: async (_, args, { dataSources, user }) =>
      getAllRequests({ args, dataSources, user }),
  },
};

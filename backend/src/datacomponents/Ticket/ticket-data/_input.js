export default `#graphql
  input InputCreateOwnTicket {
    machineId: ID!
    title: String!
    description: String
    ticketType: ID!
    inventoryParts: [InputTicketMachinePart]
    customFields: [InputCustomField]
  }

  input InputTicketMachinePart {
    part: ID!
    addedBy: ID!
    quantity: Int!
  }

  input InputCreateRequest {
    title: String!
    assetId: ID!
    ticketType: ID!
    description: String
    inventoryParts: [InputTicketMachinePart]
    customFields: [InputCustomField]
  }

  input InputCreateOwnOemTicket {
    machineId: ID!
    userId: ID
    customerId: ID!
    title: String!
    ticketType: ID!
    description: String
    inventoryParts: [InputTicketMachinePart]
    threadId: String
    customFields: [InputCustomField]
  }

  input InputDeleteOwnOemTicket {
    ticketId: ID!
    reason: String
  }

  input ScheduleInputType {
    startTime: DateTime
    endTime: DateTime
    startTimezone: String
    endTimezone: String
    isAllDay: Boolean
    isTimezoneEnabled: Boolean
  }

  input InputUpdateOwnOemTicket {
    ticketId: ID!
    status: ID
    user: ID
    notes: String    
    assignees: [ID]    
    facility: ID
    """
    TODO: Remove unread
    """
    unread: Boolean
    customFields: [InputCustomField]
    schedule: ScheduleInputType
    ticketType: ID
  }

  input InputAddTicketAssignee {
    ticketId: ID!    
    assignee: ID    
  }

  input InputAddTicketAttachment {
    name: String!
    type: String!
    url: String!
    size: Int!
  }

  input InputRemoveTicketAttachment {
    attachmentId: String!
    ticketId: ID!
  }

  input InputAddTicketAttachments {
    ticketId: ID!
    attachments: [InputAddTicketAttachment]
  }

  input InputRemoveTicketAssignee {
    ticketId: ID!
    assignee: ID
    removeAll: Boolean
  }

  input InputAddTicketFollower {
    ticketId: ID!
    follower: ID
  }

  input InputRemoveTicketFollower {
    ticketId: ID!
    follower: ID
    removeAll: Boolean
  }

  input InputNotifyOnInternalNotePost {
    ticketId: ID!
    mentionedUsers: [ID]
    message: String
  }

  input InputTicketInventoryPartPayload {
    part: ID!
    quantity: Int!
  }

  input InputAssignInventoryPartsToTicket {
    inventoryParts: [InputTicketInventoryPartPayload!]!
    ticketId: ID!
  }

  input InputRemoveInventoryPartFromTicket {
    ticketId: ID!
    partId: ID!
  }

  input InputAddTimeTrackerLogPayload {
    description: String
    startDateTime: DateTime!
    endDateTime: DateTime!
    timeElapsedInSeconds: Int!
    isBillable: Boolean!
    ticketTag: ID!
  }

  input InputAddTimeTrackerLog {
    ticketId: ID!
    timeTrackerLog: InputAddTimeTrackerLogPayload!
  }

  input InputUpdateTimeTrackerLog {
    ticketId: ID!
    timeLogId: ID!
    timeTrackerLog: InputAddTimeTrackerLogPayload
  }

  input InputRemoveTimeTrackerLog {
    ticketId: ID!
    timeLogId: ID!
  }

  input InputUpdateTitleTicket {
    ticketId: ID!
    title: String!
  }

  input InputUpdateDescriptionTicket {
    ticketId: ID!
    description: String!
  }

  input InputAddTicketResource {
    ticketId: ID!
    resource: ID!
  }

  input InputRemoveTicketResources {
    ticketId: ID!
    resources: [ID!]!
  }

  input InputDownloadWOPDF {
    id: String!
    procedureIdsToInclude: [String]
    sectionsToShow: [String]
    uuid: String!
    timezone: String!
  }
`;

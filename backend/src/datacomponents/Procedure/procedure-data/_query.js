import moment from "moment-timezone";
import { roles } from "~/directives";
import { getEnums, throwIfError } from "~/utils";
import { sendPDFUrl } from "~/utils/_sendPdfUrl";
import { TEMPLATE_TYPES } from "~/templates/procedures/partials/constants";

import PAID_FEATURES from "$/settings/paid-features.json";
import { ERROR } from "~/environment";
import { prepareProcedureForExport } from "~/utils/procedures";
import isPaidFeatureAvailable from "~/utils/isPaidFeatureAvailable";

export const paidFeaturesEnum = getEnums(PAID_FEATURES, "reference");

export const queryTypes = `#graphql
  type Query {
    getOwnOemProcedureById(id: ID!): Procedure @${roles.is.technician}
    downloadProcedurePDF(id: ID!, uuid: String!, timezone: String!): String @${roles.is.technician}
  }
`;

export const queryResolvers = {
  Query: {
    getOwnOemProcedureById: async (_, args, { dataSources, user }) => {
      const { id } = args || {};
      if (!id) return null;
      const oem = await dataSources.Oem.loadOne(user.organization);
      const proceduresEnabledForOem = await isPaidFeatureAvailable(
        dataSources,
        paidFeaturesEnum.procedures,
        oem,
      );
      if (!proceduresEnabledForOem) {
        return throwIfError(ERROR.USER.DO_NOT_HAVE_PERMISSION);
      }
      const procedure = await dataSources.Procedure.loadOne(id);
      if (!procedure || procedure.oem?.toString() !== user.organization)
        return throwIfError(ERROR.USER.BAD_USER_INPUT);

      return procedure;
    },
    downloadProcedurePDF: async (_, args, { dataSources, user, req }) => {
      const { id, uuid, timezone } = args || {};
      const { t: translate } = req || {};

      if (!id) return null;
      const oem = await dataSources.Oem.loadOne(user.organization);
      const proceduresEnabledForOem = await isPaidFeatureAvailable(
        dataSources,
        paidFeaturesEnum.procedures,
        oem,
      );
      if (!proceduresEnabledForOem) {
        return throwIfError(ERROR.USER.DO_NOT_HAVE_PERMISSION);
      }
      let procedure = await dataSources.Procedure.loadOne(id);
      if (!procedure || procedure.oem?.toString() !== user.organization)
        return throwIfError(ERROR.USER.BAD_USER_INPUT);

      const ticket = await dataSources.Ticket.getOneByQuery({
        "procedures.procedure": { $in: [id] },
      });

      const language = translate("procedure_pdf", { returnObjects: true }) || {};

      const createdAt = moment.tz(ticket.created_at, timezone).format("YYYY-MM-DDTHH:mm:ss.SSS");

      console.log("createdAt before", ticket.created_at);

      ticket.created_at = createdAt;

      console.log("createdAt after", ticket.created_at);

      procedure = await prepareProcedureForExport({ dataSources, language, procedure, ticket });

      console.log("procedure", procedure.workOrder.created_at);

      sendPDFUrl({
        data: procedure,
        oem,
        userId: user.id,
        dataSources,
        uuid,
        language,
        templateType: TEMPLATE_TYPES.PROCEDURES,
        timezone,
      });

      return "preparing pdf...";
    },
  },
};

import { IAiAssistantChat } from "~/datamodels/AiAssistantChat/interface";
import { IContext } from "~/types/common";

export const types = `#graphql
  type AiAssistantChatTurn {
    answer: String
    id: String
    query: String
  }

  type AiAssistantChat {
    _id: ID
    aiAssistant: AiAssistant
    chatId: String
    createdAt: DateTime
    createdBy: ID
    isSharedAssistantChat: Boolean
    oem: ID
    title: String
    turns: [AiAssistantChatTurn]
    updatedBy: ID
  }

  type PaginatedAiAssistantChats {
    totalCount: Int
    limit: Int
    skip: Int
    currentPage: Int
    aiAssistantChats: [AiAssistantChat]
  }
`;

export const typeResolvers = {
  AiAssistantChat: {
    aiAssistant: async ({ aiAssistant }: IAiAssistantChat, _: any, { dataSources }: IContext) =>
      await dataSources.AiAssistant.loadOne(aiAssistant),
    createdAt: async ({ created_at }: IAiAssistantChat) => created_at,
    turns: async ({ chatId }: IAiAssistantChat, _: any, { dataSources }: IContext) => {
      try {
        const turns = await dataSources.VectaraApi.listTurnsInChat(chatId);
        return turns.turns;
      } catch (err) {
        return [];
      }
    },
  },
};

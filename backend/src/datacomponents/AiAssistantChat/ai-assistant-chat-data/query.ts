import type { ExecutionContext } from "graphql/execution/execute";
import { features, roles } from "~/directives";
import { InputQueryParams } from "~/graphql/types";
import { IContext } from "~/types/common";
import { getAiAssistantChat, listAiAssistantChats } from "~/services/aiAssistantChat/fetch";

const { technician } = roles.is;
const { aiAssistants: aiAssistantsFeature } = features.oemOwns;

export const queryTypes = `#graphql
  type Query {
    getAiAssistantChat(id: ID): AiAssistantChat @${technician} @${aiAssistantsFeature}
    listAiAssistantChats(params: InputQueryParams): PaginatedAiAssistantChats @${technician} @${aiAssistantsFeature}
  }
`;

export const queryResolvers = {
  Query: {
    getAiAssistantChat: async (
      _: ExecutionContext["contextValue"],
      { id }: { id: string },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      getAiAssistantChat({
        args: { input: { id }, files: null, headers: {}, query: {}, params: {} },
        dataSources,
        user,
        translate,
      }),

    listAiAssistantChats: async (
      _: ExecutionContext["contextValue"],
      args: { params: InputQueryParams },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      listAiAssistantChats({
        args: {
          input: {},
          params: args.params,
          files: null,
          headers: {},
          query: {},
        },
        dataSources,
        user,
        translate,
      }),
  },
};

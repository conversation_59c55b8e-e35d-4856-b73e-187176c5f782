import { DefineOptions, Job } from "agenda";
import {
  CREATE_BOX_WEBHOOK_FOR_MACHINE_JOB,
  DE_INDEX_AI_ASSISTANT_FILE_JOB,
  INDEX_AI_ASSISTANT_FILE_JOB,
  SCHEDULE_TICKET_CREATION_JOB,
  DELETE_BOX_WEBHOOK_FOR_MACHINE_JOB,
  RESET_TOKEN_MONTHLY_LIMIT_JOB,
  RESET_AI_ASSISTANT_QUERIES_QUOTA,
  RESET_AI_NOTETAKER_RECORDING_QUOTA,
  WORK_ORDER_REMINDER_JOB,
  SYNC_EMAIL_ACCOUNT_JOB,
  SYNC_EMAIL_MESSAGES_JOB,
  SYNC_EMAIL_THREADS_JOB,
  SYNC_EMAIL_FOLDERS_JOB,
  OCR_DOCUMENT_JOB,
  GENERATE_DOCUMENT_EMBEDDING_JOB,
  GENERATE_DOCUMENT_CUSTOM_FIELDS_JOB,
  EXPIRE_CUSTOMER_PORTAL_INVITE_JOB,
  DELETE_BOX_WEBHOOK_FOR_TEMPLATE_JOB,
  CREATE_BOX_WEBHOOK_FOR_TEMPLATE_JOB,
  RESET_OCR_SCANS_QUOTA,
  GENERATE_DOCUMENT_TITLE_JOB,
  GENERATE_SEARCH_EMBEDDING_JOB,
  GENERATE_DOCUMENT_DATE_JOB,
  GENERATE_SEARCH_RESULT_ANSWERS_JOB,
  SYNC_AI_SEARCH_FEEDBACK_JOB,
  GENERATE_PUBLIC_APP_SITEMAP_JOB,
  GENERATE_DOCUMENT_AUTHORS_JOB,
  PARSE_DOCUMENT_JOB,
  GENERATE_DOCUMENT_TRANSLATIONS_JOB,
} from "~/agenda/constants";
import {
  initiateDocumentOperations,
  onDocumentOperationFailure,
  removeDocument,
} from "~/datacomponents/AiAssistant/ai-assistant-data/helpers";
import {
  enableBoxWebhookForAsset,
  deleteBoxWebhookForAsset,
} from "~/datacomponents/Asset/asset-data/helpers";
import { resetMonthlyOcrScans, resetMonthlyTokens } from "~/services/oem/delete";
import { IContext } from "~/types/common";
import { resetAiAssistantQueriesQuota } from "~/services/oem/update";
import { resetAiNotetakeRecordingQuota } from "~/services/user/update";
import { createPMETicket } from "~/services/preventiveMaintenance/scheduling";
import { sendWorkOrderReminderToAssignee } from "~/services/ticket/reminder";
import { syncEmailAccountJob } from "~/services/email/sync";
import { syncEmailMessageJob } from "~/services/email/sync/messages";
import { syncEmailThreadJob } from "~/services/email/sync/threads";
import { syncEmailFolderJob } from "~/services/email/sync/folders";
import {
  generateCustomFields,
  generateDocumentAuthor,
  generateDocumentDate,
  generateDocumentEmbeddings,
  generateDocumentTitle,
  generateDocumentTranslations,
  generateSearchEmbeddings,
  generateSearchResultAnswers,
  ocrDocument,
  parseDocument,
} from "~/services/document/utils";
import { expireCustomerPortalInviteJob } from "~/services/contact/update";
import {
  deleteBoxWebhookForAssetTemplate,
  enableBoxWebhookForAssetTemplate,
} from "~/services/assetTemplate/utils";
import { dumpFeedbackToGoogleSheet } from "~/services/document/update";
import { generateAndUploadSitemap } from "~/services/sitemap";

export type Definition = {
  config?: DefineOptions;
  func: (_: IContext["dataSources"], __: Job<any>) => void;
  interval?: string;
  maxRetries?: number;
  onFailure?: (_: IContext["dataSources"], __: any, ___: Job<any>) => Promise<any>;
};

export const definitions: {
  [key: string]: Definition;
} = {
  [CREATE_BOX_WEBHOOK_FOR_MACHINE_JOB]: {
    func: enableBoxWebhookForAsset,
    maxRetries: 5,
  },
  [CREATE_BOX_WEBHOOK_FOR_TEMPLATE_JOB]: {
    func: enableBoxWebhookForAssetTemplate,
    maxRetries: 5,
  },
  [DE_INDEX_AI_ASSISTANT_FILE_JOB]: {
    config: { priority: 20 },
    func: removeDocument,
    maxRetries: 5,
    onFailure: onDocumentOperationFailure,
  },
  [DELETE_BOX_WEBHOOK_FOR_MACHINE_JOB]: {
    func: deleteBoxWebhookForAsset,
    maxRetries: 5,
  },
  [DELETE_BOX_WEBHOOK_FOR_TEMPLATE_JOB]: {
    func: deleteBoxWebhookForAssetTemplate,
    maxRetries: 5,
  },
  [INDEX_AI_ASSISTANT_FILE_JOB]: {
    config: { priority: 20 },
    func: initiateDocumentOperations,
    maxRetries: 5,
    onFailure: onDocumentOperationFailure,
  },
  [RESET_AI_ASSISTANT_QUERIES_QUOTA]: {
    config: { concurrency: 10, priority: -10 },
    func: resetAiAssistantQueriesQuota,
  },
  [RESET_AI_NOTETAKER_RECORDING_QUOTA]: {
    config: { concurrency: 10, priority: -10 },
    func: resetAiNotetakeRecordingQuota,
  },
  [SCHEDULE_TICKET_CREATION_JOB]: {
    config: { concurrency: 10, priority: -10 },
    func: createPMETicket,
  },
  [WORK_ORDER_REMINDER_JOB]: {
    config: { concurrency: 10, priority: -10 },
    func: sendWorkOrderReminderToAssignee,
  },
  [RESET_OCR_SCANS_QUOTA]: {
    func: resetMonthlyOcrScans,
    interval: "0 0 1 * *",
  },
  [RESET_TOKEN_MONTHLY_LIMIT_JOB]: {
    func: resetMonthlyTokens,
    interval: "0 0 1 * *",
  },
  [SYNC_EMAIL_ACCOUNT_JOB]: {
    config: { concurrency: 1, priority: -10 },
    func: syncEmailAccountJob,
  },
  [SYNC_EMAIL_THREADS_JOB]: {
    config: { concurrency: 1, priority: -10 },
    func: syncEmailThreadJob,
  },
  [SYNC_EMAIL_MESSAGES_JOB]: {
    config: { concurrency: 1, priority: -10 },
    func: syncEmailMessageJob,
  },
  [SYNC_EMAIL_FOLDERS_JOB]: {
    config: { concurrency: 1, priority: -10 },
    func: syncEmailFolderJob,
  },
  [OCR_DOCUMENT_JOB]: {
    config: { concurrency: 10, priority: 10 },
    func: ocrDocument,
  },
  [PARSE_DOCUMENT_JOB]: {
    config: { concurrency: 10, priority: 10 },
    func: parseDocument,
  },
  [GENERATE_DOCUMENT_EMBEDDING_JOB]: {
    config: { concurrency: 5, priority: 10 },
    func: generateDocumentEmbeddings,
  },
  [GENERATE_DOCUMENT_TRANSLATIONS_JOB]: {
    config: { concurrency: 5, priority: 0 },
    func: generateDocumentTranslations,
  },
  [GENERATE_SEARCH_EMBEDDING_JOB]: {
    config: { concurrency: 10, priority: 20 },
    func: generateSearchEmbeddings,
  },
  [GENERATE_DOCUMENT_DATE_JOB]: {
    config: { concurrency: 5, priority: 0 },
    func: generateDocumentDate,
  },
  [GENERATE_DOCUMENT_AUTHORS_JOB]: {
    config: { concurrency: 5, priority: 0 },
    func: generateDocumentAuthor,
  },
  [GENERATE_DOCUMENT_CUSTOM_FIELDS_JOB]: {
    config: { concurrency: 10, priority: 0 },
    func: generateCustomFields,
  },
  [GENERATE_SEARCH_RESULT_ANSWERS_JOB]: {
    config: { concurrency: 5, priority: 20 },
    func: generateSearchResultAnswers,
  },
  [GENERATE_DOCUMENT_TITLE_JOB]: {
    config: { concurrency: 10, priority: 0 },
    func: generateDocumentTitle,
  },
  [SYNC_AI_SEARCH_FEEDBACK_JOB]: {
    config: { concurrency: 10, priority: 0 },
    func: dumpFeedbackToGoogleSheet,
  },
  [EXPIRE_CUSTOMER_PORTAL_INVITE_JOB]: {
    config: { concurrency: 10, priority: -10 },
    func: expireCustomerPortalInviteJob,
  },
  [GENERATE_PUBLIC_APP_SITEMAP_JOB]: {
    config: { concurrency: 10, priority: 20 },
    func: generateAndUploadSitemap,
    interval: "0 * * * *",
  },
};

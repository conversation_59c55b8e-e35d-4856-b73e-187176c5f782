import { GraphQLResolveInfo, GraphQLScalarType, GraphQLScalarTypeConfig } from "graphql";
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = {
  [_ in K]?: never;
};
export type Incremental<T> =
  | T
  | { [P in keyof T]?: P extends " $fragmentName" | "__typename" ? T[P] : never };
export type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>;
export type RequireFields<T, K extends keyof T> = Omit<T, K> & { [P in K]-?: NonNullable<T[P]> };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string };
  String: { input: string; output: string };
  Boolean: { input: boolean; output: boolean };
  Int: { input: number; output: number };
  Float: { input: number; output: number };
  Base64: { input: any; output: any };
  DateTime: { input: any; output: any };
  EmailAddress: { input: any; output: any };
  HTML: { input: any; output: any };
  JSON: { input: any; output: any };
  MJSON: { input: any; output: any };
  Mixed: { input: any; output: any };
  NonNegativeFloat: { input: any; output: any };
  SafeString: { input: any; output: any };
};

export type ActivityLog = {
  __typename?: "ActivityLog";
  _id?: Maybe<Scalars["ID"]["output"]>;
  action?: Maybe<Scalars["String"]["output"]>;
  actor?: Maybe<Scalars["String"]["output"]>;
  attribute?: Maybe<Scalars["String"]["output"]>;
  createdAt?: Maybe<Scalars["DateTime"]["output"]>;
  meta?: Maybe<Scalars["MJSON"]["output"]>;
  resource?: Maybe<Scalars["String"]["output"]>;
  resourceId?: Maybe<Scalars["ID"]["output"]>;
};

export type AdditionalField = {
  __typename?: "AdditionalField";
  _id: Scalars["ID"]["output"];
  createdBy?: Maybe<Scalars["ID"]["output"]>;
  created_at?: Maybe<Scalars["String"]["output"]>;
  enabled?: Maybe<Scalars["Boolean"]["output"]>;
  fieldType?: Maybe<Scalars["String"]["output"]>;
  isAdditionalField?: Maybe<Scalars["Boolean"]["output"]>;
  label?: Maybe<Scalars["String"]["output"]>;
  options?: Maybe<Array<Maybe<OptionResponse>>>;
  slug?: Maybe<Scalars["String"]["output"]>;
  type?: Maybe<Scalars["String"]["output"]>;
  updated_at?: Maybe<Scalars["String"]["output"]>;
  visibilityScope?: Maybe<VisibilityScope>;
};

export type AiAssistant = {
  __typename?: "AiAssistant";
  _id?: Maybe<Scalars["ID"]["output"]>;
  assistantName?: Maybe<Scalars["String"]["output"]>;
  assistantType?: Maybe<Scalars["String"]["output"]>;
  boxAccessToken?: Maybe<Scalars["String"]["output"]>;
  createdBy?: Maybe<Scalars["ID"]["output"]>;
  description?: Maybe<Scalars["String"]["output"]>;
  documents?: Maybe<AiAssistantDocuments>;
  isSharedAssistant?: Maybe<Scalars["Boolean"]["output"]>;
  machine?: Maybe<Asset>;
  machineID?: Maybe<Scalars["ID"]["output"]>;
  oem?: Maybe<Scalars["ID"]["output"]>;
  oemDetails?: Maybe<Oem>;
  status?: Maybe<Scalars["Int"]["output"]>;
  template?: Maybe<AssetTemplate>;
  templateId?: Maybe<Scalars["ID"]["output"]>;
};

export type AiAssistantChat = {
  __typename?: "AiAssistantChat";
  _id?: Maybe<Scalars["ID"]["output"]>;
  aiAssistant?: Maybe<AiAssistant>;
  chatId?: Maybe<Scalars["String"]["output"]>;
  createdAt?: Maybe<Scalars["DateTime"]["output"]>;
  createdBy?: Maybe<Scalars["ID"]["output"]>;
  isSharedAssistantChat?: Maybe<Scalars["Boolean"]["output"]>;
  oem?: Maybe<Scalars["ID"]["output"]>;
  title?: Maybe<Scalars["String"]["output"]>;
  turns?: Maybe<Array<Maybe<AiAssistantChatTurn>>>;
  updatedBy?: Maybe<Scalars["ID"]["output"]>;
};

export type AiAssistantChatTurn = {
  __typename?: "AiAssistantChatTurn";
  answer?: Maybe<Scalars["String"]["output"]>;
  id?: Maybe<Scalars["String"]["output"]>;
  query?: Maybe<Scalars["String"]["output"]>;
};

export type AiAssistantConfiguration = {
  __typename?: "AiAssistantConfiguration";
  allowedExternalQueries?: Maybe<Scalars["Float"]["output"]>;
  allowedOcrScans?: Maybe<Scalars["Float"]["output"]>;
  allowedQueries?: Maybe<Scalars["Float"]["output"]>;
  allowedRecordingSeconds?: Maybe<Scalars["Float"]["output"]>;
  allowedStorage?: Maybe<Scalars["Float"]["output"]>;
  allowedUsers?: Maybe<Scalars["Float"]["output"]>;
  autoApproveOCR?: Maybe<Scalars["Boolean"]["output"]>;
  consumedOcrScans?: Maybe<Scalars["Float"]["output"]>;
  consumedQueries?: Maybe<Scalars["Float"]["output"]>;
  consumedRecordingSeconds?: Maybe<Scalars["Float"]["output"]>;
  consumedStorage?: Maybe<Scalars["Float"]["output"]>;
  consumedUsers: Scalars["Float"]["output"];
};

export type AiAssistantDocument = {
  __typename?: "AiAssistantDocument";
  _id?: Maybe<Scalars["ID"]["output"]>;
  consumedStorage?: Maybe<Scalars["Int"]["output"]>;
  externalStorageServiceDocumentID?: Maybe<Scalars["String"]["output"]>;
  status?: Maybe<Scalars["Int"]["output"]>;
};

export type AiAssistantDocuments = {
  __typename?: "AiAssistantDocuments";
  externalDocuments?: Maybe<Array<Maybe<AiAssistantDocument>>>;
  internalDocuments?: Maybe<Array<Maybe<AiAssistantDocument>>>;
};

export type AiAssistantQueryResponse = {
  __typename?: "AiAssistantQueryResponse";
  answer?: Maybe<Scalars["String"]["output"]>;
  chat_id?: Maybe<Scalars["String"]["output"]>;
  response_language?: Maybe<Scalars["String"]["output"]>;
  search_results?: Maybe<Array<Maybe<AiAssistantQuerySearchResult>>>;
  turn_id?: Maybe<Scalars["String"]["output"]>;
};

export type AiAssistantQuerySearchResult = {
  __typename?: "AiAssistantQuerySearchResult";
  document_id?: Maybe<Scalars["String"]["output"]>;
  part_metadata?: Maybe<AiAssistantQuerySearchResultPartMetadata>;
  score?: Maybe<Scalars["Float"]["output"]>;
  text?: Maybe<Scalars["String"]["output"]>;
};

export type AiAssistantQuerySearchResultPartMetadata = {
  __typename?: "AiAssistantQuerySearchResultPartMetadata";
  page?: Maybe<Scalars["Int"]["output"]>;
};

export type AiAssistantUsage = {
  __typename?: "AiAssistantUsage";
  consumedOcrScans?: Maybe<Scalars["Float"]["output"]>;
  consumedQueries?: Maybe<Scalars["Float"]["output"]>;
  consumedRecordingSeconds?: Maybe<Scalars["Float"]["output"]>;
  consumedStorage?: Maybe<Scalars["Float"]["output"]>;
  consumedUsers: Scalars["Float"]["output"];
};

export type AiAssistantWithCount = {
  __typename?: "AiAssistantWithCount";
  aiAssistants?: Maybe<Array<Maybe<AiAssistant>>>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
};

export type AiNote = {
  __typename?: "AiNote";
  _id?: Maybe<Scalars["ID"]["output"]>;
  audioUrl?: Maybe<Scalars["String"]["output"]>;
  createdAt?: Maybe<Scalars["DateTime"]["output"]>;
  createdBy?: Maybe<User>;
  languageCode?: Maybe<Scalars["String"]["output"]>;
  oem?: Maybe<Scalars["ID"]["output"]>;
  publishedMachines?: Maybe<Array<Maybe<PublishedMachine>>>;
  summary?: Maybe<Scalars["String"]["output"]>;
  title?: Maybe<Scalars["String"]["output"]>;
  transcript?: Maybe<Scalars["String"]["output"]>;
  transcriptionError?: Maybe<TranscriptionError>;
  transcriptionStatus?: Maybe<Scalars["String"]["output"]>;
  updatedBy?: Maybe<Scalars["ID"]["output"]>;
};

export type Analytics = {
  __typename?: "Analytics";
  data?: Maybe<Scalars["MJSON"]["output"]>;
  inputTableOptions?: Maybe<Array<Maybe<ReportField>>>;
};

export type ApiKey = {
  __typename?: "ApiKey";
  apiKey?: Maybe<Scalars["String"]["output"]>;
};

export type AppConfig = {
  __typename?: "AppConfig";
  _id?: Maybe<Scalars["ID"]["output"]>;
  features?: Maybe<Array<Maybe<AppFeaturesEnum>>>;
  maintenanceOn?: Maybe<Scalars["Boolean"]["output"]>;
  plans?: Maybe<Array<Maybe<ProductPlan>>>;
  version?: Maybe<Scalars["String"]["output"]>;
};

export enum AppFeaturesEnum {
  BackgroundSyncEnabled = "backgroundSyncEnabled",
  HfInferenceForGenAi = "hfInferenceForGenAI",
  MultilingualAiSearch = "multilingualAISearch",
  ProductCatalog = "productCatalog",
}

export type Asset = {
  __typename?: "Asset";
  _3dModelUrl?: Maybe<Scalars["String"]["output"]>;
  _3dModelUrlUploadedBy?: Maybe<User>;
  _id?: Maybe<Scalars["ID"]["output"]>;
  aiAssistant?: Maybe<AiAssistant>;
  assetType?: Maybe<Scalars["ID"]["output"]>;
  childrenCount?: Maybe<Scalars["Int"]["output"]>;
  createdAt?: Maybe<Scalars["DateTime"]["output"]>;
  createdBy?: Maybe<Scalars["ID"]["output"]>;
  customFields?: Maybe<Array<Maybe<CustomField>>>;
  customer?: Maybe<Customer>;
  description?: Maybe<Scalars["String"]["output"]>;
  detachedFromTemplate?: Maybe<AssetDetachedFromTemplate>;
  documentFolders?: Maybe<DocumentFolders>;
  documentationFiles?: Maybe<Scalars["Int"]["output"]>;
  folderId?: Maybe<Scalars["String"]["output"]>;
  generalAccessUrl?: Maybe<Scalars["String"]["output"]>;
  hierarchy?: Maybe<Array<Maybe<AssetParent>>>;
  image?: Maybe<Scalars["String"]["output"]>;
  inventoryParts?: Maybe<Array<Maybe<AssetPart>>>;
  isAsset3dModelDeletable?: Maybe<Scalars["Boolean"]["output"]>;
  isBoxFoldersDisabled?: Maybe<Scalars["Boolean"]["output"]>;
  isOwnAsset?: Maybe<Scalars["Boolean"]["output"]>;
  isQRCodeEnabled?: Maybe<Scalars["Boolean"]["output"]>;
  isQRCodeFlowEnabled?: Maybe<Scalars["Boolean"]["output"]>;
  isSharedAsset?: Maybe<Scalars["Boolean"]["output"]>;
  issues?: Maybe<Scalars["Int"]["output"]>;
  name: Scalars["String"]["output"];
  numberOfTeams?: Maybe<Scalars["Int"]["output"]>;
  oem?: Maybe<Oem>;
  serialNumber?: Maybe<Scalars["String"]["output"]>;
  sharedAssetAccess?: Maybe<AssetAccess>;
  sharedAssistant?: Maybe<AiAssistant>;
  showParent?: Maybe<Scalars["Boolean"]["output"]>;
  slug?: Maybe<Scalars["String"]["output"]>;
  teams?: Maybe<Array<Maybe<Team>>>;
  template?: Maybe<AssetTemplate>;
  thumbnail?: Maybe<Scalars["String"]["output"]>;
  totalChildrenCount?: Maybe<Scalars["Int"]["output"]>;
  totalOpenTickets?: Maybe<Scalars["Int"]["output"]>;
  updatedAt?: Maybe<Scalars["DateTime"]["output"]>;
  uuid?: Maybe<Scalars["String"]["output"]>;
};

export type AssetAccess = {
  __typename?: "AssetAccess";
  _3dModel?: Maybe<Scalars["Boolean"]["output"]>;
  documentation?: Maybe<Scalars["Boolean"]["output"]>;
  history?: Maybe<Scalars["Boolean"]["output"]>;
  parts?: Maybe<Scalars["Boolean"]["output"]>;
  preventiveMaintenance?: Maybe<Scalars["Boolean"]["output"]>;
  qrCodes?: Maybe<Scalars["Boolean"]["output"]>;
  subAssets?: Maybe<Scalars["Boolean"]["output"]>;
};

export type AssetDetachedFromTemplate = {
  __typename?: "AssetDetachedFromTemplate";
  description?: Maybe<Scalars["Boolean"]["output"]>;
  documentation?: Maybe<Scalars["String"]["output"]>;
  image?: Maybe<Scalars["Boolean"]["output"]>;
  inventoryParts?: Maybe<Scalars["Boolean"]["output"]>;
};

export type AssetOrTemplate = Asset | AssetTemplate;

export type AssetParent = {
  __typename?: "AssetParent";
  id?: Maybe<Scalars["ID"]["output"]>;
  name?: Maybe<Scalars["String"]["output"]>;
  serialNumber?: Maybe<Scalars["String"]["output"]>;
};

export type AssetPart = {
  __typename?: "AssetPart";
  addedBy?: Maybe<User>;
  part?: Maybe<InventoryPart>;
};

export type AssetTemplate = {
  __typename?: "AssetTemplate";
  _3dModelUrl?: Maybe<Scalars["String"]["output"]>;
  _3dModelUrlUploadedBy?: Maybe<User>;
  _id?: Maybe<Scalars["ID"]["output"]>;
  aiAssistant?: Maybe<AiAssistant>;
  assets?: Maybe<Array<Maybe<Asset>>>;
  createdAt?: Maybe<Scalars["DateTime"]["output"]>;
  createdBy?: Maybe<Scalars["ID"]["output"]>;
  description?: Maybe<Scalars["String"]["output"]>;
  documentFolders?: Maybe<DocumentFolders>;
  image?: Maybe<Scalars["String"]["output"]>;
  inventoryParts?: Maybe<Array<Maybe<AssetTemplatePart>>>;
  oem?: Maybe<Oem>;
  procedures?: Maybe<Array<Maybe<AssetTemplateProcedure>>>;
  productAccess?: Maybe<ProductAccess>;
  sharedBoxToken?: Maybe<Scalars["String"]["output"]>;
  templateId?: Maybe<Scalars["String"]["output"]>;
  thumbnail?: Maybe<Scalars["String"]["output"]>;
  title: Scalars["String"]["output"];
  updatedAt?: Maybe<Scalars["DateTime"]["output"]>;
  visibility?: Maybe<Scalars["Int"]["output"]>;
};

export type AssetTemplatePart = {
  __typename?: "AssetTemplatePart";
  addedBy?: Maybe<User>;
  part?: Maybe<InventoryPart>;
};

export type AssetTemplateProcedure = {
  __typename?: "AssetTemplateProcedure";
  addedBy?: Maybe<User>;
  procedure?: Maybe<Procedure>;
  procedureId?: Maybe<Scalars["ID"]["output"]>;
};

export type AssetType = {
  __typename?: "AssetType";
  _id?: Maybe<Scalars["ID"]["output"]>;
  name?: Maybe<Scalars["String"]["output"]>;
};

export type AssignTicketTypeInput = {
  teamId: Scalars["ID"]["input"];
  ticketTypeIds: Array<InputMaybe<Scalars["ID"]["input"]>>;
};

export type AssignedWorkOrderReminder = {
  __typename?: "AssignedWorkOrderReminder";
  daysBefore?: Maybe<Scalars["Int"]["output"]>;
  enabled?: Maybe<Scalars["Boolean"]["output"]>;
};

export type Author = {
  __typename?: "Author";
  confidence?: Maybe<Scalars["String"]["output"]>;
  name?: Maybe<Scalars["String"]["output"]>;
};

export enum BoardFor {
  Facility = "facility",
  Machine = "machine",
  Ticket = "ticket",
}

export type Boards = {
  __typename?: "Boards";
  boardFor?: Maybe<BoardFor>;
  customFields?: Maybe<Array<Maybe<CustomAdditionalField>>>;
};

export type CalendarEvent = {
  __typename?: "CalendarEvent";
  assignees?: Maybe<Array<Maybe<Scalars["ID"]["output"]>>>;
  id?: Maybe<Scalars["String"]["output"]>;
  when?: Maybe<Scalars["JSON"]["output"]>;
};

export type CalendarSyncConfiguration = {
  __typename?: "CalendarSyncConfiguration";
  allowedConnectedAccounts?: Maybe<Scalars["Float"]["output"]>;
  consumedConnectedAccounts?: Maybe<Scalars["Float"]["output"]>;
};

export type CalendarSyncUsage = {
  __typename?: "CalendarSyncUsage";
  consumedConnectedAccounts?: Maybe<Scalars["Float"]["output"]>;
};

export type Citation = {
  __typename?: "Citation";
  highlight?: Maybe<Scalars["String"]["output"]>;
  id?: Maybe<Scalars["String"]["output"]>;
  keywords?: Maybe<Array<Maybe<Scalars["String"]["output"]>>>;
  pageNumber?: Maybe<Scalars["Int"]["output"]>;
  source?: Maybe<Scalars["String"]["output"]>;
};

export type CompleteMultiPartUploadPayload = {
  __typename?: "CompleteMultiPartUploadPayload";
  url: Scalars["String"]["output"];
};

export type CompletedMultipartUpload = {
  Parts?: InputMaybe<Array<InputMaybe<CompletedPartList>>>;
};

export type CompletedPartList = {
  ETag?: InputMaybe<Scalars["String"]["input"]>;
  PartNumber?: InputMaybe<Scalars["Int"]["input"]>;
};

export type ConnectionHistory = {
  __typename?: "ConnectionHistory";
  _id?: Maybe<Scalars["ID"]["output"]>;
  createdAt?: Maybe<Scalars["DateTime"]["output"]>;
  createdBy?: Maybe<User>;
  customer?: Maybe<Customer>;
  machine?: Maybe<Scalars["ID"]["output"]>;
  month?: Maybe<Scalars["Int"]["output"]>;
  resource?: Maybe<Scalars["MJSON"]["output"]>;
  resourceId?: Maybe<Scalars["ID"]["output"]>;
  ticket?: Maybe<Ticket>;
  type?: Maybe<Scalars["String"]["output"]>;
  updatedBy?: Maybe<User>;
  year?: Maybe<Scalars["Int"]["output"]>;
};

export type ConnectionRequest = {
  __typename?: "ConnectionRequest";
  _id?: Maybe<Scalars["ID"]["output"]>;
  existingContactConnection?: Maybe<Customer>;
  resolveDate?: Maybe<Scalars["DateTime"]["output"]>;
  resolvedBy?: Maybe<User>;
  senderOem?: Maybe<Oem>;
  sentBy?: Maybe<User>;
  sentDate?: Maybe<Scalars["DateTime"]["output"]>;
  sentTo?: Maybe<Oem>;
  sentToConnection?: Maybe<Customer>;
  sentToContact?: Maybe<Contact>;
  sentToUser?: Maybe<User>;
  status?: Maybe<Scalars["String"]["output"]>;
};

export type Contact = {
  __typename?: "Contact";
  _id?: Maybe<Scalars["ID"]["output"]>;
  accessStatus?: Maybe<Scalars["String"]["output"]>;
  canResendInvite?: Maybe<Scalars["Boolean"]["output"]>;
  connection?: Maybe<Scalars["ID"]["output"]>;
  createdBy?: Maybe<User>;
  deleted?: Maybe<Scalars["Boolean"]["output"]>;
  email?: Maybe<Scalars["String"]["output"]>;
  jobTitle?: Maybe<Scalars["String"]["output"]>;
  landline?: Maybe<Scalars["String"]["output"]>;
  name?: Maybe<Scalars["String"]["output"]>;
  oem?: Maybe<Scalars["ID"]["output"]>;
  phoneNumber?: Maybe<Scalars["String"]["output"]>;
  updatedBy?: Maybe<User>;
  user?: Maybe<Scalars["ID"]["output"]>;
};

export type CreateMultiPartUploadPayload = {
  __typename?: "CreateMultiPartUploadPayload";
  UploadId: Scalars["String"]["output"];
  fileKey: Scalars["String"]["output"];
  signedUrls: Array<Maybe<Scalars["String"]["output"]>>;
};

export type CustomAdditionalField = {
  __typename?: "CustomAdditionalField";
  _id: Scalars["ID"]["output"];
  createdBy?: Maybe<Scalars["ID"]["output"]>;
  created_at?: Maybe<Scalars["String"]["output"]>;
  description?: Maybe<Scalars["String"]["output"]>;
  enabled?: Maybe<Scalars["Boolean"]["output"]>;
  fieldType?: Maybe<Scalars["String"]["output"]>;
  isAdditionalField?: Maybe<Scalars["Boolean"]["output"]>;
  label?: Maybe<Scalars["String"]["output"]>;
  oem?: Maybe<Oem>;
  options?: Maybe<Array<Maybe<OptionResponse>>>;
  order?: Maybe<Scalars["Int"]["output"]>;
  slug?: Maybe<Scalars["String"]["output"]>;
  type?: Maybe<Scalars["String"]["output"]>;
  updated_at?: Maybe<Scalars["String"]["output"]>;
  visibilityScope?: Maybe<VisibilityScope>;
};

export type CustomField = {
  __typename?: "CustomField";
  _id?: Maybe<Scalars["ID"]["output"]>;
  fieldId?: Maybe<CustomAdditionalField>;
  reasonReferenceMap?: Maybe<Scalars["JSON"]["output"]>;
  value?: Maybe<Scalars["JSON"]["output"]>;
  values?: Maybe<Array<Maybe<Scalars["String"]["output"]>>>;
};

export type Customer = {
  __typename?: "Customer";
  _id?: Maybe<Scalars["ID"]["output"]>;
  assetAccess?: Maybe<AssetAccess>;
  createdAt?: Maybe<Scalars["DateTime"]["output"]>;
  createdBy?: Maybe<Scalars["ID"]["output"]>;
  customFields?: Maybe<Array<Maybe<CustomField>>>;
  customerAddress?: Maybe<CustomerAddress>;
  description?: Maybe<Scalars["String"]["output"]>;
  facilityId?: Maybe<Scalars["String"]["output"]>;
  facilityIdentifier?: Maybe<Scalars["String"]["output"]>;
  generalAccessUrl?: Maybe<Scalars["String"]["output"]>;
  hasCustomerPortal?: Maybe<Scalars["Boolean"]["output"]>;
  isMachineDocumentationEnabled?: Maybe<Scalars["Boolean"]["output"]>;
  isPreventiveMaintenanceEventsEnabled?: Maybe<Scalars["Boolean"]["output"]>;
  isQRCodeEnabled?: Maybe<Scalars["Boolean"]["output"]>;
  linkedOrg?: Maybe<Scalars["ID"]["output"]>;
  machines?: Maybe<Array<Maybe<Asset>>>;
  name?: Maybe<Scalars["String"]["output"]>;
  numberOfTeams?: Maybe<Scalars["Int"]["output"]>;
  oem?: Maybe<Oem>;
  qrCodeAccess?: Maybe<Scalars["String"]["output"]>;
  teams?: Maybe<Array<Maybe<Team>>>;
  totalMachines?: Maybe<Scalars["Int"]["output"]>;
  totalOpenTickets?: Maybe<Scalars["Int"]["output"]>;
  totalUsers?: Maybe<Scalars["Int"]["output"]>;
  totalUsersWithAccess?: Maybe<Scalars["Int"]["output"]>;
  type?: Maybe<Scalars["String"]["output"]>;
  updatedAt?: Maybe<Scalars["DateTime"]["output"]>;
  urlOemFacility?: Maybe<Scalars["String"]["output"]>;
};

export type CustomerAddress = {
  __typename?: "CustomerAddress";
  coordinates?: Maybe<Array<Maybe<Scalars["Float"]["output"]>>>;
};

export type CustomerMarker = {
  __typename?: "CustomerMarker";
  _id: Scalars["ID"]["output"];
  customerAddress?: Maybe<CustomerAddress>;
};

export type CustomerPortal = {
  __typename?: "CustomerPortal";
  _id?: Maybe<Scalars["ID"]["output"]>;
  assetAccess?: Maybe<AssetAccess>;
  connection?: Maybe<Customer>;
  contacts?: Maybe<Array<Maybe<Contact>>>;
  createdBy?: Maybe<User>;
  deleted?: Maybe<Scalars["Boolean"]["output"]>;
  name?: Maybe<Scalars["String"]["output"]>;
  oem?: Maybe<Scalars["ID"]["output"]>;
  productAccess?: Maybe<ProductAccess>;
  updatedBy?: Maybe<User>;
};

export type CustomerPortalLoginResponse = {
  __typename?: "CustomerPortalLoginResponse";
  isError?: Maybe<Scalars["Boolean"]["output"]>;
  message?: Maybe<Scalars["String"]["output"]>;
  redirectUrl?: Maybe<Scalars["String"]["output"]>;
  refreshToken?: Maybe<Scalars["String"]["output"]>;
  token?: Maybe<Scalars["String"]["output"]>;
};

export type DeletedEvent = {
  __typename?: "DeletedEvent";
  _id?: Maybe<Scalars["ID"]["output"]>;
  machine?: Maybe<Asset>;
  oem: Oem;
};

export type Document = {
  __typename?: "Document";
  _id: Scalars["ID"]["output"];
  aiGeneratedTitle?: Maybe<Scalars["String"]["output"]>;
  authors?: Maybe<Array<Maybe<Author>>>;
  boxDocumentId?: Maybe<Scalars["String"]["output"]>;
  contributors?: Maybe<Array<Maybe<Author>>>;
  createdBy?: Maybe<User>;
  customFields?: Maybe<Array<Maybe<CustomField>>>;
  date?: Maybe<Scalars["String"]["output"]>;
  documentChunks?: Maybe<Array<Maybe<DocumentChunk>>>;
  documentPages?: Maybe<Array<Maybe<DocumentPage>>>;
  editors?: Maybe<Array<Maybe<Author>>>;
  language?: Maybe<Scalars["String"]["output"]>;
  ocrGeneratedContent?: Maybe<Scalars["String"]["output"]>;
  results?: Maybe<Array<Maybe<DocumentChunk>>>;
  size?: Maybe<Scalars["Int"]["output"]>;
  status?: Maybe<Scalars["Int"]["output"]>;
  title?: Maybe<Scalars["String"]["output"]>;
  updatedBy?: Maybe<User>;
};

export type DocumentAnswers = {
  __typename?: "DocumentAnswers";
  answer?: Maybe<Scalars["String"]["output"]>;
  chunkID?: Maybe<Scalars["String"]["output"]>;
  citations?: Maybe<Array<Maybe<Citation>>>;
  documentId?: Maybe<Scalars["String"]["output"]>;
  highlight?: Maybe<Scalars["String"]["output"]>;
  keywords?: Maybe<Array<Maybe<Scalars["String"]["output"]>>>;
  pageNumber?: Maybe<Scalars["Int"]["output"]>;
};

export type DocumentChunk = {
  __typename?: "DocumentChunk";
  _id: Scalars["ID"]["output"];
  boxDocumentId?: Maybe<Scalars["String"]["output"]>;
  chunkIndex?: Maybe<Scalars["Int"]["output"]>;
  content?: Maybe<Scalars["String"]["output"]>;
  distance?: Maybe<Scalars["Float"]["output"]>;
  documentAITitle?: Maybe<Scalars["String"]["output"]>;
  documentCustomFields?: Maybe<Array<Maybe<CustomField>>>;
  documentDate?: Maybe<Scalars["String"]["output"]>;
  documentId?: Maybe<Scalars["ID"]["output"]>;
  documentPage?: Maybe<DocumentPage>;
  documentTitle?: Maybe<Scalars["String"]["output"]>;
  ocrGeneratedContent?: Maybe<Scalars["String"]["output"]>;
  pageContent?: Maybe<Scalars["String"]["output"]>;
  pageNumber?: Maybe<Scalars["Int"]["output"]>;
};

export type DocumentFolders = {
  __typename?: "DocumentFolders";
  _id?: Maybe<Scalars["ID"]["output"]>;
  externalId?: Maybe<Scalars["ID"]["output"]>;
  internalId?: Maybe<Scalars["ID"]["output"]>;
};

export type DocumentLabel = {
  __typename?: "DocumentLabel";
  _id?: Maybe<Scalars["ID"]["output"]>;
  name?: Maybe<Scalars["String"]["output"]>;
};

export type DocumentPage = {
  __typename?: "DocumentPage";
  _id: Scalars["ID"]["output"];
  content?: Maybe<Scalars["String"]["output"]>;
  documentId?: Maybe<Scalars["ID"]["output"]>;
  pageIndex?: Maybe<Scalars["Int"]["output"]>;
  translations?: Maybe<Array<Maybe<DocumentTranslation>>>;
};

export type DocumentTranslation = {
  __typename?: "DocumentTranslation";
  content?: Maybe<Scalars["String"]["output"]>;
  language?: Maybe<Scalars["String"]["output"]>;
  status?: Maybe<Scalars["Int"]["output"]>;
};

export type DraftJunkCount = {
  __typename?: "DraftJunkCount";
  drafts?: Maybe<Scalars["Int"]["output"]>;
  junk?: Maybe<Scalars["Int"]["output"]>;
};

export type DraftOperation = {
  __typename?: "DraftOperation";
  operationType: Scalars["String"]["output"];
  ticket?: Maybe<DraftTicket>;
  unavailable?: Maybe<DraftUnavailable>;
};

export type DraftTicket = {
  __typename?: "DraftTicket";
  assignees?: Maybe<Array<Maybe<Scalars["ID"]["output"]>>>;
  id?: Maybe<Scalars["ID"]["output"]>;
  resources?: Maybe<Array<Maybe<Scalars["ID"]["output"]>>>;
  schedule?: Maybe<Schedule>;
  status?: Maybe<Scalars["ID"]["output"]>;
};

export type DraftUnavailable = {
  __typename?: "DraftUnavailable";
  id?: Maybe<Scalars["ID"]["output"]>;
  schedule?: Maybe<Schedule>;
  scheduleType?: Maybe<Scalars["String"]["output"]>;
  toDelete?: Maybe<Scalars["Boolean"]["output"]>;
  user?: Maybe<Scalars["ID"]["output"]>;
};

export type EmailAccount = {
  __typename?: "EmailAccount";
  _id?: Maybe<Scalars["ID"]["output"]>;
  emailAccountStatus?: Maybe<Scalars["String"]["output"]>;
  emailAddress?: Maybe<Scalars["String"]["output"]>;
  isSynced?: Maybe<Scalars["Boolean"]["output"]>;
};

export type EmailAttachment = {
  __typename?: "EmailAttachment";
  contentId?: Maybe<Scalars["String"]["output"]>;
  contentType?: Maybe<Scalars["String"]["output"]>;
  filename?: Maybe<Scalars["String"]["output"]>;
  grantId?: Maybe<Scalars["String"]["output"]>;
  id?: Maybe<Scalars["String"]["output"]>;
  isInline?: Maybe<Scalars["Boolean"]["output"]>;
  size?: Maybe<Scalars["Int"]["output"]>;
};

export type EmailDraft = {
  __typename?: "EmailDraft";
  draft?: Maybe<Scalars["JSON"]["output"]>;
  fileUrl?: Maybe<Scalars["JSON"]["output"]>;
};

export type EmailFolder = {
  __typename?: "EmailFolder";
  _id?: Maybe<Scalars["ID"]["output"]>;
  attributes?: Maybe<Array<Maybe<Scalars["String"]["output"]>>>;
  childCount?: Maybe<Scalars["Int"]["output"]>;
  grantId?: Maybe<Scalars["String"]["output"]>;
  id?: Maybe<Scalars["String"]["output"]>;
  name?: Maybe<Scalars["String"]["output"]>;
  oem?: Maybe<Scalars["ID"]["output"]>;
  parentId?: Maybe<Scalars["String"]["output"]>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
  unreadCount?: Maybe<Scalars["Int"]["output"]>;
};

export type EmailMessage = {
  __typename?: "EmailMessage";
  _id?: Maybe<Scalars["ID"]["output"]>;
  attachments?: Maybe<Array<Maybe<EmailAttachment>>>;
  bcc?: Maybe<Array<Maybe<EmailParticipant>>>;
  body?: Maybe<Scalars["String"]["output"]>;
  cc?: Maybe<Array<Maybe<EmailParticipant>>>;
  date?: Maybe<Scalars["Int"]["output"]>;
  folders?: Maybe<Array<Maybe<Scalars["String"]["output"]>>>;
  from?: Maybe<Array<Maybe<EmailParticipant>>>;
  grantId?: Maybe<Scalars["String"]["output"]>;
  id?: Maybe<Scalars["String"]["output"]>;
  oem?: Maybe<Scalars["ID"]["output"]>;
  replyTo?: Maybe<Array<Maybe<EmailParticipant>>>;
  snippet?: Maybe<Scalars["String"]["output"]>;
  starred?: Maybe<Scalars["Boolean"]["output"]>;
  subject?: Maybe<Scalars["String"]["output"]>;
  threadId?: Maybe<Scalars["String"]["output"]>;
  to?: Maybe<Array<Maybe<EmailParticipant>>>;
  unread?: Maybe<Scalars["Boolean"]["output"]>;
};

export type EmailMessageWithFileUrl = {
  __typename?: "EmailMessageWithFileURL";
  fileUrl?: Maybe<Scalars["JSON"]["output"]>;
  message?: Maybe<EmailMessage>;
};

export type EmailNotification = {
  __typename?: "EmailNotification";
  maintenanceWorkOrderCreationNotifyTo?: Maybe<Array<Maybe<User>>>;
  messageOnUnassignedWorkOrderNotifyTo?: Maybe<Array<Maybe<User>>>;
  notifyOnMaintenanceWorkOrderCreation?: Maybe<Scalars["Boolean"]["output"]>;
  notifyOnMessageOnUnassignedWorkOrder?: Maybe<Scalars["Boolean"]["output"]>;
  notifyOnWorkOrderCreation?: Maybe<Scalars["Boolean"]["output"]>;
  onAddedAsWorkOrderFollower?: Maybe<Scalars["Boolean"]["output"]>;
  onAssignedTicketInternalNotePost?: Maybe<Scalars["Boolean"]["output"]>;
  onAssignedWorkOrderReminder?: Maybe<AssignedWorkOrderReminder>;
  onAssignedWorkOrderUpdate?: Maybe<Scalars["Boolean"]["output"]>;
  onMentionedInWorkOrderInternalNote?: Maybe<Scalars["Boolean"]["output"]>;
  onMessageOnAssignedWorkOrder?: Maybe<Scalars["Boolean"]["output"]>;
  onNewTicketAssigned?: Maybe<Scalars["Boolean"]["output"]>;
  workOrderCreationNotifyTo?: Maybe<Array<Maybe<NotificationUser>>>;
};

export type EmailParticipant = {
  __typename?: "EmailParticipant";
  email?: Maybe<Scalars["String"]["output"]>;
  name?: Maybe<Scalars["String"]["output"]>;
};

export type EmailThread = {
  __typename?: "EmailThread";
  _id?: Maybe<Scalars["ID"]["output"]>;
  earliestMessageDate?: Maybe<Scalars["Int"]["output"]>;
  folders?: Maybe<Array<Maybe<Scalars["String"]["output"]>>>;
  grantId?: Maybe<Scalars["String"]["output"]>;
  hasAttachments?: Maybe<Scalars["Boolean"]["output"]>;
  id?: Maybe<Scalars["String"]["output"]>;
  latestMessageReceivedDate?: Maybe<Scalars["Int"]["output"]>;
  latestMessageSentDate?: Maybe<Scalars["Int"]["output"]>;
  messageIds?: Maybe<Array<Maybe<Scalars["String"]["output"]>>>;
  messages?: Maybe<Array<Maybe<EmailThreadMessage>>>;
  oem?: Maybe<Scalars["ID"]["output"]>;
  participants?: Maybe<Array<Maybe<EmailParticipant>>>;
  snippet?: Maybe<Scalars["String"]["output"]>;
  subject?: Maybe<Scalars["String"]["output"]>;
  unread?: Maybe<Scalars["Boolean"]["output"]>;
};

export type EmailThreadMessage = {
  __typename?: "EmailThreadMessage";
  bcc?: Maybe<Array<Maybe<EmailParticipant>>>;
  cc?: Maybe<Array<Maybe<EmailParticipant>>>;
  date?: Maybe<Scalars["Int"]["output"]>;
  from?: Maybe<Array<Maybe<EmailParticipant>>>;
  id?: Maybe<Scalars["String"]["output"]>;
  replyTo?: Maybe<Array<Maybe<EmailParticipant>>>;
  subject?: Maybe<Scalars["String"]["output"]>;
  to?: Maybe<Array<Maybe<EmailParticipant>>>;
  unread?: Maybe<Scalars["Boolean"]["output"]>;
};

export type EmailThreadWithTicket = {
  __typename?: "EmailThreadWithTicket";
  thread?: Maybe<EmailThread>;
  ticket?: Maybe<Ticket>;
};

export enum EnumTicketStatus {
  Callback = "callback",
  Closed = "closed",
  Hold = "hold",
  Open = "open",
  Visit = "visit",
  Waiting = "waiting",
}

export type FieldAttachment = {
  __typename?: "FieldAttachment";
  _id?: Maybe<Scalars["ID"]["output"]>;
  name?: Maybe<Scalars["String"]["output"]>;
  size?: Maybe<Scalars["String"]["output"]>;
  type?: Maybe<Scalars["String"]["output"]>;
  url?: Maybe<Scalars["String"]["output"]>;
};

export type FieldOption = {
  __typename?: "FieldOption";
  _id?: Maybe<Scalars["ID"]["output"]>;
  name?: Maybe<Scalars["String"]["output"]>;
};

export enum FieldTypes {
  Address = "address",
  Date = "date",
  MultiSelect = "multiSelect",
  Number = "number",
  SingleSelect = "singleSelect",
  Tag = "tag",
  Text = "text",
}

export type FileImportDataMeta = {
  __typename?: "FileImportDataMeta";
  templateId?: Maybe<Scalars["ID"]["output"]>;
  templateSection?: Maybe<Scalars["ID"]["output"]>;
};

export type FileImporter = {
  __typename?: "FileImporter";
  _id: Scalars["ID"]["output"];
  importers?: Maybe<Array<Maybe<FileImporterData>>>;
  licenseKey?: Maybe<Scalars["String"]["output"]>;
  user?: Maybe<Scalars["JSON"]["output"]>;
};

export type FileImporterData = {
  __typename?: "FileImporterData";
  _id: Scalars["ID"]["output"];
  fields?: Maybe<Scalars["JSON"]["output"]>;
  meta?: Maybe<FileImportDataMeta>;
  settings?: Maybe<Scalars["JSON"]["output"]>;
};

export type GroupedTickets = {
  __typename?: "GroupedTickets";
  customerId?: Maybe<Scalars["ID"]["output"]>;
  tickets?: Maybe<Array<Maybe<Ticket>>>;
};

export type Guide = {
  __typename?: "Guide";
  _id?: Maybe<Scalars["ID"]["output"]>;
  createdBy?: Maybe<User>;
  image?: Maybe<Scalars["String"]["output"]>;
  machine?: Maybe<Asset>;
  name?: Maybe<Scalars["String"]["output"]>;
  oem?: Maybe<Scalars["ID"]["output"]>;
  sessionId?: Maybe<Scalars["String"]["output"]>;
};

export type Importer = {
  __typename?: "Importer";
  _id?: Maybe<Scalars["ID"]["output"]>;
  actions?: Maybe<Array<Scalars["String"]["output"]>>;
  section?: Maybe<Scalars["String"]["output"]>;
};

export type InputAddAiAssistantDocuments = {
  _id: Scalars["ID"]["input"];
  files: Array<Scalars["String"]["input"]>;
};

export type InputAddTicketAssignee = {
  assignee?: InputMaybe<Scalars["ID"]["input"]>;
  ticketId: Scalars["ID"]["input"];
};

export type InputAddTicketAttachment = {
  name: Scalars["String"]["input"];
  size: Scalars["Int"]["input"];
  type: Scalars["String"]["input"];
  url: Scalars["String"]["input"];
};

export type InputAddTicketAttachments = {
  attachments?: InputMaybe<Array<InputMaybe<InputAddTicketAttachment>>>;
  ticketId: Scalars["ID"]["input"];
};

export type InputAddTicketFollower = {
  follower?: InputMaybe<Scalars["ID"]["input"]>;
  ticketId: Scalars["ID"]["input"];
};

export type InputAddTicketResource = {
  resource: Scalars["ID"]["input"];
  ticketId: Scalars["ID"]["input"];
};

export type InputAddTimeTrackerLog = {
  ticketId: Scalars["ID"]["input"];
  timeTrackerLog: InputAddTimeTrackerLogPayload;
};

export type InputAddTimeTrackerLogPayload = {
  description?: InputMaybe<Scalars["String"]["input"]>;
  endDateTime: Scalars["DateTime"]["input"];
  isBillable: Scalars["Boolean"]["input"];
  startDateTime: Scalars["DateTime"]["input"];
  ticketTag: Scalars["ID"]["input"];
  timeElapsedInSeconds: Scalars["Int"]["input"];
};

export type InputAppConfig = {
  _id?: InputMaybe<Scalars["ID"]["input"]>;
  features?: InputMaybe<Array<InputMaybe<AppFeaturesEnum>>>;
  plans?: InputMaybe<Array<InputMaybe<InputProductPlan>>>;
};

export type InputAssetAccess = {
  _3dModel?: InputMaybe<Scalars["Boolean"]["input"]>;
  documentation?: InputMaybe<Scalars["Boolean"]["input"]>;
  history?: InputMaybe<Scalars["Boolean"]["input"]>;
  parts?: InputMaybe<Scalars["Boolean"]["input"]>;
  preventiveMaintenance?: InputMaybe<Scalars["Boolean"]["input"]>;
  qrCodes?: InputMaybe<Scalars["Boolean"]["input"]>;
};

export type InputAssignAssetInventoryParts = {
  assetId: Scalars["ID"]["input"];
  inventoryParts: Array<Scalars["ID"]["input"]>;
};

export type InputAssignAssetTemplateInventoryParts = {
  assetTemplateId: Scalars["ID"]["input"];
  inventoryParts: Array<Scalars["ID"]["input"]>;
};

export type InputAssignAssetsToParent = {
  assetIds: Array<Scalars["ID"]["input"]>;
  parentId: Scalars["ID"]["input"];
};

export type InputAssignInventoryPartsToTicket = {
  inventoryParts: Array<InputTicketInventoryPartPayload>;
  ticketId: Scalars["ID"]["input"];
};

export type InputAssignMultipleAssetsToTeam = {
  assets: Array<Scalars["ID"]["input"]>;
  team: Scalars["ID"]["input"];
};

export type InputAssignMultipleCustomersToTeam = {
  customers: Array<Scalars["ID"]["input"]>;
  team: Scalars["ID"]["input"];
};

export type InputAssignMultipleUsersToTeam = {
  team: Scalars["ID"]["input"];
  users: Array<Scalars["ID"]["input"]>;
};

export type InputAssignOemImporters = {
  oemId: Scalars["ID"]["input"];
  templates: Array<InputImporters>;
};

export type InputAssignOwnOemMultipleAssetsToOwnOemCustomer = {
  assets: Array<Scalars["ID"]["input"]>;
  customer: Scalars["ID"]["input"];
};

export type InputAssignUnassignMultipleSkillsOemUser = {
  skills: Array<Scalars["ID"]["input"]>;
  userId: Scalars["ID"]["input"];
};

export type InputAssignedWorkOrderReminder = {
  daysBefore?: InputMaybe<Scalars["Int"]["input"]>;
  enabled?: InputMaybe<Scalars["Boolean"]["input"]>;
};

export type InputAttachProcedureToWorkOrder = {
  templateId: Scalars["ID"]["input"];
  workOrderId: Scalars["ID"]["input"];
};

export type InputConfigureOemApi = {
  oem: Scalars["ID"]["input"];
  quota: Scalars["Int"]["input"];
  requestRate: Scalars["Int"]["input"];
};

export type InputConfigureOemEmail = {
  maximumAllowed: Scalars["Int"]["input"];
  oem: Scalars["ID"]["input"];
};

export type InputConfigureOemPlans = {
  oem: Scalars["ID"]["input"];
  plans?: InputMaybe<Array<InputMaybe<InputOemPlans>>>;
};

export type InputCreate3DGuide = {
  image?: InputMaybe<Scalars["String"]["input"]>;
  machineId: Scalars["ID"]["input"];
  name: Scalars["String"]["input"];
  sessionId?: InputMaybe<Scalars["String"]["input"]>;
};

export type InputCreateAiAssistantV2 = {
  assetId?: InputMaybe<Scalars["ID"]["input"]>;
  assetTemplateId?: InputMaybe<Scalars["ID"]["input"]>;
  assistantType: Scalars["String"]["input"];
  description: Scalars["String"]["input"];
  documentIds: Array<Scalars["String"]["input"]>;
  name: Scalars["String"]["input"];
};

export type InputCreateAiNote = {
  audioUrl: Scalars["String"]["input"];
  languageCode?: InputMaybe<Scalars["String"]["input"]>;
};

export type InputCreateAssetTemplate = {
  description?: InputMaybe<Scalars["String"]["input"]>;
  image?: InputMaybe<Scalars["String"]["input"]>;
  templateId?: InputMaybe<Scalars["String"]["input"]>;
  thumbnail?: InputMaybe<Scalars["String"]["input"]>;
  title: Scalars["String"]["input"];
  visibility?: InputMaybe<Scalars["Int"]["input"]>;
};

export type InputCreateContact = {
  connection: Scalars["ID"]["input"];
  email?: InputMaybe<Scalars["String"]["input"]>;
  jobTitle?: InputMaybe<Scalars["String"]["input"]>;
  landline?: InputMaybe<Scalars["String"]["input"]>;
  name: Scalars["String"]["input"];
  phoneNumber?: InputMaybe<Scalars["String"]["input"]>;
  user?: InputMaybe<Scalars["ID"]["input"]>;
};

export type InputCreateDocument = {
  documentId: Scalars["String"]["input"];
  size: Scalars["Int"]["input"];
  title: Scalars["String"]["input"];
};

export type InputCreateDocuments = {
  documents: Array<InputCreateDocument>;
  runOCR?: InputMaybe<Scalars["Boolean"]["input"]>;
};

export type InputCreateFacilityUser = {
  access?: InputMaybe<Scalars["Boolean"]["input"]>;
  email: Scalars["EmailAddress"]["input"];
  facilityId: Scalars["ID"]["input"];
  info?: InputMaybe<Scalars["String"]["input"]>;
  mobile?: InputMaybe<Scalars["String"]["input"]>;
  name?: InputMaybe<Scalars["String"]["input"]>;
  phone?: InputMaybe<Scalars["String"]["input"]>;
};

export type InputCreateFacilityUserV2 = {
  access?: InputMaybe<Scalars["Boolean"]["input"]>;
  email: Scalars["EmailAddress"]["input"];
  info?: InputMaybe<Scalars["String"]["input"]>;
  name?: InputMaybe<Scalars["String"]["input"]>;
};

export type InputCreateInventoryPart = {
  articleNumber: Scalars["String"]["input"];
  description?: InputMaybe<Scalars["String"]["input"]>;
  image?: InputMaybe<Scalars["String"]["input"]>;
  name: Scalars["String"]["input"];
  thumbnail?: InputMaybe<Scalars["String"]["input"]>;
};

export type InputCreateMachineHistoryNote = {
  machine: Scalars["String"]["input"];
  note: InputMachineHistoryNote;
};

export type InputCreateOem = {
  _id?: InputMaybe<Scalars["ID"]["input"]>;
  backgroundColor?: InputMaybe<Scalars["String"]["input"]>;
  brandLogo?: InputMaybe<Scalars["String"]["input"]>;
  crop?: InputMaybe<Scalars["JSON"]["input"]>;
  firstSignInRedirectUrl?: InputMaybe<Scalars["String"]["input"]>;
  heading?: InputMaybe<Scalars["String"]["input"]>;
  logo?: InputMaybe<Scalars["String"]["input"]>;
  name?: InputMaybe<Scalars["String"]["input"]>;
  oemEmail?: InputMaybe<Scalars["EmailAddress"]["input"]>;
  oemId?: InputMaybe<Scalars["String"]["input"]>;
  oemName?: InputMaybe<Scalars["String"]["input"]>;
  paragraph?: InputMaybe<Scalars["String"]["input"]>;
  signupSource?: InputMaybe<Scalars["String"]["input"]>;
  slug?: InputMaybe<Scalars["String"]["input"]>;
  subHeading?: InputMaybe<Scalars["String"]["input"]>;
  textColor?: InputMaybe<Scalars["String"]["input"]>;
  thumbnail?: InputMaybe<Scalars["String"]["input"]>;
};

export type InputCreateOemCustomer = {
  name: Scalars["String"]["input"];
};

export type InputCreateOemCustomerV2 = {
  facilityIdentifier?: InputMaybe<Scalars["String"]["input"]>;
  machines?: InputMaybe<Array<Scalars["ID"]["input"]>>;
  name: Scalars["String"]["input"];
  teams?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>;
  type?: InputMaybe<Scalars["String"]["input"]>;
  users?: InputMaybe<Array<InputCreateFacilityUserV2>>;
};

export type InputCreateOemSupportAccount = {
  email: Scalars["EmailAddress"]["input"];
  emailNotification?: InputMaybe<Scalars["Boolean"]["input"]>;
  info?: InputMaybe<Scalars["String"]["input"]>;
  name: Scalars["String"]["input"];
  teams?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>;
};

export type InputCreateOwnOemAsset = {
  assetType?: InputMaybe<Scalars["String"]["input"]>;
  customer?: InputMaybe<Scalars["ID"]["input"]>;
  description?: InputMaybe<Scalars["String"]["input"]>;
  image?: InputMaybe<Scalars["String"]["input"]>;
  name: Scalars["String"]["input"];
  serialNumber: Scalars["String"]["input"];
  teams?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>;
  template?: InputMaybe<Scalars["ID"]["input"]>;
  thumbnail?: InputMaybe<Scalars["String"]["input"]>;
};

export type InputCreateOwnOemAssetType = {
  name: Scalars["String"]["input"];
};

export type InputCreateOwnOemReport = {
  entity: Scalars["String"]["input"];
};

export type InputCreateOwnOemResource = {
  name: Scalars["String"]["input"];
  resourceId?: InputMaybe<Scalars["String"]["input"]>;
  type: Scalars["String"]["input"];
};

export type InputCreateOwnOemSkills = {
  names: Array<Scalars["String"]["input"]>;
};

export type InputCreateOwnOemTicket = {
  customFields?: InputMaybe<Array<InputMaybe<InputCustomField>>>;
  customerId: Scalars["ID"]["input"];
  description?: InputMaybe<Scalars["String"]["input"]>;
  inventoryParts?: InputMaybe<Array<InputMaybe<InputTicketMachinePart>>>;
  machineId: Scalars["ID"]["input"];
  threadId?: InputMaybe<Scalars["String"]["input"]>;
  ticketType: Scalars["ID"]["input"];
  title: Scalars["String"]["input"];
  userId?: InputMaybe<Scalars["ID"]["input"]>;
};

export type InputCreateOwnTicket = {
  customFields?: InputMaybe<Array<InputMaybe<InputCustomField>>>;
  description?: InputMaybe<Scalars["String"]["input"]>;
  inventoryParts?: InputMaybe<Array<InputMaybe<InputTicketMachinePart>>>;
  machineId: Scalars["ID"]["input"];
  ticketType: Scalars["ID"]["input"];
  title: Scalars["String"]["input"];
};

export type InputCreatePreventiveMaintenanceEvent = {
  description?: InputMaybe<Scalars["String"]["input"]>;
  eventDate: Scalars["DateTime"]["input"];
  frequency?: InputMaybe<Scalars["String"]["input"]>;
  inventoryParts?: InputMaybe<Array<InputMaybe<InputPmeInventoryPart>>>;
  machine: Scalars["ID"]["input"];
  oem: Scalars["ID"]["input"];
  procedures?: InputMaybe<Array<InputMaybe<InputPmeProcedure>>>;
  repeatIn?: InputMaybe<Scalars["String"]["input"]>;
  repeatInNumber?: InputMaybe<Scalars["Int"]["input"]>;
  startDate: Scalars["DateTime"]["input"];
  ticketCreationIn?: InputMaybe<Scalars["String"]["input"]>;
  ticketCreationNumber?: InputMaybe<Scalars["Int"]["input"]>;
  timezoneOffset?: InputMaybe<Scalars["Int"]["input"]>;
  title: Scalars["String"]["input"];
};

export type InputCreateRequest = {
  assetId: Scalars["ID"]["input"];
  customFields?: InputMaybe<Array<InputMaybe<InputCustomField>>>;
  description?: InputMaybe<Scalars["String"]["input"]>;
  inventoryParts?: InputMaybe<Array<InputMaybe<InputTicketMachinePart>>>;
  ticketType: Scalars["ID"]["input"];
  title: Scalars["String"]["input"];
};

export type InputCreateTeam = {
  customers?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>;
  description?: InputMaybe<Scalars["String"]["input"]>;
  machines?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>;
  name: Scalars["String"]["input"];
  teamColor?: InputMaybe<Scalars["String"]["input"]>;
  users?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>;
};

export type InputCustomField = {
  _id?: InputMaybe<Scalars["ID"]["input"]>;
  fieldId?: InputMaybe<Scalars["ID"]["input"]>;
  value?: InputMaybe<Scalars["JSON"]["input"]>;
  values?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>;
};

export type InputCustomFieldOrder = {
  _id: Scalars["ID"]["input"];
  order: Scalars["Int"]["input"];
};

export type InputCustomerPortalSignup = {
  companyName?: InputMaybe<Scalars["String"]["input"]>;
  contactId: Scalars["ID"]["input"];
  name: Scalars["String"]["input"];
};

export type InputDateRange = {
  endDate?: InputMaybe<Scalars["DateTime"]["input"]>;
  startDate?: InputMaybe<Scalars["DateTime"]["input"]>;
};

export type InputDeleteOemTicketType = {
  transferToTicketTypeId: Scalars["ID"]["input"];
};

export type InputDeleteOwnOemAssetType = {
  _id: Scalars["ID"]["input"];
};

export type InputDeleteOwnOemCustomAdditionalField = {
  _id: Scalars["ID"]["input"];
  customFieldsWithOrder?: InputMaybe<Array<InputMaybe<InputCustomFieldOrder>>>;
};

export type InputDeleteOwnOemResource = {
  _id: Scalars["ID"]["input"];
  resourceIds?: InputMaybe<Array<Scalars["ID"]["input"]>>;
};

export type InputDeleteOwnOemSkill = {
  _id: Scalars["ID"]["input"];
};

export type InputDeleteOwnOemTicket = {
  reason?: InputMaybe<Scalars["String"]["input"]>;
  ticketId: Scalars["ID"]["input"];
};

export type InputDescriptionConfig = {
  fieldName: Scalars["String"]["input"];
  isRequired?: InputMaybe<Scalars["Boolean"]["input"]>;
  show?: InputMaybe<Scalars["Boolean"]["input"]>;
};

export type InputDetachProcedureFromWorkOrder = {
  procedureId: Scalars["ID"]["input"];
  workOrderId: Scalars["ID"]["input"];
};

export type InputDownloadWopdf = {
  id: Scalars["String"]["input"];
  procedureIdsToInclude?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>;
  sectionsToShow?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>;
  timezone: Scalars["String"]["input"];
  uuid: Scalars["String"]["input"];
};

export type InputDuplicateOwnOemReport = {
  _id: Scalars["ID"]["input"];
  title?: InputMaybe<Scalars["String"]["input"]>;
};

export type InputEmailInlineAttachments = {
  content: Scalars["String"]["input"];
  content_disposition: Scalars["String"]["input"];
  content_id: Scalars["String"]["input"];
  content_type: Scalars["String"]["input"];
  filename: Scalars["String"]["input"];
  size: Scalars["Int"]["input"];
};

export type InputEmailNotificationSettings = {
  onAddedAsWorkOrderFollower?: InputMaybe<Scalars["Boolean"]["input"]>;
  onAssignedTicketInternalNotePost?: InputMaybe<Scalars["Boolean"]["input"]>;
  onAssignedWorkOrderReminder?: InputMaybe<InputAssignedWorkOrderReminder>;
  onAssignedWorkOrderUpdate?: InputMaybe<Scalars["Boolean"]["input"]>;
  onMentionedInWorkOrderInternalNote?: InputMaybe<Scalars["Boolean"]["input"]>;
  onMessageOnAssignedWorkOrder?: InputMaybe<Scalars["Boolean"]["input"]>;
  onNewTicketAssigned?: InputMaybe<Scalars["Boolean"]["input"]>;
};

export type InputFieldAttachment = {
  _id?: InputMaybe<Scalars["ID"]["input"]>;
  name?: InputMaybe<Scalars["String"]["input"]>;
  size?: InputMaybe<Scalars["String"]["input"]>;
  type?: InputMaybe<Scalars["String"]["input"]>;
  url: Scalars["String"]["input"];
};

export type InputFieldOption = {
  _id?: InputMaybe<Scalars["ID"]["input"]>;
  name?: InputMaybe<Scalars["String"]["input"]>;
};

export type InputFinalizeProcedure = {
  _id: Scalars["ID"]["input"];
  children?: InputMaybe<Array<InputMaybe<InputProcedureNodeValue>>>;
  signatures?: InputMaybe<Array<InputMaybe<InputSignatureValue>>>;
};

export type InputForwardedEmailAttachments = {
  contentDisposition: Scalars["String"]["input"];
  contentId?: InputMaybe<Scalars["String"]["input"]>;
  contentType: Scalars["String"]["input"];
  filename: Scalars["String"]["input"];
  grantId?: InputMaybe<Scalars["String"]["input"]>;
  id: Scalars["String"]["input"];
  isInline: Scalars["Boolean"]["input"];
  size: Scalars["Int"]["input"];
};

export type InputGetAnalytics = {
  _id?: InputMaybe<Scalars["ID"]["input"]>;
  chartType?: InputMaybe<Scalars["String"]["input"]>;
  entity?: InputMaybe<Scalars["String"]["input"]>;
  filters?: InputMaybe<Array<InputMaybe<InputReportFilter>>>;
  segment?: InputMaybe<InputReportField>;
  table?: InputMaybe<Array<InputMaybe<InputReportField>>>;
  withInputTableOptions?: InputMaybe<Scalars["Boolean"]["input"]>;
  xAxis?: InputMaybe<InputReportField>;
};

export type InputGetGeneralSignUpToken = {
  email: Scalars["String"]["input"];
  feature?: InputMaybe<Scalars["String"]["input"]>;
  redirectUrl?: InputMaybe<Scalars["String"]["input"]>;
};

export type InputHandleImportUploadId = {
  fillEmptyFieldsInDb?: InputMaybe<Scalars["Boolean"]["input"]>;
  id: Scalars["ID"]["input"];
  ignoreEmptyFieldsInFile?: InputMaybe<Scalars["Boolean"]["input"]>;
  templateId: Scalars["String"]["input"];
  templateSection: Scalars["String"]["input"];
};

export type InputHandleOwnOemAssetQrAccess = {
  assetId: Scalars["ID"]["input"];
  isQRCodeEnabled: Scalars["Boolean"]["input"];
};

export type InputImporters = {
  templateId: Scalars["String"]["input"];
  templateSection: Scalars["String"]["input"];
};

export type InputInsertCustomAdditionalField = {
  customFieldsWithOrder?: InputMaybe<Array<InputMaybe<InputCustomFieldOrder>>>;
  description?: InputMaybe<Scalars["String"]["input"]>;
  fieldType?: InputMaybe<FieldTypes>;
  isAdditionalField?: InputMaybe<Scalars["Boolean"]["input"]>;
  label?: InputMaybe<Scalars["String"]["input"]>;
  options?: InputMaybe<Array<InputMaybe<InputOption>>>;
  showOnCustomerPortal?: InputMaybe<Scalars["Boolean"]["input"]>;
  type?: InputMaybe<Types>;
};

export type InputInsertTag = {
  color?: InputMaybe<Scalars["String"]["input"]>;
  label?: InputMaybe<Scalars["String"]["input"]>;
  type?: InputMaybe<Types>;
};

export type InputInventoryQueryParams = {
  limit?: InputMaybe<Scalars["Int"]["input"]>;
  skip?: InputMaybe<Scalars["Int"]["input"]>;
  sort?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>;
  where?: InputMaybe<Scalars["MJSON"]["input"]>;
};

export type InputInviteContacts = {
  connectionId: Scalars["ID"]["input"];
  contactIds: Array<InputMaybe<Scalars["ID"]["input"]>>;
};

export type InputMachineHistoryNote = {
  attachments: Array<InputMaybe<InputMachineHistoryNoteAttachment>>;
  isInternal: Scalars["Boolean"]["input"];
  message: Scalars["String"]["input"];
};

export type InputMachineHistoryNoteAttachment = {
  _id?: InputMaybe<Scalars["ID"]["input"]>;
  name: Scalars["String"]["input"];
  size: Scalars["Int"]["input"];
  type: Scalars["String"]["input"];
  url: Scalars["String"]["input"];
};

export type InputMoveEmailToFolder = {
  emailAddressId: Scalars["ID"]["input"];
  folderId: Scalars["String"]["input"];
  messageIds: Array<Scalars["String"]["input"]>;
};

export type InputNotifyOnInternalNotePost = {
  mentionedUsers?: InputMaybe<Array<InputMaybe<Scalars["ID"]["input"]>>>;
  message?: InputMaybe<Scalars["String"]["input"]>;
  ticketId: Scalars["ID"]["input"];
};

export type InputOemCustomField = {
  _id?: InputMaybe<Scalars["ID"]["input"]>;
  customAdditionalField: Scalars["ID"]["input"];
  description?: InputMaybe<Scalars["String"]["input"]>;
  isRequired: Scalars["Boolean"]["input"];
};

export type InputOemPlans = {
  tier: Scalars["String"]["input"];
  type: Scalars["String"]["input"];
};

export type InputOperation = {
  operationType: Scalars["String"]["input"];
  ticket?: InputMaybe<InputTicket>;
  unavailable?: InputMaybe<InputUnavailable>;
};

export type InputOption = {
  _id?: InputMaybe<Scalars["ID"]["input"]>;
  color?: InputMaybe<Scalars["String"]["input"]>;
  description?: InputMaybe<Scalars["String"]["input"]>;
  value?: InputMaybe<Scalars["String"]["input"]>;
};

export type InputPmeInventoryPart = {
  part: Scalars["ID"]["input"];
  quantity: Scalars["Int"]["input"];
};

export type InputPmeProcedure = {
  procedure: Scalars["ID"]["input"];
};

export type InputPartsConfig = {
  fieldName: Scalars["String"]["input"];
  isRequired?: InputMaybe<Scalars["Boolean"]["input"]>;
  show?: InputMaybe<Scalars["Boolean"]["input"]>;
};

export type InputPlanTier = {
  allowedFeatures?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>;
  limits?: InputMaybe<Array<InputMaybe<InputTierLimit>>>;
  name?: InputMaybe<Scalars["String"]["input"]>;
  price?: InputMaybe<Scalars["Float"]["input"]>;
  type?: InputMaybe<Scalars["String"]["input"]>;
};

export type InputProcedureNodeValue = {
  _id: Scalars["ID"]["input"];
  children?: InputMaybe<Array<InputMaybe<InputProcedureNodeValue>>>;
  value?: InputMaybe<Scalars["Mixed"]["input"]>;
};

export type InputProcedureTemplateNode = {
  _id?: InputMaybe<Scalars["ID"]["input"]>;
  allowExtraRows?: InputMaybe<Scalars["Boolean"]["input"]>;
  attachments?: InputMaybe<Array<InputMaybe<InputFieldAttachment>>>;
  children?: InputMaybe<Array<InputMaybe<InputProcedureTemplateNode>>>;
  description?: InputMaybe<Scalars["String"]["input"]>;
  isRequired?: InputMaybe<Scalars["Boolean"]["input"]>;
  name?: InputMaybe<Scalars["String"]["input"]>;
  options?: InputMaybe<Array<InputMaybe<InputFieldOption>>>;
  tableOption?: InputMaybe<InputTableOption>;
  type: NodeAndFieldTypes;
  value?: InputMaybe<Array<InputMaybe<Scalars["JSON"]["input"]>>>;
};

export type InputProcedureTemplateSignature = {
  _id?: InputMaybe<Scalars["ID"]["input"]>;
  signatoryTitle?: InputMaybe<Scalars["String"]["input"]>;
};

export type InputProductAccess = {
  _3dModel?: InputMaybe<Scalars["Boolean"]["input"]>;
  allowCopying?: InputMaybe<Scalars["Boolean"]["input"]>;
  documentation?: InputMaybe<Scalars["Boolean"]["input"]>;
  parts?: InputMaybe<Scalars["Boolean"]["input"]>;
  procedures?: InputMaybe<Scalars["Boolean"]["input"]>;
};

export type InputProductPlan = {
  conflictingPurchases?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>;
  name?: InputMaybe<Scalars["String"]["input"]>;
  tiers?: InputMaybe<Array<InputMaybe<InputPlanTier>>>;
  type?: InputMaybe<Scalars["String"]["input"]>;
};

export type InputPublishAiNote = {
  id: Scalars["ID"]["input"];
  machines: Array<Scalars["ID"]["input"]>;
};

export type InputPublishTicketScheduleDraft = {
  assignees?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>;
  dateRange: InputDateRange;
  version?: InputMaybe<Scalars["Int"]["input"]>;
};

export type InputQueryAiAssistant = {
  chatID?: InputMaybe<Scalars["String"]["input"]>;
  dbChatId?: InputMaybe<Scalars["String"]["input"]>;
  id: Scalars["String"]["input"];
  query: Scalars["String"]["input"];
};

export type InputQueryOem = {
  limit?: InputMaybe<Scalars["Int"]["input"]>;
  skip?: InputMaybe<Scalars["Int"]["input"]>;
  sort?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>;
  where?: InputMaybe<Scalars["MJSON"]["input"]>;
};

export type InputQueryParams = {
  limit?: InputMaybe<Scalars["Int"]["input"]>;
  skip?: InputMaybe<Scalars["Int"]["input"]>;
  sort?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>;
  where?: InputMaybe<Scalars["MJSON"]["input"]>;
};

export type InputRemoveAiAssistantDocuments = {
  _id: Scalars["ID"]["input"];
  documentIds: Array<Scalars["String"]["input"]>;
};

export type InputRemoveAssetInventoryPart = {
  assetId: Scalars["ID"]["input"];
  partId: Scalars["ID"]["input"];
};

export type InputRemoveAssetTemplateInventoryPart = {
  assetTemplateId: Scalars["ID"]["input"];
  partId: Scalars["ID"]["input"];
};

export type InputRemoveChannelsMemberships = {
  channels: Array<InputMaybe<Scalars["String"]["input"]>>;
};

export type InputRemoveInventoryPartFromTicket = {
  partId: Scalars["ID"]["input"];
  ticketId: Scalars["ID"]["input"];
};

export type InputRemoveOwnOemAssetFromOwnOemCustomer = {
  asset: Scalars["ID"]["input"];
  customer: Scalars["ID"]["input"];
};

export type InputRemoveTicketAssignee = {
  assignee?: InputMaybe<Scalars["ID"]["input"]>;
  removeAll?: InputMaybe<Scalars["Boolean"]["input"]>;
  ticketId: Scalars["ID"]["input"];
};

export type InputRemoveTicketAttachment = {
  attachmentId: Scalars["String"]["input"];
  ticketId: Scalars["ID"]["input"];
};

export type InputRemoveTicketFollower = {
  follower?: InputMaybe<Scalars["ID"]["input"]>;
  removeAll?: InputMaybe<Scalars["Boolean"]["input"]>;
  ticketId: Scalars["ID"]["input"];
};

export type InputRemoveTicketResources = {
  resources: Array<Scalars["ID"]["input"]>;
  ticketId: Scalars["ID"]["input"];
};

export type InputRemoveTimeTrackerLog = {
  ticketId: Scalars["ID"]["input"];
  timeLogId: Scalars["ID"]["input"];
};

export type InputRenameAiAssistant = {
  _id: Scalars["ID"]["input"];
  assistantName: Scalars["String"]["input"];
};

export type InputRenameAiAssistantChat = {
  aiAssistantChatId: Scalars["ID"]["input"];
  aiAssistantChatName: Scalars["String"]["input"];
};

export type InputReportField = {
  label?: InputMaybe<Scalars["String"]["input"]>;
  value?: InputMaybe<Scalars["Mixed"]["input"]>;
};

export type InputReportFilter = {
  condition?: InputMaybe<Scalars["String"]["input"]>;
  field?: InputMaybe<Scalars["String"]["input"]>;
  filterType?: InputMaybe<Scalars["String"]["input"]>;
  value?: InputMaybe<Array<InputMaybe<InputReportField>>>;
};

export type InputResetAssetToTemplate = {
  assetId: Scalars["ID"]["input"];
  fieldsToReset: Array<Scalars["String"]["input"]>;
};

export type InputResultDocument = {
  answer?: InputMaybe<Scalars["String"]["input"]>;
  documentChunks?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>;
  documentId?: InputMaybe<Scalars["String"]["input"]>;
  documentTitle?: InputMaybe<Scalars["String"]["input"]>;
};

export type InputSaveProcedure = {
  _id: Scalars["ID"]["input"];
  children?: InputMaybe<Array<InputMaybe<InputProcedureNodeValue>>>;
  signatures?: InputMaybe<Array<InputMaybe<InputSignatureValue>>>;
};

export type InputSaveProcedureTemplate = {
  _id?: InputMaybe<Scalars["ID"]["input"]>;
  allowUsersToAddMoreSignatures?: InputMaybe<Scalars["Boolean"]["input"]>;
  children?: InputMaybe<Array<InputMaybe<InputProcedureTemplateNode>>>;
  description?: InputMaybe<Scalars["String"]["input"]>;
  name: Scalars["String"]["input"];
  pageHeader?: InputMaybe<InputFieldAttachment>;
  signatures?: InputMaybe<Array<InputMaybe<InputProcedureTemplateSignature>>>;
};

export type InputSendEmail = {
  bcc?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>;
  body?: InputMaybe<Scalars["String"]["input"]>;
  cc?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>;
  draftId?: InputMaybe<Scalars["String"]["input"]>;
  emailAddressId: Scalars["ID"]["input"];
  fileUrls?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>;
  forwardedAttachments?: InputMaybe<Array<InputMaybe<InputForwardedEmailAttachments>>>;
  inlineAttachments?: InputMaybe<Array<InputMaybe<InputEmailInlineAttachments>>>;
  replyToMessageId?: InputMaybe<Scalars["String"]["input"]>;
  subject?: InputMaybe<Scalars["String"]["input"]>;
  ticketId?: InputMaybe<Scalars["String"]["input"]>;
  to?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>;
  uuid?: InputMaybe<Scalars["String"]["input"]>;
};

export type InputSignatureValue = {
  _id?: InputMaybe<Scalars["ID"]["input"]>;
  date?: InputMaybe<Scalars["DateTime"]["input"]>;
  isAdditionalSignature?: InputMaybe<Scalars["Boolean"]["input"]>;
  name?: InputMaybe<Scalars["String"]["input"]>;
  signatoryTitle?: InputMaybe<Scalars["String"]["input"]>;
  signatureUrl?: InputMaybe<Scalars["String"]["input"]>;
};

export type InputStatuses = {
  _id?: InputMaybe<Scalars["ID"]["input"]>;
  color: Scalars["String"]["input"];
  label: Scalars["String"]["input"];
};

export type InputTableColumn = {
  _id?: InputMaybe<Scalars["ID"]["input"]>;
  heading?: InputMaybe<Scalars["String"]["input"]>;
  width?: InputMaybe<Scalars["Int"]["input"]>;
};

export type InputTableOption = {
  _id?: InputMaybe<Scalars["ID"]["input"]>;
  columns?: InputMaybe<Array<InputMaybe<InputTableColumn>>>;
  rowCount?: InputMaybe<Scalars["Int"]["input"]>;
};

export type InputTicket = {
  assignees?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>;
  id?: InputMaybe<Scalars["String"]["input"]>;
  resources?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>;
  schedule?: InputMaybe<ScheduleInputType>;
  status?: InputMaybe<Scalars["String"]["input"]>;
};

export type InputTicketInventoryPartPayload = {
  part: Scalars["ID"]["input"];
  quantity: Scalars["Int"]["input"];
};

export type InputTicketMachinePart = {
  addedBy: Scalars["ID"]["input"];
  part: Scalars["ID"]["input"];
  quantity: Scalars["Int"]["input"];
};

export type InputTicketType = {
  color: Scalars["String"]["input"];
  icon: Scalars["String"]["input"];
  isInternal?: InputMaybe<Scalars["Boolean"]["input"]>;
  name: Scalars["String"]["input"];
};

export type InputTicketTypeConfig = {
  customFields?: InputMaybe<Array<InputMaybe<InputOemCustomField>>>;
  descriptionConfig?: InputMaybe<InputDescriptionConfig>;
  partsConfig?: InputMaybe<InputPartsConfig>;
};

export type InputTierLimit = {
  type?: InputMaybe<Scalars["String"]["input"]>;
  value?: InputMaybe<Scalars["Float"]["input"]>;
};

export type InputTranscribeAiNote = {
  aiNoteId: Scalars["ID"]["input"];
};

export type InputTranslateAiNoteSummary = {
  aiNoteId: Scalars["ID"]["input"];
  targetLanguageCode: Scalars["String"]["input"];
};

export type InputUnassignAssetFromParent = {
  assetId: Scalars["ID"]["input"];
  parentId: Scalars["ID"]["input"];
};

export type InputUnassignAssetFromTeam = {
  asset: Scalars["ID"]["input"];
  team: Scalars["ID"]["input"];
};

export type InputUnassignCustomerFromTeam = {
  customer: Scalars["ID"]["input"];
  team: Scalars["ID"]["input"];
};

export type InputUnassignUserFromTeam = {
  team: Scalars["ID"]["input"];
  user: Scalars["ID"]["input"];
};

export type InputUnavailable = {
  id?: InputMaybe<Scalars["String"]["input"]>;
  schedule?: InputMaybe<ScheduleInputType>;
  scheduleType?: InputMaybe<Scalars["String"]["input"]>;
  toDelete?: InputMaybe<Scalars["Boolean"]["input"]>;
  user?: InputMaybe<Scalars["String"]["input"]>;
};

export type InputUpdate3DGuide = {
  _id: Scalars["ID"]["input"];
  image: Scalars["String"]["input"];
  sessionId: Scalars["String"]["input"];
};

export type InputUpdateAiAssistant = {
  _id: Scalars["ID"]["input"];
  assistantName: Scalars["String"]["input"];
  description: Scalars["String"]["input"];
};

export type InputUpdateAiNoteSummary = {
  id: Scalars["ID"]["input"];
  summary: Scalars["String"]["input"];
};

export type InputUpdateAiNoteTitle = {
  id: Scalars["ID"]["input"];
  title: Scalars["String"]["input"];
};

export type InputUpdateAssetTemplate = {
  _id: Scalars["ID"]["input"];
  description?: InputMaybe<Scalars["String"]["input"]>;
  image?: InputMaybe<Scalars["String"]["input"]>;
  templateId?: InputMaybe<Scalars["String"]["input"]>;
  thumbnail?: InputMaybe<Scalars["String"]["input"]>;
  title?: InputMaybe<Scalars["String"]["input"]>;
  visibility?: InputMaybe<Scalars["Int"]["input"]>;
};

export type InputUpdateContact = {
  _id: Scalars["ID"]["input"];
  email?: InputMaybe<Scalars["String"]["input"]>;
  jobTitle?: InputMaybe<Scalars["String"]["input"]>;
  landline?: InputMaybe<Scalars["String"]["input"]>;
  name?: InputMaybe<Scalars["String"]["input"]>;
  phoneNumber?: InputMaybe<Scalars["String"]["input"]>;
};

export type InputUpdateCrmOem = {
  _id: Scalars["ID"]["input"];
  adminEmail?: InputMaybe<Scalars["String"]["input"]>;
  adminName?: InputMaybe<Scalars["String"]["input"]>;
  backgroundColor?: InputMaybe<Scalars["String"]["input"]>;
  brandLogo?: InputMaybe<Scalars["String"]["input"]>;
  heading?: InputMaybe<Scalars["String"]["input"]>;
  logo?: InputMaybe<Scalars["String"]["input"]>;
  name?: InputMaybe<Scalars["String"]["input"]>;
  paidFeatures?: InputMaybe<Array<InputMaybe<PaidFeaturesEnum>>>;
  paragraph?: InputMaybe<Scalars["String"]["input"]>;
  slug?: InputMaybe<Scalars["String"]["input"]>;
  subHeading?: InputMaybe<Scalars["String"]["input"]>;
  textColor?: InputMaybe<Scalars["String"]["input"]>;
  thumbnail?: InputMaybe<Scalars["String"]["input"]>;
  usageTracking?: InputMaybe<InputUsageTracking>;
};

export type InputUpdateCustomAdditionalField = {
  _id: Scalars["ID"]["input"];
  description?: InputMaybe<Scalars["String"]["input"]>;
  fieldType?: InputMaybe<FieldTypes>;
  isAdditionalField?: InputMaybe<Scalars["Boolean"]["input"]>;
  label?: InputMaybe<Scalars["String"]["input"]>;
  options?: InputMaybe<Array<InputMaybe<InputOption>>>;
  showOnCustomerPortal?: InputMaybe<Scalars["Boolean"]["input"]>;
  type?: InputMaybe<Types>;
};

export type InputUpdateCustomFieldsOrder = {
  customFieldsWithOrder: Array<InputMaybe<InputCustomFieldOrder>>;
};

export type InputUpdateCustomerPortal = {
  _id: Scalars["ID"]["input"];
  assetAccess?: InputMaybe<InputAssetAccess>;
  productAccess?: InputMaybe<InputProductAccess>;
};

export type InputUpdateDescriptionTicket = {
  description: Scalars["String"]["input"];
  ticketId: Scalars["ID"]["input"];
};

export type InputUpdateDocument = {
  _id: Scalars["ID"]["input"];
  customFields?: InputMaybe<Array<InputMaybe<InputCustomField>>>;
};

export type InputUpdateEmailThread = {
  emailAddressId: Scalars["ID"]["input"];
  isDraft?: InputMaybe<Scalars["Boolean"]["input"]>;
  threadId: Scalars["String"]["input"];
  ticketId?: InputMaybe<Scalars["ID"]["input"]>;
  unread?: InputMaybe<Scalars["Boolean"]["input"]>;
};

export type InputUpdateFacilityUser = {
  _id: Scalars["ID"]["input"];
  access?: InputMaybe<Scalars["Boolean"]["input"]>;
  email?: InputMaybe<Scalars["EmailAddress"]["input"]>;
  info?: InputMaybe<Scalars["String"]["input"]>;
  mobile?: InputMaybe<Scalars["String"]["input"]>;
  name?: InputMaybe<Scalars["String"]["input"]>;
  phone?: InputMaybe<Scalars["String"]["input"]>;
};

export type InputUpdateInventoryPart = {
  _id: Scalars["ID"]["input"];
  articleNumber?: InputMaybe<Scalars["String"]["input"]>;
  customFields?: InputMaybe<Array<InputMaybe<InputCustomField>>>;
  description?: InputMaybe<Scalars["String"]["input"]>;
  image?: InputMaybe<Scalars["String"]["input"]>;
  name?: InputMaybe<Scalars["String"]["input"]>;
  thumbnail?: InputMaybe<Scalars["String"]["input"]>;
};

export type InputUpdateKnowledgeBase = {
  description?: InputMaybe<Scalars["String"]["input"]>;
  title?: InputMaybe<Scalars["String"]["input"]>;
};

export type InputUpdateMachineHistoryNote = {
  _id: Scalars["ID"]["input"];
  machine: Scalars["String"]["input"];
  note: InputMachineHistoryNote;
};

export type InputUpdateOemAiAssistantConfiguration = {
  autoApproveOCR?: InputMaybe<Scalars["Boolean"]["input"]>;
};

export type InputUpdateOemCustomer = {
  _id: Scalars["ID"]["input"];
  assetAccess?: InputMaybe<InputAssetAccess>;
  customFields?: InputMaybe<Array<InputMaybe<InputCustomField>>>;
  description?: InputMaybe<Scalars["String"]["input"]>;
  facilityId?: InputMaybe<Scalars["String"]["input"]>;
  isMachineDocumentationEnabled?: InputMaybe<Scalars["Boolean"]["input"]>;
  isPreventiveMaintenanceEventsEnabled?: InputMaybe<Scalars["Boolean"]["input"]>;
  name?: InputMaybe<Scalars["String"]["input"]>;
  qrCodeAccess?: InputMaybe<Scalars["String"]["input"]>;
  type?: InputMaybe<Scalars["String"]["input"]>;
};

export type InputUpdateOemSupportAccount = {
  _id: Scalars["ID"]["input"];
  email?: InputMaybe<Scalars["EmailAddress"]["input"]>;
  emailNotification?: InputMaybe<Scalars["Boolean"]["input"]>;
  hasAiAssistantAccess?: InputMaybe<Scalars["Boolean"]["input"]>;
  info?: InputMaybe<Scalars["String"]["input"]>;
  name?: InputMaybe<Scalars["String"]["input"]>;
  productAccess?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>;
  role?: InputMaybe<Scalars["String"]["input"]>;
};

export type InputUpdateOemTicketType = {
  color?: InputMaybe<Scalars["String"]["input"]>;
  icon?: InputMaybe<Scalars["String"]["input"]>;
  isInternal?: InputMaybe<Scalars["Boolean"]["input"]>;
  name?: InputMaybe<Scalars["String"]["input"]>;
};

export type InputUpdateOwnOem = {
  allowAssigneesAcrossTeams?: InputMaybe<Scalars["Boolean"]["input"]>;
  allowFollowersMyWorkOrders?: InputMaybe<Scalars["Boolean"]["input"]>;
  allowTechnicianToAssignWorkOrders?: InputMaybe<Scalars["Boolean"]["input"]>;
  backgroundColor?: InputMaybe<Scalars["String"]["input"]>;
  brandLogo?: InputMaybe<Scalars["String"]["input"]>;
  coverPhoto?: InputMaybe<Scalars["String"]["input"]>;
  crop?: InputMaybe<Scalars["JSON"]["input"]>;
  description?: InputMaybe<Scalars["String"]["input"]>;
  heading?: InputMaybe<Scalars["String"]["input"]>;
  headline?: InputMaybe<Scalars["String"]["input"]>;
  location?: InputMaybe<Scalars["String"]["input"]>;
  logo?: InputMaybe<Scalars["String"]["input"]>;
  maintenanceWorkOrderCreationNotifyTo?: InputMaybe<Array<InputMaybe<Scalars["ID"]["input"]>>>;
  messageOnUnassignedWorkOrderNotifyTo?: InputMaybe<Array<InputMaybe<Scalars["ID"]["input"]>>>;
  name?: InputMaybe<Scalars["String"]["input"]>;
  notifyOnMaintenanceWorkOrderCreation?: InputMaybe<Scalars["Boolean"]["input"]>;
  notifyOnMessageOnUnassignedWorkOrder?: InputMaybe<Scalars["Boolean"]["input"]>;
  notifyOnWorkOrderCreation?: InputMaybe<Scalars["Boolean"]["input"]>;
  paragraph?: InputMaybe<Scalars["String"]["input"]>;
  statusToRemove?: InputMaybe<Scalars["String"]["input"]>;
  statusToReplace?: InputMaybe<Scalars["String"]["input"]>;
  statuses?: InputMaybe<Array<InputMaybe<InputStatuses>>>;
  subHeading?: InputMaybe<Scalars["String"]["input"]>;
  textColor?: InputMaybe<Scalars["String"]["input"]>;
  thumbnail?: InputMaybe<Scalars["String"]["input"]>;
  ticketTypes?: InputMaybe<Array<InputMaybe<InputTicketType>>>;
  website?: InputMaybe<Scalars["String"]["input"]>;
  workOrderCreationNotifyTo?: InputMaybe<Array<InputMaybe<InputWorkOrderCreationNotifyTo>>>;
};

export type InputUpdateOwnOemAsset = {
  _id: Scalars["ID"]["input"];
  assetType?: InputMaybe<Scalars["ID"]["input"]>;
  customFields?: InputMaybe<Array<InputMaybe<InputCustomField>>>;
  description?: InputMaybe<Scalars["String"]["input"]>;
  image?: InputMaybe<Scalars["String"]["input"]>;
  isQRCodeEnabled?: InputMaybe<Scalars["Boolean"]["input"]>;
  name?: InputMaybe<Scalars["String"]["input"]>;
  serialNumber?: InputMaybe<Scalars["String"]["input"]>;
  template?: InputMaybe<Scalars["ID"]["input"]>;
  thumbnail?: InputMaybe<Scalars["String"]["input"]>;
};

export type InputUpdateOwnOemAssetType = {
  _id: Scalars["ID"]["input"];
  name: Scalars["String"]["input"];
};

export type InputUpdateOwnOemReport = {
  _id: Scalars["ID"]["input"];
  chartType?: InputMaybe<Scalars["String"]["input"]>;
  filters?: InputMaybe<Array<InputMaybe<InputReportFilter>>>;
  segment?: InputMaybe<InputReportField>;
  table?: InputMaybe<Array<InputMaybe<InputReportField>>>;
  title?: InputMaybe<Scalars["String"]["input"]>;
  xAxis?: InputMaybe<InputReportField>;
};

export type InputUpdateOwnOemResource = {
  _id: Scalars["ID"]["input"];
  name: Scalars["String"]["input"];
  resourceId?: InputMaybe<Scalars["String"]["input"]>;
  type: Scalars["String"]["input"];
};

export type InputUpdateOwnOemSkill = {
  _id: Scalars["ID"]["input"];
  name: Scalars["String"]["input"];
};

export type InputUpdateOwnOemTicket = {
  assignees?: InputMaybe<Array<InputMaybe<Scalars["ID"]["input"]>>>;
  customFields?: InputMaybe<Array<InputMaybe<InputCustomField>>>;
  facility?: InputMaybe<Scalars["ID"]["input"]>;
  notes?: InputMaybe<Scalars["String"]["input"]>;
  schedule?: InputMaybe<ScheduleInputType>;
  status?: InputMaybe<Scalars["ID"]["input"]>;
  ticketId: Scalars["ID"]["input"];
  ticketType?: InputMaybe<Scalars["ID"]["input"]>;
  /** TODO: Remove unread */
  unread?: InputMaybe<Scalars["Boolean"]["input"]>;
  user?: InputMaybe<Scalars["ID"]["input"]>;
};

export type InputUpdatePersonalSettings = {
  name?: InputMaybe<Scalars["String"]["input"]>;
  newConfirmPassword?: InputMaybe<Scalars["Base64"]["input"]>;
  newPassword?: InputMaybe<Scalars["Base64"]["input"]>;
  oldPassword?: InputMaybe<Scalars["Base64"]["input"]>;
};

export type InputUpdatePreventiveMaintenanceEvent = {
  _id: Scalars["ID"]["input"];
  description?: InputMaybe<Scalars["String"]["input"]>;
  eventDate: Scalars["DateTime"]["input"];
  frequency?: InputMaybe<Scalars["String"]["input"]>;
  inventoryParts?: InputMaybe<Array<InputMaybe<InputPmeInventoryPart>>>;
  machine: Scalars["ID"]["input"];
  oem: Scalars["ID"]["input"];
  procedures?: InputMaybe<Array<InputMaybe<InputPmeProcedure>>>;
  repeatIn?: InputMaybe<Scalars["String"]["input"]>;
  repeatInNumber?: InputMaybe<Scalars["Int"]["input"]>;
  ticketCreationIn?: InputMaybe<Scalars["String"]["input"]>;
  ticketCreationNumber?: InputMaybe<Scalars["Int"]["input"]>;
  title: Scalars["String"]["input"];
};

export type InputUpdateSelfFacilityUser = {
  about?: InputMaybe<Scalars["String"]["input"]>;
  name?: InputMaybe<Scalars["String"]["input"]>;
  phone?: InputMaybe<Scalars["String"]["input"]>;
};

export type InputUpdateTag = {
  _id: Scalars["ID"]["input"];
  color?: InputMaybe<Scalars["String"]["input"]>;
  label?: InputMaybe<Scalars["String"]["input"]>;
  type?: InputMaybe<Types>;
};

export type InputUpdateTeam = {
  _id: Scalars["ID"]["input"];
  customers?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>;
  description?: InputMaybe<Scalars["String"]["input"]>;
  machines?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>;
  name?: InputMaybe<Scalars["String"]["input"]>;
  teamColor?: InputMaybe<Scalars["String"]["input"]>;
  users?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>;
};

export type InputUpdateTicketScheduleDraft = {
  operations: Array<InputOperation>;
  version?: InputMaybe<Scalars["Int"]["input"]>;
};

export type InputUpdateTimeTrackerLog = {
  ticketId: Scalars["ID"]["input"];
  timeLogId: Scalars["ID"]["input"];
  timeTrackerLog?: InputMaybe<InputAddTimeTrackerLogPayload>;
};

export type InputUpdateTitleTicket = {
  ticketId: Scalars["ID"]["input"];
  title: Scalars["String"]["input"];
};

export type InputUploadSearchFeedback = {
  description?: InputMaybe<Scalars["String"]["input"]>;
  queryText?: InputMaybe<Scalars["String"]["input"]>;
  resultDocuments?: InputMaybe<Array<InputMaybe<InputResultDocument>>>;
};

export type InputUsageTracking = {
  logRocket?: InputMaybe<Scalars["Boolean"]["input"]>;
};

export type InputUserBoard = {
  boardFor: BoardFor;
  fieldId: Scalars["ID"]["input"];
};

export type InputWorkOrderCreationNotifyTo = {
  user?: InputMaybe<Scalars["ID"]["input"]>;
  workOrderTypes?: InputMaybe<Array<InputMaybe<Scalars["ID"]["input"]>>>;
};

export type InstalledProduct = {
  __typename?: "InstalledProduct";
  tier?: Maybe<Scalars["String"]["output"]>;
  type?: Maybe<Scalars["String"]["output"]>;
};

export enum IntegrationCustomersEnum {
  Hocap = "hocap",
  Hoogwerkservice = "hoogwerkservice",
}

export type InventoryPart = {
  __typename?: "InventoryPart";
  _id?: Maybe<Scalars["ID"]["output"]>;
  accessToken?: Maybe<Scalars["String"]["output"]>;
  articleNumber?: Maybe<Scalars["String"]["output"]>;
  createdAt?: Maybe<Scalars["DateTime"]["output"]>;
  createdBy?: Maybe<Scalars["ID"]["output"]>;
  customFields?: Maybe<Array<Maybe<CustomField>>>;
  description?: Maybe<Scalars["String"]["output"]>;
  documentFolders?: Maybe<DocumentFolders>;
  image?: Maybe<Scalars["String"]["output"]>;
  machines?: Maybe<Array<Maybe<Asset>>>;
  name?: Maybe<Scalars["String"]["output"]>;
  oem?: Maybe<Oem>;
  thumbnail?: Maybe<Scalars["String"]["output"]>;
  tickets?: Maybe<Array<Maybe<Ticket>>>;
};

export type KanBanAssetType = {
  __typename?: "KanBanAssetType";
  _id?: Maybe<Scalars["ID"]["output"]>;
  label?: Maybe<Scalars["String"]["output"]>;
};

export type KanbanColumnAsset = {
  __typename?: "KanbanColumnAsset";
  _id?: Maybe<Scalars["Int"]["output"]>;
  assetType?: Maybe<KanBanAssetType>;
  boardFor?: Maybe<Scalars["String"]["output"]>;
  cards?: Maybe<Array<Maybe<Asset>>>;
  column?: Maybe<Scalars["String"]["output"]>;
  columnIndex?: Maybe<Scalars["Int"]["output"]>;
  currentPage?: Maybe<Scalars["Int"]["output"]>;
  customField?: Maybe<Scalars["ID"]["output"]>;
  limit?: Maybe<Scalars["Int"]["output"]>;
  skip?: Maybe<Scalars["Int"]["output"]>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
  type?: Maybe<Scalars["String"]["output"]>;
};

export type KanbanColumnCustomer = {
  __typename?: "KanbanColumnCustomer";
  _id?: Maybe<Scalars["Int"]["output"]>;
  boardFor?: Maybe<Scalars["String"]["output"]>;
  cards?: Maybe<Array<Maybe<Customer>>>;
  column?: Maybe<Scalars["String"]["output"]>;
  columnIndex?: Maybe<Scalars["Int"]["output"]>;
  currentPage?: Maybe<Scalars["Int"]["output"]>;
  customField?: Maybe<Scalars["ID"]["output"]>;
  limit?: Maybe<Scalars["Int"]["output"]>;
  skip?: Maybe<Scalars["Int"]["output"]>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
  type?: Maybe<Scalars["String"]["output"]>;
};

export type KanbanColumnTicket = {
  __typename?: "KanbanColumnTicket";
  _id?: Maybe<Scalars["Int"]["output"]>;
  boardFor?: Maybe<Scalars["String"]["output"]>;
  cards?: Maybe<Array<Maybe<Ticket>>>;
  column?: Maybe<Scalars["String"]["output"]>;
  columnIndex?: Maybe<Scalars["Int"]["output"]>;
  currentPage?: Maybe<Scalars["Int"]["output"]>;
  customField?: Maybe<Scalars["ID"]["output"]>;
  limit?: Maybe<Scalars["Int"]["output"]>;
  skip?: Maybe<Scalars["Int"]["output"]>;
  status?: Maybe<Status>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
  type?: Maybe<Scalars["String"]["output"]>;
};

export type KnowledgeBase = {
  __typename?: "KnowledgeBase";
  _id?: Maybe<Scalars["ID"]["output"]>;
  createdBy?: Maybe<User>;
  description?: Maybe<Scalars["String"]["output"]>;
  folderId?: Maybe<Scalars["String"]["output"]>;
  supportedLanguages?: Maybe<Array<Maybe<Scalars["String"]["output"]>>>;
  title?: Maybe<Scalars["String"]["output"]>;
  updatedBy?: Maybe<User>;
};

export type LinkedTicket = {
  __typename?: "LinkedTicket";
  linkedAt?: Maybe<Scalars["DateTime"]["output"]>;
  linkedBy?: Maybe<User>;
  ticket?: Maybe<Ticket>;
};

export type MachineHistory = {
  __typename?: "MachineHistory";
  _id?: Maybe<Scalars["ID"]["output"]>;
  createdAt?: Maybe<Scalars["DateTime"]["output"]>;
  createdBy?: Maybe<User>;
  customer?: Maybe<Customer>;
  machine?: Maybe<Scalars["ID"]["output"]>;
  machineDetails?: Maybe<Asset>;
  month?: Maybe<Scalars["Int"]["output"]>;
  note?: Maybe<MachineHistoryNote>;
  resource?: Maybe<Scalars["MJSON"]["output"]>;
  resourceId?: Maybe<Scalars["ID"]["output"]>;
  ticket?: Maybe<Ticket>;
  type?: Maybe<Scalars["String"]["output"]>;
  updatedBy?: Maybe<User>;
  year?: Maybe<Scalars["Int"]["output"]>;
};

export type MachineHistoryNote = {
  __typename?: "MachineHistoryNote";
  attachments: Array<Maybe<MachineHistoryNoteAttachment>>;
  isInternal?: Maybe<Scalars["Boolean"]["output"]>;
  message?: Maybe<Scalars["String"]["output"]>;
};

export type MachineHistoryNoteAttachment = {
  __typename?: "MachineHistoryNoteAttachment";
  createdAt?: Maybe<Scalars["DateTime"]["output"]>;
  name: Scalars["String"]["output"];
  size: Scalars["Int"]["output"];
  type: Scalars["String"]["output"];
  url: Scalars["String"]["output"];
};

export type Mutation = {
  __typename?: "Mutation";
  _completeMultiPartUploadS3: CompleteMultiPartUploadPayload;
  _createMultiPartUploadS3: CreateMultiPartUploadPayload;
  _safeSignS3: S3Payload;
  _signS3Download: S3Payload;
  _signS3MultiDownload?: Maybe<Array<S3Payload>>;
  acceptConnectionRequest?: Maybe<Scalars["String"]["output"]>;
  activeInactiveOwnOemAdditionalField?: Maybe<CustomAdditionalField>;
  addAiAssistantDocuments?: Maybe<Scalars["ID"]["output"]>;
  addOwnOemAsset3DModel?: Maybe<Asset>;
  addOwnOemAssetTemplate3DModel?: Maybe<AssetTemplate>;
  addTicketAssignee?: Maybe<Ticket>;
  addTicketAttachments?: Maybe<Ticket>;
  addTicketFollower?: Maybe<Ticket>;
  addTicketResource?: Maybe<Ticket>;
  addTimeTrackerLog?: Maybe<Ticket>;
  addUserBoard?: Maybe<User>;
  approveInvite?: Maybe<Scalars["String"]["output"]>;
  approveOcrDocuments?: Maybe<Scalars["String"]["output"]>;
  assignAssetsToParent?: Maybe<Asset>;
  assignMultipleAssetsToTeam?: Maybe<Array<Maybe<Asset>>>;
  assignMultipleCustomersToTeam?: Maybe<Array<Maybe<Customer>>>;
  assignMultipleSkillsToOemUser?: Maybe<User>;
  assignMultipleUsersToTeam?: Maybe<Array<Maybe<User>>>;
  assignOwnOemInventoryPartsToAsset?: Maybe<Asset>;
  assignOwnOemInventoryPartsToAssetTemplate?: Maybe<AssetTemplate>;
  assignOwnOemInventoryPartsToTicket?: Maybe<Ticket>;
  assignOwnOemMultipleAssetsToOwnOemCustomer?: Maybe<Array<Maybe<Asset>>>;
  assignTicketType?: Maybe<Team>;
  attachOwnOemProcedureToWorkOrder?: Maybe<Scalars["String"]["output"]>;
  attachProceduresToTemplate?: Maybe<AssetTemplate>;
  checkValidFacilityUser?: Maybe<Scalars["Boolean"]["output"]>;
  configureOemTicketType?: Maybe<TicketType>;
  create3DGuide?: Maybe<Guide>;
  createAiAssistant?: Maybe<AiAssistant>;
  createAiAssistantChat?: Maybe<AiAssistantChat>;
  createAiAssistantV2?: Maybe<AiAssistant>;
  createAiNote?: Maybe<AiNote>;
  createConnectionRequest?: Maybe<Scalars["String"]["output"]>;
  createContact?: Maybe<Contact>;
  createCustomerPortal?: Maybe<CustomerPortal>;
  createDocuments?: Maybe<Scalars["String"]["output"]>;
  createMachineHistoryNote?: Maybe<MachineHistory>;
  createOem?: Maybe<Oem>;
  createOemAPI?: Maybe<OemApi>;
  createOemCustomer?: Maybe<Customer>;
  createOemCustomerV2?: Maybe<Customer>;
  createOemFacilityUser?: Maybe<User>;
  createOemSupportAccount?: Maybe<User>;
  createOemTicketType?: Maybe<TicketType>;
  createOwnOemAsset?: Maybe<Asset>;
  createOwnOemAssetTemplate?: Maybe<AssetTemplate>;
  createOwnOemAssetType?: Maybe<Oem>;
  createOwnOemCustomField?: Maybe<CustomAdditionalField>;
  createOwnOemInventoryPart?: Maybe<InventoryPart>;
  createOwnOemReport?: Maybe<Report>;
  createOwnOemResource?: Maybe<Resource>;
  createOwnOemSkills?: Maybe<Oem>;
  createOwnOemTicket?: Maybe<Ticket>;
  createOwnOemTimeTrackerTag?: Maybe<TimeTracker>;
  createOwnTicket?: Maybe<Ticket>;
  createPreventiveMaintenanceEvent?: Maybe<PreventiveMaintenance>;
  createRequest?: Maybe<Ticket>;
  createTeam?: Maybe<Team>;
  customerPortalSignup?: Maybe<Scalars["String"]["output"]>;
  declineConnectionRequest?: Maybe<Scalars["String"]["output"]>;
  declineInvite?: Maybe<Scalars["String"]["output"]>;
  delete3DGuide?: Maybe<Scalars["ID"]["output"]>;
  deleteAiAssistantChat?: Maybe<Scalars["ID"]["output"]>;
  deleteAiNote?: Maybe<Scalars["String"]["output"]>;
  deleteContact?: Maybe<Scalars["ID"]["output"]>;
  deleteCustomerPortal?: Maybe<Scalars["String"]["output"]>;
  deleteDocuments?: Maybe<Scalars["String"]["output"]>;
  deleteMachineHistoryNote?: Maybe<Scalars["String"]["output"]>;
  deleteOemCustomer?: Maybe<Scalars["String"]["output"]>;
  deleteOemFacilityUser?: Maybe<Scalars["String"]["output"]>;
  deleteOemSupportAccount?: Maybe<Scalars["String"]["output"]>;
  deleteOemTicketType?: Maybe<Scalars["String"]["output"]>;
  deleteOwnOemAsset?: Maybe<Scalars["String"]["output"]>;
  deleteOwnOemAsset3DModel?: Maybe<Asset>;
  deleteOwnOemAssetImage?: Maybe<Asset>;
  deleteOwnOemAssetTemplate?: Maybe<AssetTemplate>;
  deleteOwnOemAssetTemplate3DModel?: Maybe<AssetTemplate>;
  deleteOwnOemAssetType?: Maybe<Scalars["String"]["output"]>;
  deleteOwnOemCustomAdditionalField?: Maybe<Scalars["String"]["output"]>;
  deleteOwnOemInventoryPart: InventoryPart;
  deleteOwnOemProcedureTemplate?: Maybe<ProcedureTemplate>;
  deleteOwnOemReport?: Maybe<Scalars["String"]["output"]>;
  deleteOwnOemResource?: Maybe<Scalars["String"]["output"]>;
  deleteOwnOemSkill?: Maybe<Scalars["String"]["output"]>;
  deleteOwnOemTicket?: Maybe<Scalars["String"]["output"]>;
  deleteOwnOemTimeTrackerTag?: Maybe<TimeTracker>;
  deletePreventiveMaintenanceEvent?: Maybe<DeletedEvent>;
  deleteTeam?: Maybe<Scalars["String"]["output"]>;
  detachOwnOemAssetDocumentation?: Maybe<Asset>;
  detachOwnOemProcedureFromWorkOrder?: Maybe<Procedure>;
  detachProcedureFromTemplate?: Maybe<AssetTemplate>;
  detectLanguage?: Maybe<Scalars["String"]["output"]>;
  duplicateOwnOemProcedureTemplate?: Maybe<Scalars["String"]["output"]>;
  duplicateOwnOemReport?: Maybe<Report>;
  finalizeOwnOemProcedure?: Maybe<Scalars["String"]["output"]>;
  forgotPassword?: Maybe<Scalars["String"]["output"]>;
  generateApiKey?: Maybe<ApiKey>;
  getGeneralSignUpToken?: Maybe<Scalars["String"]["output"]>;
  handleImportUploadId?: Maybe<Scalars["Boolean"]["output"]>;
  handleOwnOemAssetQRAccess?: Maybe<Asset>;
  indexDocuments?: Maybe<Scalars["String"]["output"]>;
  integrationAddOrUpdateOwnOemCustomersAndMachinesWithAddress?: Maybe<Scalars["Boolean"]["output"]>;
  integrationAddOwnOemCustomersAndMachinesWithAddress?: Maybe<Scalars["Boolean"]["output"]>;
  inviteContacts?: Maybe<Scalars["String"]["output"]>;
  linkCalendarSyncAccount?: Maybe<User>;
  linkEmailAccount?: Maybe<Oem>;
  linkTickets?: Maybe<Ticket>;
  login?: Maybe<Scalars["String"]["output"]>;
  loginCmsDashboard?: Maybe<Scalars["String"]["output"]>;
  loginCustomerPortal?: Maybe<CustomerPortalLoginResponse>;
  loginFacilityApp?: Maybe<Scalars["String"]["output"]>;
  loginMobileFacility?: Maybe<Scalars["String"]["output"]>;
  loginMobileOem?: Maybe<Scalars["String"]["output"]>;
  loginOemDashboard?: Maybe<Scalars["JSON"]["output"]>;
  logout?: Maybe<Scalars["String"]["output"]>;
  machineQrcFacilityView?: Maybe<Scalars["String"]["output"]>;
  moveEmailToFolder?: Maybe<Scalars["String"]["output"]>;
  publishAiNote?: Maybe<AiNote>;
  publishTicketScheduleDraft?: Maybe<TicketScheduleDraft>;
  raiseOemSupportQuery?: Maybe<Scalars["String"]["output"]>;
  refreshTokens?: Maybe<Scalars["JSON"]["output"]>;
  removeAiAssistant?: Maybe<Scalars["ID"]["output"]>;
  removeAiAssistantDocuments?: Maybe<Scalars["ID"]["output"]>;
  removeCustomerInvite?: Maybe<Scalars["ID"]["output"]>;
  removeFirstSignInRedirectUrl?: Maybe<User>;
  removeOwnOemAssetFromOwnOemCustomer?: Maybe<Asset>;
  removeOwnOemInventoryPartFromAsset?: Maybe<Asset>;
  removeOwnOemInventoryPartFromAssetTemplate?: Maybe<AssetTemplate>;
  removeOwnOemInventoryPartFromTicket?: Maybe<Ticket>;
  removeTicketAssignee?: Maybe<Ticket>;
  removeTicketAttachment?: Maybe<Ticket>;
  removeTicketFollower?: Maybe<Ticket>;
  removeTicketResources?: Maybe<Ticket>;
  removeTicketType?: Maybe<Team>;
  removeTimeTrackerLogFromTicket?: Maybe<Ticket>;
  removeUserBoard?: Maybe<User>;
  renameAiAssistant?: Maybe<Scalars["ID"]["output"]>;
  renameAiAssistantChat?: Maybe<Scalars["ID"]["output"]>;
  resendCustomerInvite?: Maybe<Scalars["ID"]["output"]>;
  resetOemAPI?: Maybe<Scalars["String"]["output"]>;
  resetOwnOemAssetToTemplate?: Maybe<Asset>;
  saveAppConfig?: Maybe<AppConfig>;
  saveOwnOemProcedure?: Maybe<Procedure>;
  saveOwnOemProcedureTemplate?: Maybe<ProcedureTemplate>;
  scanDocuments?: Maybe<Scalars["String"]["output"]>;
  sendEmail?: Maybe<Scalars["String"]["output"]>;
  submitIdeaSuggestion?: Maybe<Scalars["String"]["output"]>;
  transcribeAiNote?: Maybe<TranscriptionResult>;
  translateAiNoteSummary?: Maybe<Scalars["String"]["output"]>;
  unassignAssetFromParent?: Maybe<Asset>;
  unassignAssetFromTeam?: Maybe<Asset>;
  unassignCustomerFromTeam?: Maybe<Customer>;
  unassignUserFromTeam?: Maybe<User>;
  unlinkCalendarSyncAccount?: Maybe<User>;
  unlinkEmailAccount?: Maybe<Oem>;
  unlinkTickets?: Maybe<Ticket>;
  update3DGuide?: Maybe<Guide>;
  updateAiAssistant?: Maybe<Scalars["ID"]["output"]>;
  updateAiNoteSummary?: Maybe<AiNote>;
  updateAiNoteTitle?: Maybe<AiNote>;
  updateContact?: Maybe<Contact>;
  updateCrmOem?: Maybe<OemAdmin>;
  updateCustomerPortal?: Maybe<CustomerPortal>;
  updateDescriptionTicket?: Maybe<Ticket>;
  updateDocument?: Maybe<Document>;
  updateEmailSignature?: Maybe<User>;
  updateEmailThread?: Maybe<EmailThreadWithTicket>;
  updateMachineHistoryNote?: Maybe<MachineHistory>;
  updateOemAPI?: Maybe<OemApi>;
  updateOemAiAssistantConfiguration?: Maybe<AiAssistantConfiguration>;
  updateOemCalendarSyncConfiguration?: Maybe<CalendarSyncConfiguration>;
  updateOemCustomer?: Maybe<Customer>;
  updateOemEmailConfiguration?: Maybe<Oem>;
  updateOemFacilityUser?: Maybe<User>;
  updateOemPlans?: Maybe<Oem>;
  updateOemSupportAccount?: Maybe<User>;
  updateOemTicketType?: Maybe<TicketType>;
  updateOwnOem?: Maybe<Oem>;
  updateOwnOemAsset?: Maybe<Asset>;
  updateOwnOemAssetTemplate?: Maybe<AssetTemplate>;
  updateOwnOemAssetType?: Maybe<Scalars["String"]["output"]>;
  updateOwnOemCustomField?: Maybe<CustomAdditionalField>;
  updateOwnOemCustomFieldsOrder?: Maybe<Scalars["String"]["output"]>;
  updateOwnOemInventoryPart?: Maybe<InventoryPart>;
  updateOwnOemReport?: Maybe<Report>;
  updateOwnOemResource?: Maybe<Resource>;
  updateOwnOemSkill?: Maybe<Scalars["String"]["output"]>;
  updateOwnOemTicket?: Maybe<Ticket>;
  updateOwnOemTimeTrackerTag?: Maybe<TimeTracker>;
  updatePersonalSettings?: Maybe<User>;
  updatePreventiveMaintenanceEvent?: Maybe<PreventiveMaintenance>;
  updateSelfFacilityUser?: Maybe<User>;
  updateTeam?: Maybe<Team>;
  updateTicketScheduleDraft?: Maybe<TicketScheduleDraft>;
  updateTimeTrackerLog?: Maybe<Ticket>;
  updateTitleTicket?: Maybe<Ticket>;
  uploadSearchFeedback?: Maybe<Scalars["String"]["output"]>;
  verifyGeneralSignUpToken?: Maybe<VerifyGeneralSignUpToken>;
  verifySignUpToken?: Maybe<SignupTokenVerification>;
};

export type Mutation_CompleteMultiPartUploadS3Args = {
  MultipartUpload?: InputMaybe<CompletedMultipartUpload>;
  UploadId: Scalars["String"]["input"];
  fileKey: Scalars["String"]["input"];
};

export type Mutation_CreateMultiPartUploadS3Args = {
  fileSize: Scalars["Int"]["input"];
  filename: Scalars["String"]["input"];
  filetype: Scalars["String"]["input"];
  forceCustomPath?: InputMaybe<Scalars["Boolean"]["input"]>;
  forceUniqueFilename?: InputMaybe<Scalars["Boolean"]["input"]>;
  partSize: Scalars["Int"]["input"];
  type?: InputMaybe<Scalars["String"]["input"]>;
};

export type Mutation_SafeSignS3Args = {
  filename: Scalars["String"]["input"];
  filetype: Scalars["String"]["input"];
  forceCustomPath?: InputMaybe<Scalars["Boolean"]["input"]>;
  forceUniqueFilename?: InputMaybe<Scalars["Boolean"]["input"]>;
  metadata?: InputMaybe<Scalars["JSON"]["input"]>;
  type?: InputMaybe<Scalars["String"]["input"]>;
};

export type Mutation_SignS3DownloadArgs = {
  filename: Scalars["String"]["input"];
};

export type Mutation_SignS3MultiDownloadArgs = {
  keys: Array<Scalars["String"]["input"]>;
};

export type MutationAcceptConnectionRequestArgs = {
  portalToAttach: Scalars["ID"]["input"];
  requestId: Scalars["ID"]["input"];
};

export type MutationActiveInactiveOwnOemAdditionalFieldArgs = {
  _id: Scalars["ID"]["input"];
  enabled: Scalars["Boolean"]["input"];
};

export type MutationAddAiAssistantDocumentsArgs = {
  input?: InputMaybe<InputAddAiAssistantDocuments>;
};

export type MutationAddOwnOemAsset3DModelArgs = {
  _3dModelUrl: Scalars["String"]["input"];
  _id: Scalars["ID"]["input"];
};

export type MutationAddOwnOemAssetTemplate3DModelArgs = {
  _3dModelUrl: Scalars["String"]["input"];
  _id: Scalars["ID"]["input"];
};

export type MutationAddTicketAssigneeArgs = {
  input: InputAddTicketAssignee;
};

export type MutationAddTicketAttachmentsArgs = {
  input: InputAddTicketAttachments;
};

export type MutationAddTicketFollowerArgs = {
  input: InputAddTicketFollower;
};

export type MutationAddTicketResourceArgs = {
  input: InputAddTicketResource;
};

export type MutationAddTimeTrackerLogArgs = {
  input: InputAddTimeTrackerLog;
};

export type MutationAddUserBoardArgs = {
  board: InputUserBoard;
};

export type MutationApproveInviteArgs = {
  token: Scalars["String"]["input"];
};

export type MutationApproveOcrDocumentsArgs = {
  documentsToApprove?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>;
};

export type MutationAssignAssetsToParentArgs = {
  input: InputAssignAssetsToParent;
};

export type MutationAssignMultipleAssetsToTeamArgs = {
  input: InputAssignMultipleAssetsToTeam;
};

export type MutationAssignMultipleCustomersToTeamArgs = {
  input: InputAssignMultipleCustomersToTeam;
};

export type MutationAssignMultipleSkillsToOemUserArgs = {
  input: InputAssignUnassignMultipleSkillsOemUser;
};

export type MutationAssignMultipleUsersToTeamArgs = {
  input?: InputMaybe<InputAssignMultipleUsersToTeam>;
};

export type MutationAssignOwnOemInventoryPartsToAssetArgs = {
  input?: InputMaybe<InputAssignAssetInventoryParts>;
};

export type MutationAssignOwnOemInventoryPartsToAssetTemplateArgs = {
  input?: InputMaybe<InputAssignAssetTemplateInventoryParts>;
};

export type MutationAssignOwnOemInventoryPartsToTicketArgs = {
  input: InputAssignInventoryPartsToTicket;
};

export type MutationAssignOwnOemMultipleAssetsToOwnOemCustomerArgs = {
  input: InputAssignOwnOemMultipleAssetsToOwnOemCustomer;
};

export type MutationAssignTicketTypeArgs = {
  input: AssignTicketTypeInput;
};

export type MutationAttachOwnOemProcedureToWorkOrderArgs = {
  input: InputAttachProcedureToWorkOrder;
};

export type MutationAttachProceduresToTemplateArgs = {
  procedures?: InputMaybe<Array<InputMaybe<Scalars["ID"]["input"]>>>;
  templateId?: InputMaybe<Scalars["ID"]["input"]>;
};

export type MutationCheckValidFacilityUserArgs = {
  userId: Scalars["ID"]["input"];
};

export type MutationConfigureOemTicketTypeArgs = {
  id: Scalars["ID"]["input"];
  input: InputTicketTypeConfig;
};

export type MutationCreate3DGuideArgs = {
  input?: InputMaybe<InputCreate3DGuide>;
};

export type MutationCreateAiAssistantArgs = {
  machineID?: InputMaybe<Scalars["ID"]["input"]>;
};

export type MutationCreateAiAssistantChatArgs = {
  aiAssistantId: Scalars["ID"]["input"];
};

export type MutationCreateAiAssistantV2Args = {
  input?: InputMaybe<InputCreateAiAssistantV2>;
};

export type MutationCreateAiNoteArgs = {
  input: InputCreateAiNote;
};

export type MutationCreateConnectionRequestArgs = {
  oemId: Scalars["ID"]["input"];
};

export type MutationCreateContactArgs = {
  input: InputCreateContact;
};

export type MutationCreateCustomerPortalArgs = {
  connectionId: Scalars["ID"]["input"];
};

export type MutationCreateDocumentsArgs = {
  input?: InputMaybe<InputCreateDocuments>;
};

export type MutationCreateMachineHistoryNoteArgs = {
  input?: InputMaybe<InputCreateMachineHistoryNote>;
};

export type MutationCreateOemArgs = {
  input: InputCreateOem;
};

export type MutationCreateOemApiArgs = {
  input?: InputMaybe<InputConfigureOemApi>;
};

export type MutationCreateOemCustomerArgs = {
  input: InputCreateOemCustomer;
};

export type MutationCreateOemCustomerV2Args = {
  input: InputCreateOemCustomerV2;
};

export type MutationCreateOemFacilityUserArgs = {
  input: InputCreateFacilityUser;
};

export type MutationCreateOemSupportAccountArgs = {
  input: InputCreateOemSupportAccount;
};

export type MutationCreateOemTicketTypeArgs = {
  input: InputTicketType;
};

export type MutationCreateOwnOemAssetArgs = {
  input?: InputMaybe<InputCreateOwnOemAsset>;
};

export type MutationCreateOwnOemAssetTemplateArgs = {
  input: InputCreateAssetTemplate;
};

export type MutationCreateOwnOemAssetTypeArgs = {
  input: InputCreateOwnOemAssetType;
};

export type MutationCreateOwnOemCustomFieldArgs = {
  input?: InputMaybe<InputInsertCustomAdditionalField>;
};

export type MutationCreateOwnOemInventoryPartArgs = {
  input: InputCreateInventoryPart;
};

export type MutationCreateOwnOemReportArgs = {
  input?: InputMaybe<InputCreateOwnOemReport>;
};

export type MutationCreateOwnOemResourceArgs = {
  input: InputCreateOwnOemResource;
};

export type MutationCreateOwnOemSkillsArgs = {
  input: InputCreateOwnOemSkills;
};

export type MutationCreateOwnOemTicketArgs = {
  input: InputCreateOwnOemTicket;
};

export type MutationCreateOwnOemTimeTrackerTagArgs = {
  input: InputInsertTag;
};

export type MutationCreateOwnTicketArgs = {
  input: InputCreateOwnTicket;
};

export type MutationCreatePreventiveMaintenanceEventArgs = {
  input: InputCreatePreventiveMaintenanceEvent;
};

export type MutationCreateRequestArgs = {
  input: InputCreateRequest;
};

export type MutationCreateTeamArgs = {
  input?: InputMaybe<InputCreateTeam>;
};

export type MutationCustomerPortalSignupArgs = {
  input?: InputMaybe<InputCustomerPortalSignup>;
};

export type MutationDeclineConnectionRequestArgs = {
  requestId: Scalars["ID"]["input"];
};

export type MutationDeclineInviteArgs = {
  token: Scalars["String"]["input"];
};

export type MutationDelete3DGuideArgs = {
  id?: InputMaybe<Scalars["ID"]["input"]>;
};

export type MutationDeleteAiAssistantChatArgs = {
  aiAssistantChatId: Scalars["ID"]["input"];
};

export type MutationDeleteAiNoteArgs = {
  id: Scalars["ID"]["input"];
};

export type MutationDeleteContactArgs = {
  id?: InputMaybe<Scalars["ID"]["input"]>;
};

export type MutationDeleteCustomerPortalArgs = {
  id: Scalars["ID"]["input"];
};

export type MutationDeleteDocumentsArgs = {
  documentsToDelete?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>;
};

export type MutationDeleteMachineHistoryNoteArgs = {
  id: Scalars["ID"]["input"];
  machine: Scalars["ID"]["input"];
};

export type MutationDeleteOemCustomerArgs = {
  customerId: Scalars["ID"]["input"];
};

export type MutationDeleteOemFacilityUserArgs = {
  id: Scalars["ID"]["input"];
};

export type MutationDeleteOemSupportAccountArgs = {
  id: Scalars["ID"]["input"];
};

export type MutationDeleteOemTicketTypeArgs = {
  id: Scalars["ID"]["input"];
  input?: InputMaybe<InputDeleteOemTicketType>;
};

export type MutationDeleteOwnOemAssetArgs = {
  assetId: Scalars["ID"]["input"];
};

export type MutationDeleteOwnOemAsset3DModelArgs = {
  assetId: Scalars["ID"]["input"];
};

export type MutationDeleteOwnOemAssetImageArgs = {
  assetId: Scalars["ID"]["input"];
};

export type MutationDeleteOwnOemAssetTemplateArgs = {
  templateId: Scalars["ID"]["input"];
};

export type MutationDeleteOwnOemAssetTemplate3DModelArgs = {
  templateId: Scalars["ID"]["input"];
};

export type MutationDeleteOwnOemAssetTypeArgs = {
  input: InputDeleteOwnOemAssetType;
};

export type MutationDeleteOwnOemCustomAdditionalFieldArgs = {
  input?: InputMaybe<InputDeleteOwnOemCustomAdditionalField>;
};

export type MutationDeleteOwnOemInventoryPartArgs = {
  id: Scalars["ID"]["input"];
};

export type MutationDeleteOwnOemProcedureTemplateArgs = {
  id: Scalars["ID"]["input"];
};

export type MutationDeleteOwnOemReportArgs = {
  id: Scalars["ID"]["input"];
};

export type MutationDeleteOwnOemResourceArgs = {
  input: InputDeleteOwnOemResource;
};

export type MutationDeleteOwnOemSkillArgs = {
  input: InputDeleteOwnOemSkill;
};

export type MutationDeleteOwnOemTicketArgs = {
  input: InputDeleteOwnOemTicket;
};

export type MutationDeleteOwnOemTimeTrackerTagArgs = {
  id: Scalars["ID"]["input"];
};

export type MutationDeletePreventiveMaintenanceEventArgs = {
  id: Scalars["ID"]["input"];
};

export type MutationDeleteTeamArgs = {
  id: Scalars["ID"]["input"];
};

export type MutationDetachOwnOemAssetDocumentationArgs = {
  _id: Scalars["ID"]["input"];
};

export type MutationDetachOwnOemProcedureFromWorkOrderArgs = {
  input: InputDetachProcedureFromWorkOrder;
};

export type MutationDetachProcedureFromTemplateArgs = {
  procedureId?: InputMaybe<Scalars["ID"]["input"]>;
  templateId: Scalars["ID"]["input"];
};

export type MutationDetectLanguageArgs = {
  audioUrl: Scalars["String"]["input"];
};

export type MutationDuplicateOwnOemProcedureTemplateArgs = {
  id: Scalars["ID"]["input"];
};

export type MutationDuplicateOwnOemReportArgs = {
  input?: InputMaybe<InputDuplicateOwnOemReport>;
};

export type MutationFinalizeOwnOemProcedureArgs = {
  input: InputFinalizeProcedure;
};

export type MutationForgotPasswordArgs = {
  email: Scalars["String"]["input"];
};

export type MutationGetGeneralSignUpTokenArgs = {
  input: InputGetGeneralSignUpToken;
};

export type MutationHandleImportUploadIdArgs = {
  input: InputHandleImportUploadId;
};

export type MutationHandleOwnOemAssetQrAccessArgs = {
  input?: InputMaybe<InputHandleOwnOemAssetQrAccess>;
};

export type MutationIndexDocumentsArgs = {
  documentsToIndex?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>;
};

export type MutationIntegrationAddOrUpdateOwnOemCustomersAndMachinesWithAddressArgs = {
  customer: IntegrationCustomersEnum;
  input?: InputMaybe<Scalars["MJSON"]["input"]>;
};

export type MutationIntegrationAddOwnOemCustomersAndMachinesWithAddressArgs = {
  customer: IntegrationCustomersEnum;
  input?: InputMaybe<Scalars["MJSON"]["input"]>;
};

export type MutationInviteContactsArgs = {
  input: InputInviteContacts;
};

export type MutationLinkCalendarSyncAccountArgs = {
  code: Scalars["String"]["input"];
};

export type MutationLinkEmailAccountArgs = {
  code: Scalars["String"]["input"];
};

export type MutationLinkTicketsArgs = {
  linkedTicketId: Scalars["ID"]["input"];
  ticketId: Scalars["ID"]["input"];
};

export type MutationLoginArgs = {
  input?: InputMaybe<UserCredentials>;
};

export type MutationLoginCmsDashboardArgs = {
  input?: InputMaybe<UserCredentials>;
};

export type MutationLoginCustomerPortalArgs = {
  input?: InputMaybe<UserOemCredentials>;
};

export type MutationLoginFacilityAppArgs = {
  input?: InputMaybe<UserFacilityCredentials>;
};

export type MutationLoginMobileFacilityArgs = {
  input?: InputMaybe<UserCredentials>;
};

export type MutationLoginMobileOemArgs = {
  input?: InputMaybe<UserCredentials>;
};

export type MutationLoginOemDashboardArgs = {
  input?: InputMaybe<UserOemCredentials>;
};

export type MutationMachineQrcFacilityViewArgs = {
  input?: InputMaybe<QrcMachineCredentials>;
};

export type MutationMoveEmailToFolderArgs = {
  input?: InputMaybe<InputMoveEmailToFolder>;
};

export type MutationPublishAiNoteArgs = {
  input: InputPublishAiNote;
};

export type MutationPublishTicketScheduleDraftArgs = {
  input: InputPublishTicketScheduleDraft;
};

export type MutationRaiseOemSupportQueryArgs = {
  companySize?: InputMaybe<Scalars["String"]["input"]>;
  contact?: InputMaybe<Scalars["String"]["input"]>;
  isDelete?: InputMaybe<Scalars["Boolean"]["input"]>;
  productType?: InputMaybe<Scalars["String"]["input"]>;
  query?: InputMaybe<Scalars["String"]["input"]>;
};

export type MutationRefreshTokensArgs = {
  refreshToken: Scalars["String"]["input"];
};

export type MutationRemoveAiAssistantArgs = {
  aiAssistantID?: InputMaybe<Scalars["ID"]["input"]>;
};

export type MutationRemoveAiAssistantDocumentsArgs = {
  input?: InputMaybe<InputRemoveAiAssistantDocuments>;
};

export type MutationRemoveCustomerInviteArgs = {
  id?: InputMaybe<Scalars["ID"]["input"]>;
};

export type MutationRemoveOwnOemAssetFromOwnOemCustomerArgs = {
  input: InputRemoveOwnOemAssetFromOwnOemCustomer;
};

export type MutationRemoveOwnOemInventoryPartFromAssetArgs = {
  input?: InputMaybe<InputRemoveAssetInventoryPart>;
};

export type MutationRemoveOwnOemInventoryPartFromAssetTemplateArgs = {
  input?: InputMaybe<InputRemoveAssetTemplateInventoryPart>;
};

export type MutationRemoveOwnOemInventoryPartFromTicketArgs = {
  input: InputRemoveInventoryPartFromTicket;
};

export type MutationRemoveTicketAssigneeArgs = {
  input: InputRemoveTicketAssignee;
};

export type MutationRemoveTicketAttachmentArgs = {
  input: InputRemoveTicketAttachment;
};

export type MutationRemoveTicketFollowerArgs = {
  input: InputRemoveTicketFollower;
};

export type MutationRemoveTicketResourcesArgs = {
  input: InputRemoveTicketResources;
};

export type MutationRemoveTicketTypeArgs = {
  input?: InputMaybe<RemoveTicketTypeInput>;
};

export type MutationRemoveTimeTrackerLogFromTicketArgs = {
  input: InputRemoveTimeTrackerLog;
};

export type MutationRemoveUserBoardArgs = {
  board: InputUserBoard;
};

export type MutationRenameAiAssistantArgs = {
  input?: InputMaybe<InputRenameAiAssistant>;
};

export type MutationRenameAiAssistantChatArgs = {
  input: InputRenameAiAssistantChat;
};

export type MutationResendCustomerInviteArgs = {
  id?: InputMaybe<Scalars["ID"]["input"]>;
};

export type MutationResetOemApiArgs = {
  oemId: Scalars["ID"]["input"];
};

export type MutationResetOwnOemAssetToTemplateArgs = {
  input?: InputMaybe<InputResetAssetToTemplate>;
};

export type MutationSaveAppConfigArgs = {
  input?: InputMaybe<InputAppConfig>;
};

export type MutationSaveOwnOemProcedureArgs = {
  input: InputSaveProcedure;
};

export type MutationSaveOwnOemProcedureTemplateArgs = {
  input: InputSaveProcedureTemplate;
};

export type MutationScanDocumentsArgs = {
  documentsToScan?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>;
};

export type MutationSendEmailArgs = {
  input?: InputMaybe<InputSendEmail>;
};

export type MutationSubmitIdeaSuggestionArgs = {
  suggestion?: InputMaybe<Scalars["String"]["input"]>;
};

export type MutationTranscribeAiNoteArgs = {
  input: InputTranscribeAiNote;
};

export type MutationTranslateAiNoteSummaryArgs = {
  input: InputTranslateAiNoteSummary;
};

export type MutationUnassignAssetFromParentArgs = {
  input?: InputMaybe<InputUnassignAssetFromParent>;
};

export type MutationUnassignAssetFromTeamArgs = {
  input?: InputMaybe<InputUnassignAssetFromTeam>;
};

export type MutationUnassignCustomerFromTeamArgs = {
  input?: InputMaybe<InputUnassignCustomerFromTeam>;
};

export type MutationUnassignUserFromTeamArgs = {
  input?: InputMaybe<InputUnassignUserFromTeam>;
};

export type MutationUnlinkEmailAccountArgs = {
  emailAddressId: Scalars["ID"]["input"];
};

export type MutationUnlinkTicketsArgs = {
  linkedTicketId: Scalars["ID"]["input"];
  ticketId: Scalars["ID"]["input"];
};

export type MutationUpdate3DGuideArgs = {
  input?: InputMaybe<InputUpdate3DGuide>;
};

export type MutationUpdateAiAssistantArgs = {
  input?: InputMaybe<InputUpdateAiAssistant>;
};

export type MutationUpdateAiNoteSummaryArgs = {
  input: InputUpdateAiNoteSummary;
};

export type MutationUpdateAiNoteTitleArgs = {
  input: InputUpdateAiNoteTitle;
};

export type MutationUpdateContactArgs = {
  input: InputUpdateContact;
};

export type MutationUpdateCrmOemArgs = {
  input: InputUpdateCrmOem;
};

export type MutationUpdateCustomerPortalArgs = {
  input: InputUpdateCustomerPortal;
};

export type MutationUpdateDescriptionTicketArgs = {
  input: InputUpdateDescriptionTicket;
};

export type MutationUpdateDocumentArgs = {
  input?: InputMaybe<InputUpdateDocument>;
};

export type MutationUpdateEmailSignatureArgs = {
  autoAppendEmailSignature: Scalars["Boolean"]["input"];
  signature: Scalars["String"]["input"];
};

export type MutationUpdateEmailThreadArgs = {
  input?: InputMaybe<InputUpdateEmailThread>;
};

export type MutationUpdateMachineHistoryNoteArgs = {
  input?: InputMaybe<InputUpdateMachineHistoryNote>;
};

export type MutationUpdateOemApiArgs = {
  input?: InputMaybe<InputConfigureOemApi>;
};

export type MutationUpdateOemAiAssistantConfigurationArgs = {
  input: UpdateOemAiAssistantConfiguration;
};

export type MutationUpdateOemCalendarSyncConfigurationArgs = {
  input: UpdateOemCalendarSyncConfiguration;
};

export type MutationUpdateOemCustomerArgs = {
  input: InputUpdateOemCustomer;
};

export type MutationUpdateOemEmailConfigurationArgs = {
  input?: InputMaybe<InputConfigureOemEmail>;
};

export type MutationUpdateOemFacilityUserArgs = {
  input: InputUpdateFacilityUser;
};

export type MutationUpdateOemPlansArgs = {
  input?: InputMaybe<InputConfigureOemPlans>;
};

export type MutationUpdateOemSupportAccountArgs = {
  input: InputUpdateOemSupportAccount;
};

export type MutationUpdateOemTicketTypeArgs = {
  id: Scalars["ID"]["input"];
  input: InputUpdateOemTicketType;
};

export type MutationUpdateOwnOemArgs = {
  input: InputUpdateOwnOem;
};

export type MutationUpdateOwnOemAssetArgs = {
  input?: InputMaybe<InputUpdateOwnOemAsset>;
};

export type MutationUpdateOwnOemAssetTemplateArgs = {
  input: InputUpdateAssetTemplate;
};

export type MutationUpdateOwnOemAssetTypeArgs = {
  input: InputUpdateOwnOemAssetType;
};

export type MutationUpdateOwnOemCustomFieldArgs = {
  input?: InputMaybe<InputUpdateCustomAdditionalField>;
};

export type MutationUpdateOwnOemCustomFieldsOrderArgs = {
  input?: InputMaybe<InputUpdateCustomFieldsOrder>;
};

export type MutationUpdateOwnOemInventoryPartArgs = {
  input: InputUpdateInventoryPart;
};

export type MutationUpdateOwnOemReportArgs = {
  input?: InputMaybe<InputUpdateOwnOemReport>;
};

export type MutationUpdateOwnOemResourceArgs = {
  input: InputUpdateOwnOemResource;
};

export type MutationUpdateOwnOemSkillArgs = {
  input: InputUpdateOwnOemSkill;
};

export type MutationUpdateOwnOemTicketArgs = {
  input: InputUpdateOwnOemTicket;
};

export type MutationUpdateOwnOemTimeTrackerTagArgs = {
  input: InputUpdateTag;
};

export type MutationUpdatePersonalSettingsArgs = {
  emailNotificationSettings?: InputMaybe<InputEmailNotificationSettings>;
  personalSettings?: InputMaybe<InputUpdatePersonalSettings>;
};

export type MutationUpdatePreventiveMaintenanceEventArgs = {
  input: InputUpdatePreventiveMaintenanceEvent;
};

export type MutationUpdateSelfFacilityUserArgs = {
  input: InputUpdateSelfFacilityUser;
};

export type MutationUpdateTeamArgs = {
  input?: InputMaybe<InputUpdateTeam>;
};

export type MutationUpdateTicketScheduleDraftArgs = {
  input: InputUpdateTicketScheduleDraft;
};

export type MutationUpdateTimeTrackerLogArgs = {
  input: InputUpdateTimeTrackerLog;
};

export type MutationUpdateTitleTicketArgs = {
  input: InputUpdateTitleTicket;
};

export type MutationUploadSearchFeedbackArgs = {
  input?: InputMaybe<InputUploadSearchFeedback>;
};

export type MutationVerifyGeneralSignUpTokenArgs = {
  token: Scalars["String"]["input"];
};

export type MutationVerifySignUpTokenArgs = {
  token: Scalars["String"]["input"];
};

export enum NodeAndFieldTypes {
  AmountField = "AMOUNT_FIELD",
  AssetsField = "ASSETS_FIELD",
  CheckboxField = "CHECKBOX_FIELD",
  ChecklistField = "CHECKLIST_FIELD",
  DateField = "DATE_FIELD",
  DocumentUploaderField = "DOCUMENT_UPLOADER_FIELD",
  DropdownField = "DROPDOWN_FIELD",
  Heading = "HEADING",
  ImageUploaderField = "IMAGE_UPLOADER_FIELD",
  MemberField = "MEMBER_FIELD",
  NumberField = "NUMBER_FIELD",
  PartsField = "PARTS_FIELD",
  Section = "SECTION",
  SignatureField = "SIGNATURE_FIELD",
  SingleSelectField = "SINGLE_SELECT_FIELD",
  TableField = "TABLE_FIELD",
  TextAreaField = "TEXT_AREA_FIELD",
}

export type Notification = {
  __typename?: "Notification";
  email?: Maybe<EmailNotification>;
  status?: Maybe<Scalars["String"]["output"]>;
};

export type NotificationUser = {
  __typename?: "NotificationUser";
  user?: Maybe<User>;
  workOrderTypes?: Maybe<Array<Maybe<Scalars["String"]["output"]>>>;
};

export type Oem = {
  __typename?: "Oem";
  _id: Scalars["ID"]["output"];
  aiAssistantConfiguration?: Maybe<AiAssistantConfiguration>;
  aiAssistantUsage?: Maybe<AiAssistantUsage>;
  allowAssigneesAcrossTeams?: Maybe<Scalars["Boolean"]["output"]>;
  allowFollowersMyWorkOrders?: Maybe<Scalars["Boolean"]["output"]>;
  allowHourlyPMEOption?: Maybe<Scalars["Boolean"]["output"]>;
  allowTechnicianToAssignWorkOrders?: Maybe<Scalars["Boolean"]["output"]>;
  api?: Maybe<OemApi>;
  assetTypes?: Maybe<Array<Maybe<AssetType>>>;
  assetsCount?: Maybe<Scalars["Int"]["output"]>;
  backgroundColor?: Maybe<Scalars["String"]["output"]>;
  brandLogo?: Maybe<Scalars["String"]["output"]>;
  calendarSyncConfiguration?: Maybe<CalendarSyncConfiguration>;
  calendarSyncUsage?: Maybe<CalendarSyncUsage>;
  channelsAddedInGroup?: Maybe<Scalars["Boolean"]["output"]>;
  coverPhoto?: Maybe<Scalars["String"]["output"]>;
  deepOfflineFunctionalityEnabled?: Maybe<Scalars["Boolean"]["output"]>;
  description?: Maybe<Scalars["String"]["output"]>;
  emailAccountLinked?: Maybe<Scalars["Boolean"]["output"]>;
  emailAccountStatus?: Maybe<Scalars["String"]["output"]>;
  emailAccounts?: Maybe<Array<Maybe<EmailAccount>>>;
  heading?: Maybe<Scalars["String"]["output"]>;
  headline?: Maybe<Scalars["String"]["output"]>;
  importerTemplates?: Maybe<Array<Maybe<TemplateImporter>>>;
  installedProducts?: Maybe<Array<Maybe<InstalledProduct>>>;
  linkedConnection?: Maybe<Scalars["ID"]["output"]>;
  linkedEmailAddress?: Maybe<Scalars["String"]["output"]>;
  location?: Maybe<Scalars["String"]["output"]>;
  logo?: Maybe<Scalars["String"]["output"]>;
  maximumAllowedEmailAddresses?: Maybe<Scalars["Int"]["output"]>;
  name?: Maybe<Scalars["String"]["output"]>;
  notification?: Maybe<Notification>;
  paidFeatures?: Maybe<Array<Maybe<PaidFeaturesEnum>>>;
  paragraph?: Maybe<Scalars["String"]["output"]>;
  signupSource?: Maybe<Scalars["String"]["output"]>;
  skills?: Maybe<Array<Maybe<Skill>>>;
  slug?: Maybe<Scalars["String"]["output"]>;
  statuses?: Maybe<Array<Maybe<Statuses>>>;
  subHeading?: Maybe<Scalars["String"]["output"]>;
  textColor?: Maybe<Scalars["String"]["output"]>;
  thumbnail?: Maybe<Scalars["String"]["output"]>;
  ticketTypes?: Maybe<Array<Maybe<TicketType>>>;
  urlOem?: Maybe<Scalars["String"]["output"]>;
  urlOemFacility?: Maybe<Scalars["String"]["output"]>;
  usageTracking?: Maybe<UsageTracking>;
  website?: Maybe<Scalars["String"]["output"]>;
};

export type OemApi = {
  __typename?: "OemAPI";
  _id?: Maybe<Scalars["ID"]["output"]>;
  consumedQuota?: Maybe<Scalars["Int"]["output"]>;
  key?: Maybe<Scalars["String"]["output"]>;
  quota?: Maybe<Scalars["Int"]["output"]>;
  requestRate?: Maybe<Scalars["Int"]["output"]>;
  token?: Maybe<Scalars["String"]["output"]>;
};

export type OemAdmin = {
  __typename?: "OemAdmin";
  admin?: Maybe<User>;
  oem?: Maybe<Oem>;
};

export type OemPlan = {
  __typename?: "OemPlan";
  tier?: Maybe<Scalars["String"]["output"]>;
  type?: Maybe<Scalars["String"]["output"]>;
  users?: Maybe<Scalars["Int"]["output"]>;
};

export type OptionResponse = {
  __typename?: "OptionResponse";
  _id?: Maybe<Scalars["ID"]["output"]>;
  color?: Maybe<Scalars["String"]["output"]>;
  description?: Maybe<Scalars["String"]["output"]>;
  value?: Maybe<Scalars["String"]["output"]>;
};

export type PmeAssetPart = {
  __typename?: "PMEAssetPart";
  addedBy?: Maybe<User>;
  part?: Maybe<InventoryPart>;
  quantity?: Maybe<Scalars["Int"]["output"]>;
};

export type PmeProcedure = {
  __typename?: "PMEProcedure";
  addedBy?: Maybe<User>;
  procedure?: Maybe<Procedure>;
};

export type PaginatedActivityLogs = {
  __typename?: "PaginatedActivityLogs";
  currentPage?: Maybe<Scalars["Int"]["output"]>;
  limit?: Maybe<Scalars["Int"]["output"]>;
  logs?: Maybe<Array<Maybe<ActivityLog>>>;
  skip?: Maybe<Scalars["Int"]["output"]>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
};

export type PaginatedAiAssistantChats = {
  __typename?: "PaginatedAiAssistantChats";
  aiAssistantChats?: Maybe<Array<Maybe<AiAssistantChat>>>;
  currentPage?: Maybe<Scalars["Int"]["output"]>;
  limit?: Maybe<Scalars["Int"]["output"]>;
  skip?: Maybe<Scalars["Int"]["output"]>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
};

export type PaginatedAiNotes = {
  __typename?: "PaginatedAiNotes";
  aiNotes?: Maybe<Array<Maybe<AiNote>>>;
  currentPage?: Maybe<Scalars["Int"]["output"]>;
  limit?: Maybe<Scalars["Int"]["output"]>;
  skip?: Maybe<Scalars["Int"]["output"]>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
};

export type PaginatedAssets = {
  __typename?: "PaginatedAssets";
  assets?: Maybe<Array<Maybe<Asset>>>;
  currentPage?: Maybe<Scalars["Int"]["output"]>;
  limit?: Maybe<Scalars["Int"]["output"]>;
  skip?: Maybe<Scalars["Int"]["output"]>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
};

export type PaginatedAssistants = {
  __typename?: "PaginatedAssistants";
  assistants?: Maybe<Array<Maybe<AiAssistant>>>;
  currentPage?: Maybe<Scalars["Int"]["output"]>;
  limit?: Maybe<Scalars["Int"]["output"]>;
  skip?: Maybe<Scalars["Int"]["output"]>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
};

export type PaginatedConnectionHistory = {
  __typename?: "PaginatedConnectionHistory";
  currentPage?: Maybe<Scalars["Int"]["output"]>;
  history?: Maybe<Array<Maybe<MachineHistory>>>;
  limit?: Maybe<Scalars["Int"]["output"]>;
  machineCount?: Maybe<Scalars["Int"]["output"]>;
  skip?: Maybe<Scalars["Int"]["output"]>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
};

export type PaginatedContacts = {
  __typename?: "PaginatedContacts";
  contacts?: Maybe<Array<Maybe<Contact>>>;
  currentPage?: Maybe<Scalars["Int"]["output"]>;
  limit?: Maybe<Scalars["Int"]["output"]>;
  skip?: Maybe<Scalars["Int"]["output"]>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
};

export type PaginatedCustomerPortals = {
  __typename?: "PaginatedCustomerPortals";
  currentPage?: Maybe<Scalars["Int"]["output"]>;
  customerPortals?: Maybe<Array<Maybe<CustomerPortal>>>;
  isConnectionAlreadyEstablished?: Maybe<Scalars["Boolean"]["output"]>;
  isExistingContactLinkable?: Maybe<Scalars["Boolean"]["output"]>;
  limit?: Maybe<Scalars["Int"]["output"]>;
  skip?: Maybe<Scalars["Int"]["output"]>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
};

export type PaginatedCustomers = {
  __typename?: "PaginatedCustomers";
  currentPage?: Maybe<Scalars["Int"]["output"]>;
  customers?: Maybe<Array<Maybe<Customer>>>;
  limit?: Maybe<Scalars["Int"]["output"]>;
  skip?: Maybe<Scalars["Int"]["output"]>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
};

export type PaginatedDocuments = {
  __typename?: "PaginatedDocuments";
  currentPage?: Maybe<Scalars["Int"]["output"]>;
  documents?: Maybe<Array<Maybe<Document>>>;
  limit?: Maybe<Scalars["Int"]["output"]>;
  skip?: Maybe<Scalars["Int"]["output"]>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
};

export type PaginatedEmailContacts = {
  __typename?: "PaginatedEmailContacts";
  contacts?: Maybe<Array<Maybe<Scalars["JSON"]["output"]>>>;
  pageToken?: Maybe<Scalars["String"]["output"]>;
};

export type PaginatedEmailDrafts = {
  __typename?: "PaginatedEmailDrafts";
  drafts?: Maybe<Array<Maybe<Scalars["JSON"]["output"]>>>;
  pageToken?: Maybe<Scalars["String"]["output"]>;
};

export type PaginatedEmailFolders = {
  __typename?: "PaginatedEmailFolders";
  currentPage?: Maybe<Scalars["Int"]["output"]>;
  folders?: Maybe<Array<Maybe<EmailFolder>>>;
  limit?: Maybe<Scalars["Int"]["output"]>;
  skip?: Maybe<Scalars["Int"]["output"]>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
};

export type PaginatedEmailThreads = {
  __typename?: "PaginatedEmailThreads";
  currentPage?: Maybe<Scalars["Int"]["output"]>;
  pageToken?: Maybe<Scalars["String"]["output"]>;
  threads?: Maybe<Array<Maybe<EmailThread>>>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
};

export type PaginatedGuide = {
  __typename?: "PaginatedGuide";
  currentPage?: Maybe<Scalars["Int"]["output"]>;
  guides?: Maybe<Array<Maybe<Guide>>>;
  limit?: Maybe<Scalars["Int"]["output"]>;
  skip?: Maybe<Scalars["Int"]["output"]>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
};

export type PaginatedInventoryParts = {
  __typename?: "PaginatedInventoryParts";
  currentPage?: Maybe<Scalars["Int"]["output"]>;
  limit?: Maybe<Scalars["Int"]["output"]>;
  parts?: Maybe<Array<Maybe<InventoryPart>>>;
  skip?: Maybe<Scalars["Int"]["output"]>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
};

export type PaginatedKanbanAssets = {
  __typename?: "PaginatedKanbanAssets";
  columns?: Maybe<Array<Maybe<KanbanColumnAsset>>>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
};

export type PaginatedKanbanCustomers = {
  __typename?: "PaginatedKanbanCustomers";
  columns?: Maybe<Array<Maybe<KanbanColumnCustomer>>>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
};

export type PaginatedKanbanTickets = {
  __typename?: "PaginatedKanbanTickets";
  columns?: Maybe<Array<Maybe<KanbanColumnTicket>>>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
};

export type PaginatedMachineHistory = {
  __typename?: "PaginatedMachineHistory";
  currentPage?: Maybe<Scalars["Int"]["output"]>;
  history?: Maybe<Array<Maybe<MachineHistory>>>;
  limit?: Maybe<Scalars["Int"]["output"]>;
  skip?: Maybe<Scalars["Int"]["output"]>;
  ticketCount?: Maybe<Scalars["Int"]["output"]>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
};

export type PaginatedModels = {
  __typename?: "PaginatedModels";
  currentPage?: Maybe<Scalars["Int"]["output"]>;
  limit?: Maybe<Scalars["Int"]["output"]>;
  models?: Maybe<Array<Maybe<AssetOrTemplate>>>;
  skip?: Maybe<Scalars["Int"]["output"]>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
};

export type PaginatedPreventiveMaintenanceEvents = {
  __typename?: "PaginatedPreventiveMaintenanceEvents";
  events?: Maybe<Array<Maybe<PreventiveMaintenance>>>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
};

export type PaginatedReports = {
  __typename?: "PaginatedReports";
  currentPage?: Maybe<Scalars["Int"]["output"]>;
  limit?: Maybe<Scalars["Int"]["output"]>;
  reports?: Maybe<Array<Maybe<Report>>>;
  skip?: Maybe<Scalars["Int"]["output"]>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
};

export type PaginatedRequests = {
  __typename?: "PaginatedRequests";
  currentPage?: Maybe<Scalars["Int"]["output"]>;
  limit?: Maybe<Scalars["Int"]["output"]>;
  requests?: Maybe<Array<Maybe<Ticket>>>;
  skip?: Maybe<Scalars["Int"]["output"]>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
};

export type PaginatedResources = {
  __typename?: "PaginatedResources";
  currentPage?: Maybe<Scalars["Int"]["output"]>;
  limit?: Maybe<Scalars["Int"]["output"]>;
  resources?: Maybe<Array<Maybe<Resource>>>;
  skip?: Maybe<Scalars["Int"]["output"]>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
};

export type PaginatedSharedAssetTemplates = {
  __typename?: "PaginatedSharedAssetTemplates";
  currentPage?: Maybe<Scalars["Int"]["output"]>;
  limit?: Maybe<Scalars["Int"]["output"]>;
  skip?: Maybe<Scalars["Int"]["output"]>;
  templates?: Maybe<Array<Maybe<AssetTemplate>>>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
};

export type PaginatedSharedAssetsOem = {
  __typename?: "PaginatedSharedAssetsOem";
  currentPage?: Maybe<Scalars["Int"]["output"]>;
  limit?: Maybe<Scalars["Int"]["output"]>;
  oems?: Maybe<Array<Maybe<Oem>>>;
  skip?: Maybe<Scalars["Int"]["output"]>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
};

export type PaginatedTeams = {
  __typename?: "PaginatedTeams";
  currentPage?: Maybe<Scalars["Int"]["output"]>;
  limit?: Maybe<Scalars["Int"]["output"]>;
  skip?: Maybe<Scalars["Int"]["output"]>;
  teams?: Maybe<Array<Maybe<Team>>>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
};

export type PaginatedTickets = {
  __typename?: "PaginatedTickets";
  currentPage?: Maybe<Scalars["Int"]["output"]>;
  limit?: Maybe<Scalars["Int"]["output"]>;
  skip?: Maybe<Scalars["Int"]["output"]>;
  tickets?: Maybe<Array<Maybe<Ticket>>>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
};

export type PaginatedUserSchedules = {
  __typename?: "PaginatedUserSchedules";
  currentPage?: Maybe<Scalars["Int"]["output"]>;
  limit?: Maybe<Scalars["Int"]["output"]>;
  skip?: Maybe<Scalars["Int"]["output"]>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
  userSchedules?: Maybe<Array<Maybe<UserSchedule>>>;
};

export type PaginatedUsers = {
  __typename?: "PaginatedUsers";
  currentPage?: Maybe<Scalars["Int"]["output"]>;
  limit?: Maybe<Scalars["Int"]["output"]>;
  skip?: Maybe<Scalars["Int"]["output"]>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
  users?: Maybe<Array<Maybe<User>>>;
};

export enum PaidFeaturesEnum {
  "3DGuides" = "_3DGuides",
  "3DModels" = "_3DModels",
  ActivityLogging = "activityLogging",
  AiAssistants = "aiAssistants",
  AiNotetaker = "aiNotetaker",
  AiSearch = "aiSearch",
  AiSearchSummary = "aiSearchSummary",
  Analytics = "analytics",
  Branding = "branding",
  CalendarSync = "calendarSync",
  CustomerPortal = "customerPortal",
  Emails = "emails",
  Hierarchy = "hierarchy",
  PreventiveMaintenance = "preventiveMaintenance",
  Procedures = "procedures",
  Scheduler = "scheduler",
  Teams = "teams",
  WorkManagement = "workManagement",
}

export type PlanTier = {
  __typename?: "PlanTier";
  allowedFeatures?: Maybe<Array<Maybe<Scalars["String"]["output"]>>>;
  limits?: Maybe<Array<Maybe<TierLimit>>>;
  name?: Maybe<Scalars["String"]["output"]>;
  price?: Maybe<Scalars["Float"]["output"]>;
  type?: Maybe<Scalars["String"]["output"]>;
};

export type PreventiveMaintenance = {
  __typename?: "PreventiveMaintenance";
  _id?: Maybe<Scalars["ID"]["output"]>;
  createdBy?: Maybe<User>;
  description?: Maybe<Scalars["String"]["output"]>;
  eventDate: Scalars["DateTime"]["output"];
  frequency?: Maybe<Scalars["String"]["output"]>;
  inventoryParts?: Maybe<Array<Maybe<PmeAssetPart>>>;
  machine: Asset;
  nextCreationDate?: Maybe<Scalars["DateTime"]["output"]>;
  oem: Oem;
  procedures?: Maybe<Array<Maybe<PmeProcedure>>>;
  repeatIn?: Maybe<Scalars["String"]["output"]>;
  repeatInNumber?: Maybe<Scalars["Int"]["output"]>;
  startDate?: Maybe<Scalars["DateTime"]["output"]>;
  ticketCreationIn?: Maybe<Scalars["String"]["output"]>;
  ticketCreationNumber?: Maybe<Scalars["Int"]["output"]>;
  tickets?: Maybe<Array<Maybe<Ticket>>>;
  timezoneOffset?: Maybe<Scalars["Int"]["output"]>;
  title: Scalars["String"]["output"];
  user?: Maybe<User>;
};

export type Procedure = {
  __typename?: "Procedure";
  _id?: Maybe<Scalars["ID"]["output"]>;
  allowUsersToAddMoreSignatures?: Maybe<Scalars["Boolean"]["output"]>;
  children?: Maybe<Array<Maybe<ProcedureNode>>>;
  createdAt?: Maybe<Scalars["DateTime"]["output"]>;
  description?: Maybe<Scalars["String"]["output"]>;
  name?: Maybe<Scalars["String"]["output"]>;
  oem?: Maybe<Oem>;
  pageHeader?: Maybe<FieldAttachment>;
  pdfUrl?: Maybe<Scalars["String"]["output"]>;
  procedureId?: Maybe<Scalars["String"]["output"]>;
  signatures?: Maybe<Array<Maybe<ProcedureSignature>>>;
  state: ProcedureStates;
  submittedBy?: Maybe<User>;
  updatedAt?: Maybe<Scalars["DateTime"]["output"]>;
};

export type ProcedureNode = {
  __typename?: "ProcedureNode";
  _id?: Maybe<Scalars["ID"]["output"]>;
  allowExtraRows?: Maybe<Scalars["Boolean"]["output"]>;
  attachments?: Maybe<Array<Maybe<FieldAttachment>>>;
  children?: Maybe<Array<Maybe<ProcedureNode>>>;
  description?: Maybe<Scalars["String"]["output"]>;
  isRequired?: Maybe<Scalars["Boolean"]["output"]>;
  name?: Maybe<Scalars["String"]["output"]>;
  options?: Maybe<Array<Maybe<FieldOption>>>;
  tableOption?: Maybe<TableOption>;
  type?: Maybe<NodeAndFieldTypes>;
  value?: Maybe<Scalars["Mixed"]["output"]>;
};

export type ProcedureSignature = {
  __typename?: "ProcedureSignature";
  _id?: Maybe<Scalars["ID"]["output"]>;
  date?: Maybe<Scalars["DateTime"]["output"]>;
  isAdditionalSignature?: Maybe<Scalars["Boolean"]["output"]>;
  name?: Maybe<Scalars["String"]["output"]>;
  signatoryTitle?: Maybe<Scalars["String"]["output"]>;
  signatureUrl?: Maybe<Scalars["String"]["output"]>;
};

export enum ProcedureStates {
  Draft = "DRAFT",
  Finalized = "FINALIZED",
  NotStarted = "NOT_STARTED",
}

export type ProcedureTemplate = {
  __typename?: "ProcedureTemplate";
  _id?: Maybe<Scalars["ID"]["output"]>;
  allowUsersToAddMoreSignatures?: Maybe<Scalars["Boolean"]["output"]>;
  children?: Maybe<Array<Maybe<ProcedureTemplateNode>>>;
  createdAt?: Maybe<Scalars["DateTime"]["output"]>;
  createdBy?: Maybe<User>;
  description?: Maybe<Scalars["String"]["output"]>;
  name?: Maybe<Scalars["String"]["output"]>;
  oem?: Maybe<Oem>;
  pageHeader?: Maybe<FieldAttachment>;
  signatures?: Maybe<Array<Maybe<ProcedureTemplateSignature>>>;
  updatedAt?: Maybe<Scalars["DateTime"]["output"]>;
};

export type ProcedureTemplateList = {
  __typename?: "ProcedureTemplateList";
  procedures?: Maybe<Array<Maybe<ProcedureTemplate>>>;
  totalCount?: Maybe<Scalars["Int"]["output"]>;
};

export type ProcedureTemplateNode = {
  __typename?: "ProcedureTemplateNode";
  _id?: Maybe<Scalars["ID"]["output"]>;
  allowExtraRows?: Maybe<Scalars["Boolean"]["output"]>;
  attachments?: Maybe<Array<Maybe<FieldAttachment>>>;
  children?: Maybe<Array<Maybe<ProcedureTemplateNode>>>;
  description?: Maybe<Scalars["String"]["output"]>;
  isRequired?: Maybe<Scalars["Boolean"]["output"]>;
  name?: Maybe<Scalars["String"]["output"]>;
  options?: Maybe<Array<Maybe<FieldOption>>>;
  tableOption?: Maybe<TableOption>;
  type?: Maybe<NodeAndFieldTypes>;
  value?: Maybe<Array<Maybe<Scalars["JSON"]["output"]>>>;
};

export type ProcedureTemplateSignature = {
  __typename?: "ProcedureTemplateSignature";
  _id?: Maybe<Scalars["ID"]["output"]>;
  signatoryTitle?: Maybe<Scalars["String"]["output"]>;
};

export type ProductAccess = {
  __typename?: "ProductAccess";
  _3dModel?: Maybe<Scalars["Boolean"]["output"]>;
  allowCopying?: Maybe<Scalars["Boolean"]["output"]>;
  documentation?: Maybe<Scalars["Boolean"]["output"]>;
  parts?: Maybe<Scalars["Boolean"]["output"]>;
  procedures?: Maybe<Scalars["Boolean"]["output"]>;
};

export type ProductPlan = {
  __typename?: "ProductPlan";
  conflictingPurchases?: Maybe<Array<Maybe<Scalars["String"]["output"]>>>;
  name?: Maybe<Scalars["String"]["output"]>;
  tiers?: Maybe<Array<Maybe<PlanTier>>>;
  type?: Maybe<Scalars["String"]["output"]>;
};

export type PublishedMachine = {
  __typename?: "PublishedMachine";
  machine?: Maybe<Asset>;
  machineHistoryNoteId?: Maybe<Scalars["ID"]["output"]>;
  publishedAt?: Maybe<Scalars["DateTime"]["output"]>;
  publishedBy?: Maybe<Scalars["ID"]["output"]>;
};

export type Query = {
  __typename?: "Query";
  _checkAuth?: Maybe<Scalars["String"]["output"]>;
  apiKey?: Maybe<ApiKey>;
  appUrl?: Maybe<Scalars["String"]["output"]>;
  checkIfTicketsExistForType?: Maybe<Scalars["Boolean"]["output"]>;
  checkTicketHasStatus?: Maybe<Scalars["Boolean"]["output"]>;
  connection: Scalars["String"]["output"];
  currentUser?: Maybe<User>;
  downloadEmailFile?: Maybe<Scalars["String"]["output"]>;
  downloadProcedurePDF?: Maybe<Scalars["String"]["output"]>;
  downloadWOPDF?: Maybe<Scalars["String"]["output"]>;
  expensiveQuery?: Maybe<Scalars["String"]["output"]>;
  get3DGuideById?: Maybe<Guide>;
  get3DModels?: Maybe<PaginatedModels>;
  getAiAssistant?: Maybe<AiAssistant>;
  getAiAssistantChat?: Maybe<AiAssistantChat>;
  getAiAssistantUsage?: Maybe<UserAiAssistantUsage>;
  getAppConfig?: Maybe<AppConfig>;
  getAssetTemplate3DAuthToken?: Maybe<Scalars["String"]["output"]>;
  getBoxFolderAccessToken?: Maybe<Scalars["String"]["output"]>;
  getBoxFolderToken?: Maybe<Scalars["String"]["output"]>;
  getCalendarSyncAuthUrl: Scalars["String"]["output"];
  getCustomerInventoryPart?: Maybe<InventoryPart>;
  getCustomerPortal?: Maybe<CustomerPortal>;
  getDataCsv?: Maybe<Scalars["Boolean"]["output"]>;
  getDocumentAnswers?: Maybe<Array<Maybe<DocumentAnswers>>>;
  getDocumentAuthors?: Maybe<Array<Maybe<Scalars["String"]["output"]>>>;
  getDocumentById?: Maybe<Document>;
  getDocumentChunksByIds?: Maybe<Array<Maybe<DocumentChunk>>>;
  getDraft?: Maybe<EmailDraft>;
  getDraftAndJunkEmailCount?: Maybe<DraftJunkCount>;
  getEmailAuthUrl: Scalars["String"]["output"];
  getEmailFileUploadUrl?: Maybe<Scalars["String"]["output"]>;
  getEmailMessage?: Maybe<EmailMessageWithFileUrl>;
  getEmailMessageNylas?: Maybe<EmailMessageWithFileUrl>;
  getEmailThread?: Maybe<EmailThreadWithTicket>;
  getEmailThreadNylas?: Maybe<EmailThreadWithTicket>;
  getInventoryPart?: Maybe<InventoryPart>;
  getKnowledgeBase?: Maybe<KnowledgeBase>;
  getMachine3DAuthToken?: Maybe<Scalars["String"]["output"]>;
  getNewChatToken?: Maybe<Scalars["String"]["output"]>;
  getOemBySlug?: Maybe<Oem>;
  getOwnAiNote?: Maybe<AiNote>;
  getOwnCustomerAssetById?: Maybe<Asset>;
  getOwnCustomerAssetByUuid?: Maybe<Asset>;
  getOwnFacilityTicketById?: Maybe<Ticket>;
  getOwnOem?: Maybe<Oem>;
  getOwnOemAnalytics?: Maybe<Analytics>;
  getOwnOemAssetById?: Maybe<Asset>;
  getOwnOemAssetTemplate?: Maybe<AssetTemplate>;
  getOwnOemCustomerById?: Maybe<Customer>;
  getOwnOemProcedureById?: Maybe<Procedure>;
  getOwnOemProcedureTemplate?: Maybe<ProcedureTemplate>;
  getOwnOemReportByID?: Maybe<Report>;
  getOwnOemTicketById?: Maybe<Ticket>;
  getOwnTicketScheduleDraft?: Maybe<TicketScheduleDraft>;
  getPreventiveMaintenanceEventByID?: Maybe<PreventiveMaintenance>;
  getRequest?: Maybe<Ticket>;
  getSharedOrganizationDetails?: Maybe<SharedOrganizationDetails>;
  getTeam?: Maybe<Team>;
  list3DGuides?: Maybe<PaginatedGuide>;
  listAdditionalFields?: Maybe<Array<Maybe<AdditionalField>>>;
  listAiAssistantChats?: Maybe<PaginatedAiAssistantChats>;
  listAiAssistants?: Maybe<AiAssistantWithCount>;
  listAllImporters?: Maybe<Array<Maybe<Importer>>>;
  listAllOemCustomersInArea?: Maybe<Array<Maybe<CustomerMarker>>>;
  listAllOwnOemAssets?: Maybe<PaginatedAssets>;
  listAllOwnOemCustomers?: Maybe<PaginatedCustomers>;
  listAllOwnOemReports?: Maybe<PaginatedReports>;
  listAllPreventiveMaintenanceEvents?: Maybe<PaginatedPreventiveMaintenanceEvents>;
  listAssetsWithLinkedTemplates: Array<Scalars["String"]["output"]>;
  listAssignableUsers?: Maybe<Array<Maybe<User>>>;
  listConnectionRequests?: Maybe<Array<Maybe<ConnectionRequest>>>;
  listContacts?: Maybe<PaginatedContacts>;
  listCustomerPortals?: Maybe<PaginatedCustomerPortals>;
  listCustomersWithTickets?: Maybe<Array<Maybe<Scalars["String"]["output"]>>>;
  listDocuments?: Maybe<PaginatedDocuments>;
  listDrafts?: Maybe<PaginatedEmailDrafts>;
  listEmailContacts?: Maybe<PaginatedEmailContacts>;
  listEmailFolders?: Maybe<PaginatedEmailFolders>;
  listEmailFoldersNylas?: Maybe<PaginatedEmailFolders>;
  listEmailThreadsByFolder?: Maybe<PaginatedEmailThreads>;
  listEmailThreadsByFolderNylas?: Maybe<PaginatedEmailThreads>;
  listEmailThreadsByTicket?: Maybe<Array<Maybe<EmailThread>>>;
  listEmailThreadsByTicketNylas?: Maybe<Array<Maybe<Scalars["JSON"]["output"]>>>;
  listGroupedByCustomersTickets?: Maybe<Array<Maybe<GroupedTickets>>>;
  listOemCustomersInArea?: Maybe<Array<Maybe<CustomerMarker>>>;
  listOwnAiNotes?: Maybe<PaginatedAiNotes>;
  listOwnAssetHierarchyTickets?: Maybe<PaginatedTickets>;
  listOwnCustomerAssets?: Maybe<PaginatedAssets>;
  listOwnCustomerTickets?: Maybe<PaginatedTickets>;
  listOwnFacilityClosedTickets?: Maybe<Array<Maybe<Ticket>>>;
  listOwnFacilityOpenTickets?: Maybe<Array<Maybe<Ticket>>>;
  listOwnOemActivityLogs?: Maybe<PaginatedActivityLogs>;
  listOwnOemAllTickets?: Maybe<Array<Maybe<Ticket>>>;
  listOwnOemAssetTemplates: Array<Maybe<AssetTemplate>>;
  listOwnOemAssetTemplatesMissingTemplateIds?: Maybe<Array<Maybe<Scalars["String"]["output"]>>>;
  listOwnOemAssetTicketHistoryById?: Maybe<Array<Maybe<Ticket>>>;
  listOwnOemAssetsMissingSerialNumbers?: Maybe<Array<Maybe<Scalars["String"]["output"]>>>;
  listOwnOemClosedTickets?: Maybe<Array<Maybe<Ticket>>>;
  listOwnOemConnectionHistory?: Maybe<PaginatedConnectionHistory>;
  listOwnOemCustomFields?: Maybe<Array<Maybe<CustomAdditionalField>>>;
  listOwnOemCustomersMissingClientIds?: Maybe<Array<Maybe<Scalars["String"]["output"]>>>;
  listOwnOemDuplicateTickets?: Maybe<Array<Maybe<Scalars["String"]["output"]>>>;
  listOwnOemFacilityUsers?: Maybe<PaginatedUsers>;
  listOwnOemInventoryPart?: Maybe<PaginatedInventoryParts>;
  listOwnOemKanbanAssets?: Maybe<PaginatedKanbanAssets>;
  listOwnOemKanbanCustomers?: Maybe<PaginatedKanbanCustomers>;
  listOwnOemKanbanTickets?: Maybe<PaginatedKanbanTickets>;
  listOwnOemMachineHistory?: Maybe<PaginatedMachineHistory>;
  listOwnOemOpenTickets?: Maybe<Array<Maybe<Ticket>>>;
  listOwnOemPlans?: Maybe<Array<Maybe<OemPlan>>>;
  listOwnOemProcedureTemplates: Array<Maybe<ProcedureTemplate>>;
  listOwnOemProcedureTemplatesWithCount: ProcedureTemplateList;
  listOwnOemResources?: Maybe<PaginatedResources>;
  listOwnOemSharedAssetHistory?: Maybe<PaginatedMachineHistory>;
  listOwnOemSupportAccounts?: Maybe<Array<Maybe<User>>>;
  listOwnOemTickets?: Maybe<PaginatedTickets>;
  listOwnOemTimeTrackers?: Maybe<Array<Maybe<TimeTracker>>>;
  listOwnOemUserClosedTickets?: Maybe<Array<Maybe<Ticket>>>;
  listOwnOemUserOpenTickets?: Maybe<Array<Maybe<Ticket>>>;
  listOwnTicketMentionableUsers?: Maybe<TicketMentionUsers>;
  listOwnUserSchedules?: Maybe<PaginatedUserSchedules>;
  listPreventiveMaintenanceEvents?: Maybe<Array<Maybe<PreventiveMaintenance>>>;
  listRequestableAssets?: Maybe<PaginatedAssets>;
  listRequests?: Maybe<PaginatedRequests>;
  listSharedAiAssistants?: Maybe<PaginatedAssistants>;
  listSharedAssetTemplates?: Maybe<PaginatedSharedAssetTemplates>;
  listSharedAssetsOrganizations?: Maybe<PaginatedSharedAssetsOem>;
  listSharedPreventiveMaintenanceEvents?: Maybe<Array<Maybe<PreventiveMaintenance>>>;
  listTeams?: Maybe<PaginatedTeams>;
  listTicketTypes?: Maybe<Array<Maybe<TicketType>>>;
  listUsersCalendarEvents?: Maybe<Array<Maybe<CalendarEvent>>>;
  listUsersDuplicatedEmails?: Maybe<Array<Maybe<Scalars["String"]["output"]>>>;
  oemById?: Maybe<Oem>;
  oemByIdWithAdmin?: Maybe<OemAdmin>;
  oems?: Maybe<Array<Maybe<Oem>>>;
  ping?: Maybe<Scalars["String"]["output"]>;
  queryAiAssistant?: Maybe<AiAssistantQueryResponse>;
  queryDocuments?: Maybe<QueryResponse>;
  test?: Maybe<Scalars["String"]["output"]>;
  tickets?: Maybe<Array<Maybe<Ticket>>>;
  totalTickets?: Maybe<Scalars["Int"]["output"]>;
};

export type QueryCheckIfTicketsExistForTypeArgs = {
  ticketTypeId: Scalars["ID"]["input"];
};

export type QueryCheckTicketHasStatusArgs = {
  statusId: Scalars["ID"]["input"];
};

export type QueryCurrentUserArgs = {
  delay?: InputMaybe<Scalars["Int"]["input"]>;
};

export type QueryDownloadEmailFileArgs = {
  emailAddressId: Scalars["ID"]["input"];
  fileId: Scalars["String"]["input"];
  messageId: Scalars["String"]["input"];
  uuid: Scalars["String"]["input"];
};

export type QueryDownloadProcedurePdfArgs = {
  id: Scalars["ID"]["input"];
  timezone: Scalars["String"]["input"];
  uuid: Scalars["String"]["input"];
};

export type QueryDownloadWopdfArgs = {
  input?: InputMaybe<InputDownloadWopdf>;
};

export type QueryGet3DGuideByIdArgs = {
  id: Scalars["ID"]["input"];
};

export type QueryGet3DModelsArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryGetAiAssistantArgs = {
  id?: InputMaybe<Scalars["ID"]["input"]>;
};

export type QueryGetAiAssistantChatArgs = {
  id?: InputMaybe<Scalars["ID"]["input"]>;
};

export type QueryGetAiAssistantUsageArgs = {
  oemId: Scalars["ID"]["input"];
};

export type QueryGetAssetTemplate3DAuthTokenArgs = {
  id: Scalars["ID"]["input"];
};

export type QueryGetBoxFolderAccessTokenArgs = {
  assetId: Scalars["ID"]["input"];
};

export type QueryGetBoxFolderTokenArgs = {
  assetId?: InputMaybe<Scalars["ID"]["input"]>;
  assetTemplateId?: InputMaybe<Scalars["ID"]["input"]>;
};

export type QueryGetCustomerInventoryPartArgs = {
  id: Scalars["ID"]["input"];
};

export type QueryGetCustomerPortalArgs = {
  id: Scalars["ID"]["input"];
};

export type QueryGetDataCsvArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryGetDocumentAnswersArgs = {
  jobId: Scalars["String"]["input"];
};

export type QueryGetDocumentByIdArgs = {
  documentId: Scalars["ID"]["input"];
  language?: InputMaybe<Scalars["String"]["input"]>;
  pages?: InputMaybe<Array<InputMaybe<Scalars["Int"]["input"]>>>;
};

export type QueryGetDocumentChunksByIdsArgs = {
  documentChunkIds: Array<Scalars["ID"]["input"]>;
};

export type QueryGetDraftArgs = {
  draftId: Scalars["String"]["input"];
  emailAddressId: Scalars["ID"]["input"];
};

export type QueryGetDraftAndJunkEmailCountArgs = {
  emailAddressId: Scalars["ID"]["input"];
};

export type QueryGetEmailMessageArgs = {
  emailAddressId: Scalars["ID"]["input"];
  messageId: Scalars["String"]["input"];
};

export type QueryGetEmailMessageNylasArgs = {
  emailAddressId: Scalars["ID"]["input"];
  messageId: Scalars["String"]["input"];
};

export type QueryGetEmailThreadArgs = {
  emailAddressId: Scalars["ID"]["input"];
  threadId: Scalars["String"]["input"];
};

export type QueryGetEmailThreadNylasArgs = {
  emailAddressId: Scalars["ID"]["input"];
  threadId: Scalars["String"]["input"];
};

export type QueryGetInventoryPartArgs = {
  id: Scalars["ID"]["input"];
};

export type QueryGetMachine3DAuthTokenArgs = {
  id: Scalars["ID"]["input"];
};

export type QueryGetOemBySlugArgs = {
  slug: Scalars["String"]["input"];
};

export type QueryGetOwnAiNoteArgs = {
  id: Scalars["ID"]["input"];
};

export type QueryGetOwnCustomerAssetByIdArgs = {
  id: Scalars["ID"]["input"];
};

export type QueryGetOwnCustomerAssetByUuidArgs = {
  uuid: Scalars["ID"]["input"];
};

export type QueryGetOwnFacilityTicketByIdArgs = {
  id: Scalars["ID"]["input"];
};

export type QueryGetOwnOemAnalyticsArgs = {
  input?: InputMaybe<InputGetAnalytics>;
};

export type QueryGetOwnOemAssetByIdArgs = {
  id: Scalars["ID"]["input"];
  isSharedAsset?: InputMaybe<Scalars["Boolean"]["input"]>;
};

export type QueryGetOwnOemAssetTemplateArgs = {
  templateId: Scalars["ID"]["input"];
};

export type QueryGetOwnOemCustomerByIdArgs = {
  id: Scalars["ID"]["input"];
};

export type QueryGetOwnOemProcedureByIdArgs = {
  id: Scalars["ID"]["input"];
};

export type QueryGetOwnOemProcedureTemplateArgs = {
  id?: InputMaybe<Scalars["String"]["input"]>;
};

export type QueryGetOwnOemReportByIdArgs = {
  id: Scalars["ID"]["input"];
};

export type QueryGetOwnOemTicketByIdArgs = {
  id: Scalars["ID"]["input"];
};

export type QueryGetPreventiveMaintenanceEventByIdArgs = {
  id: Scalars["ID"]["input"];
};

export type QueryGetRequestArgs = {
  requestId: Scalars["ID"]["input"];
};

export type QueryGetSharedOrganizationDetailsArgs = {
  connectionId: Scalars["ID"]["input"];
};

export type QueryGetTeamArgs = {
  id: Scalars["ID"]["input"];
};

export type QueryList3DGuidesArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListAdditionalFieldsArgs = {
  type?: InputMaybe<Types>;
};

export type QueryListAiAssistantChatsArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListAiAssistantsArgs = {
  limit?: InputMaybe<Scalars["Int"]["input"]>;
  searchQuery?: InputMaybe<Scalars["String"]["input"]>;
};

export type QueryListAllOemCustomersInAreaArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListAllOwnOemAssetsArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListAllOwnOemCustomersArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListAllOwnOemReportsArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListAllPreventiveMaintenanceEventsArgs = {
  params: InputQueryParams;
};

export type QueryListAssetsWithLinkedTemplatesArgs = {
  serialNumbers?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>;
};

export type QueryListAssignableUsersArgs = {
  ticketId: Scalars["ID"]["input"];
};

export type QueryListConnectionRequestsArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListContactsArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListCustomerPortalsArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListCustomersWithTicketsArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListDocumentsArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListDraftsArgs = {
  emailAddressId: Scalars["ID"]["input"];
  pageToken?: InputMaybe<Scalars["String"]["input"]>;
  search?: InputMaybe<Scalars["String"]["input"]>;
};

export type QueryListEmailContactsArgs = {
  emailAddressId: Scalars["ID"]["input"];
  pageToken?: InputMaybe<Scalars["String"]["input"]>;
  search?: InputMaybe<Scalars["String"]["input"]>;
};

export type QueryListEmailFoldersArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListEmailFoldersNylasArgs = {
  emailAddressId: Scalars["ID"]["input"];
  pageToken?: InputMaybe<Scalars["String"]["input"]>;
};

export type QueryListEmailThreadsByFolderArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListEmailThreadsByFolderNylasArgs = {
  emailAddressId: Scalars["ID"]["input"];
  folder: Scalars["String"]["input"];
  pageToken?: InputMaybe<Scalars["String"]["input"]>;
  search: Scalars["String"]["input"];
};

export type QueryListEmailThreadsByTicketArgs = {
  emailAddressId: Scalars["ID"]["input"];
  ticketId: Scalars["ID"]["input"];
};

export type QueryListEmailThreadsByTicketNylasArgs = {
  emailAddressId: Scalars["ID"]["input"];
  ticketId: Scalars["ID"]["input"];
};

export type QueryListGroupedByCustomersTicketsArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListOemCustomersInAreaArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListOwnAiNotesArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListOwnAssetHierarchyTicketsArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListOwnCustomerAssetsArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListOwnCustomerTicketsArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListOwnOemActivityLogsArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListOwnOemAllTicketsArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListOwnOemAssetTemplatesArgs = {
  params: InputQueryParams;
};

export type QueryListOwnOemAssetTemplatesMissingTemplateIdsArgs = {
  templateIds: Array<Scalars["String"]["input"]>;
};

export type QueryListOwnOemAssetTicketHistoryByIdArgs = {
  id: Scalars["ID"]["input"];
};

export type QueryListOwnOemAssetsMissingSerialNumbersArgs = {
  serialNumbers: Array<Scalars["String"]["input"]>;
};

export type QueryListOwnOemConnectionHistoryArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListOwnOemCustomFieldsArgs = {
  type?: InputMaybe<Types>;
};

export type QueryListOwnOemCustomersMissingClientIdsArgs = {
  clientIds: Array<Scalars["String"]["input"]>;
};

export type QueryListOwnOemDuplicateTicketsArgs = {
  ticketIds: Array<Scalars["String"]["input"]>;
};

export type QueryListOwnOemFacilityUsersArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListOwnOemInventoryPartArgs = {
  params?: InputMaybe<InputInventoryQueryParams>;
};

export type QueryListOwnOemKanbanAssetsArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListOwnOemKanbanCustomersArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListOwnOemKanbanTicketsArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListOwnOemMachineHistoryArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListOwnOemOpenTicketsArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListOwnOemProcedureTemplatesArgs = {
  params: InputQueryParams;
};

export type QueryListOwnOemProcedureTemplatesWithCountArgs = {
  params: InputQueryParams;
};

export type QueryListOwnOemResourcesArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListOwnOemSharedAssetHistoryArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListOwnOemSupportAccountsArgs = {
  excludeTeam?: InputMaybe<Scalars["String"]["input"]>;
  getAllUsers?: InputMaybe<Scalars["Boolean"]["input"]>;
  team?: InputMaybe<Scalars["String"]["input"]>;
  useAssetTeams?: InputMaybe<Scalars["Boolean"]["input"]>;
  withoutTeam?: InputMaybe<Scalars["Boolean"]["input"]>;
};

export type QueryListOwnOemTicketsArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListOwnOemTimeTrackersArgs = {
  fieldType: FieldTypes;
  type: Types;
};

export type QueryListOwnTicketMentionableUsersArgs = {
  ticketId: Scalars["ID"]["input"];
};

export type QueryListOwnUserSchedulesArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListPreventiveMaintenanceEventsArgs = {
  params: InputQueryParams;
};

export type QueryListRequestableAssetsArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListRequestsArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListSharedAiAssistantsArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListSharedAssetTemplatesArgs = {
  params: InputQueryParams;
};

export type QueryListSharedAssetsOrganizationsArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListSharedPreventiveMaintenanceEventsArgs = {
  params: InputQueryParams;
};

export type QueryListTeamsArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryListUsersCalendarEventsArgs = {
  endDate: Scalars["DateTime"]["input"];
  startDate: Scalars["DateTime"]["input"];
};

export type QueryListUsersDuplicatedEmailsArgs = {
  emails: Array<Scalars["String"]["input"]>;
};

export type QueryOemByIdArgs = {
  id: Scalars["ID"]["input"];
};

export type QueryOemByIdWithAdminArgs = {
  id: Scalars["ID"]["input"];
};

export type QueryOemsArgs = {
  params: InputQueryOem;
};

export type QueryQueryAiAssistantArgs = {
  input: InputQueryAiAssistant;
};

export type QueryQueryDocumentsArgs = {
  activeFilters?: InputMaybe<Scalars["JSON"]["input"]>;
  query: Scalars["String"]["input"];
};

export type QueryTicketsArgs = {
  params: InputQueryParams;
};

export type QueryTotalTicketsArgs = {
  params?: InputMaybe<InputQueryParams>;
};

export type QueryResponse = {
  __typename?: "QueryResponse";
  groupedResults?: Maybe<Array<Maybe<Document>>>;
  jobId?: Maybe<Scalars["String"]["output"]>;
};

export type RemoveTicketTypeInput = {
  teamId: Scalars["ID"]["input"];
  ticketTypeId: Scalars["ID"]["input"];
};

export type Report = {
  __typename?: "Report";
  _id?: Maybe<Scalars["ID"]["output"]>;
  chartType?: Maybe<Scalars["String"]["output"]>;
  data?: Maybe<Scalars["MJSON"]["output"]>;
  entity?: Maybe<Scalars["String"]["output"]>;
  filters?: Maybe<Array<Maybe<ReportFilter>>>;
  inputs?: Maybe<ReportOptions>;
  oem?: Maybe<Oem>;
  segment?: Maybe<ReportField>;
  table?: Maybe<Array<Maybe<ReportField>>>;
  title?: Maybe<Scalars["String"]["output"]>;
  xAxis?: Maybe<ReportField>;
};

export type ReportField = {
  __typename?: "ReportField";
  label?: Maybe<Scalars["String"]["output"]>;
  value?: Maybe<Scalars["Mixed"]["output"]>;
};

export type ReportFilter = {
  __typename?: "ReportFilter";
  condition?: Maybe<Scalars["String"]["output"]>;
  field?: Maybe<Scalars["String"]["output"]>;
  filterType?: Maybe<Scalars["String"]["output"]>;
  value?: Maybe<Array<Maybe<ReportField>>>;
};

export type ReportOptionFilter = {
  __typename?: "ReportOptionFilter";
  condition?: Maybe<Array<Maybe<Scalars["String"]["output"]>>>;
  field?: Maybe<Scalars["String"]["output"]>;
  filterType?: Maybe<Scalars["String"]["output"]>;
  value?: Maybe<Array<Maybe<ReportField>>>;
};

export type ReportOptions = {
  __typename?: "ReportOptions";
  filter?: Maybe<Array<Maybe<ReportOptionFilter>>>;
  segment?: Maybe<Array<Maybe<ReportField>>>;
  table?: Maybe<Array<Maybe<ReportField>>>;
  xAxis?: Maybe<Array<Maybe<ReportField>>>;
};

export type Resource = {
  __typename?: "Resource";
  _id: Scalars["ID"]["output"];
  name: Scalars["String"]["output"];
  oem: Oem;
  resourceId?: Maybe<Scalars["String"]["output"]>;
  ticketsCount?: Maybe<Scalars["Int"]["output"]>;
  type: Scalars["String"]["output"];
};

export type S3Payload = {
  __typename?: "S3Payload";
  id: Scalars["String"]["output"];
  signedRequest: Scalars["String"]["output"];
  url?: Maybe<Scalars["String"]["output"]>;
};

export type Schedule = {
  __typename?: "Schedule";
  endTime?: Maybe<Scalars["DateTime"]["output"]>;
  endTimezone?: Maybe<Scalars["String"]["output"]>;
  isAllDay?: Maybe<Scalars["Boolean"]["output"]>;
  isTimezoneEnabled?: Maybe<Scalars["Boolean"]["output"]>;
  startTime?: Maybe<Scalars["DateTime"]["output"]>;
  startTimezone?: Maybe<Scalars["String"]["output"]>;
};

export type ScheduleInputType = {
  endTime?: InputMaybe<Scalars["DateTime"]["input"]>;
  endTimezone?: InputMaybe<Scalars["String"]["input"]>;
  isAllDay?: InputMaybe<Scalars["Boolean"]["input"]>;
  isTimezoneEnabled?: InputMaybe<Scalars["Boolean"]["input"]>;
  startTime?: InputMaybe<Scalars["DateTime"]["input"]>;
  startTimezone?: InputMaybe<Scalars["String"]["input"]>;
};

export type SharedOrganizationDetails = {
  __typename?: "SharedOrganizationDetails";
  assetAccess?: Maybe<AssetAccess>;
  oem?: Maybe<Oem>;
};

export type SignupTokenVerification = {
  __typename?: "SignupTokenVerification";
  email?: Maybe<Scalars["String"]["output"]>;
  emailMismatch?: Maybe<Scalars["Boolean"]["output"]>;
  id?: Maybe<Scalars["String"]["output"]>;
  isError?: Maybe<Scalars["Boolean"]["output"]>;
  isInvalidToken?: Maybe<Scalars["Boolean"]["output"]>;
  isInvitationExpired?: Maybe<Scalars["Boolean"]["output"]>;
  message?: Maybe<Scalars["String"]["output"]>;
  notApprovedByOwner?: Maybe<Scalars["Boolean"]["output"]>;
  userAdded?: Maybe<Scalars["Boolean"]["output"]>;
  userExists?: Maybe<Scalars["Boolean"]["output"]>;
};

export type Skill = {
  __typename?: "Skill";
  _id?: Maybe<Scalars["ID"]["output"]>;
  name?: Maybe<Scalars["String"]["output"]>;
};

export type Status = {
  __typename?: "Status";
  _id?: Maybe<Scalars["ID"]["output"]>;
  color?: Maybe<Scalars["String"]["output"]>;
  label?: Maybe<Scalars["String"]["output"]>;
};

export type Statuses = {
  __typename?: "Statuses";
  _id?: Maybe<Scalars["ID"]["output"]>;
  color?: Maybe<Scalars["String"]["output"]>;
  label?: Maybe<Scalars["String"]["output"]>;
};

export type TableColumn = {
  __typename?: "TableColumn";
  _id?: Maybe<Scalars["ID"]["output"]>;
  heading?: Maybe<Scalars["String"]["output"]>;
  uniqueId?: Maybe<Scalars["String"]["output"]>;
  width?: Maybe<Scalars["Int"]["output"]>;
};

export type TableOption = {
  __typename?: "TableOption";
  _id?: Maybe<Scalars["ID"]["output"]>;
  columns?: Maybe<Array<Maybe<TableColumn>>>;
  rowCount?: Maybe<Scalars["Int"]["output"]>;
};

export type Team = {
  __typename?: "Team";
  _id: Scalars["ID"]["output"];
  createdAt?: Maybe<Scalars["DateTime"]["output"]>;
  description?: Maybe<Scalars["String"]["output"]>;
  disallowedTicketTypes?: Maybe<Array<Maybe<TicketType>>>;
  members?: Maybe<Array<Maybe<User>>>;
  name: Scalars["String"]["output"];
  numberOfMembers?: Maybe<Scalars["Int"]["output"]>;
  oem?: Maybe<Oem>;
  teamColor?: Maybe<Scalars["String"]["output"]>;
};

export type TemplateImporter = {
  __typename?: "TemplateImporter";
  templateId?: Maybe<Scalars["String"]["output"]>;
  templateSection?: Maybe<Scalars["String"]["output"]>;
};

export type Ticket = {
  __typename?: "Ticket";
  _id: Scalars["ID"]["output"];
  assignee?: Maybe<User>;
  assignees?: Maybe<Array<Maybe<User>>>;
  attachments?: Maybe<Array<Maybe<TicketAttachment>>>;
  createdAt?: Maybe<Scalars["String"]["output"]>;
  createdBy?: Maybe<User>;
  createdByContact?: Maybe<Contact>;
  customFields?: Maybe<Array<Maybe<CustomField>>>;
  description?: Maybe<Scalars["String"]["output"]>;
  facility?: Maybe<Customer>;
  followers?: Maybe<Array<Maybe<User>>>;
  hasOriginalEventBeenDeleted?: Maybe<Scalars["Boolean"]["output"]>;
  inventoryParts?: Maybe<Array<Maybe<TicketMachinePart>>>;
  isMyTicket?: Maybe<Scalars["Boolean"]["output"]>;
  lastUpdatedAt?: Maybe<Scalars["String"]["output"]>;
  linkedTickets?: Maybe<Array<Maybe<LinkedTicket>>>;
  machine?: Maybe<Asset>;
  maintenanceOn?: Maybe<Scalars["DateTime"]["output"]>;
  notes?: Maybe<Scalars["String"]["output"]>;
  numberOfTeams?: Maybe<Scalars["Int"]["output"]>;
  oem?: Maybe<Oem>;
  preventiveMaintenance?: Maybe<PreventiveMaintenance>;
  procedures?: Maybe<Array<Maybe<TicketProcedure>>>;
  resources?: Maybe<Array<Maybe<Resource>>>;
  schedule?: Maybe<Schedule>;
  status?: Maybe<Scalars["ID"]["output"]>;
  teams?: Maybe<Array<Maybe<Team>>>;
  ticketChatChannels?: Maybe<Array<Maybe<Scalars["String"]["output"]>>>;
  ticketId?: Maybe<Scalars["String"]["output"]>;
  ticketInternalNotesChatChannels?: Maybe<Array<Maybe<Scalars["String"]["output"]>>>;
  ticketType?: Maybe<TicketType>;
  timeTrackerLogs?: Maybe<Array<Maybe<TimeTrackerLog>>>;
  title?: Maybe<Scalars["String"]["output"]>;
  totalTimeLoggedInSeconds?: Maybe<Scalars["Int"]["output"]>;
  /** TODO: Remove unread */
  unread?: Maybe<Scalars["Boolean"]["output"]>;
  updatedAt?: Maybe<Scalars["String"]["output"]>;
  url?: Maybe<Scalars["String"]["output"]>;
  user?: Maybe<User>;
};

export type TicketAttachment = {
  __typename?: "TicketAttachment";
  _id?: Maybe<Scalars["ID"]["output"]>;
  createdAt?: Maybe<Scalars["DateTime"]["output"]>;
  name: Scalars["String"]["output"];
  size: Scalars["Int"]["output"];
  type: Scalars["String"]["output"];
  url: Scalars["String"]["output"];
};

export type TicketMachinePart = {
  __typename?: "TicketMachinePart";
  addedBy?: Maybe<User>;
  part?: Maybe<InventoryPart>;
  quantity?: Maybe<Scalars["Int"]["output"]>;
};

export type TicketMentionUsers = {
  __typename?: "TicketMentionUsers";
  facilityUsers?: Maybe<Array<Maybe<User>>>;
  oemUsers?: Maybe<Array<Maybe<User>>>;
};

export type TicketProcedure = {
  __typename?: "TicketProcedure";
  addedBy?: Maybe<User>;
  procedure?: Maybe<Procedure>;
};

export type TicketScheduleDraft = {
  __typename?: "TicketScheduleDraft";
  _id: Scalars["ID"]["output"];
  draft?: Maybe<Array<Maybe<DraftOperation>>>;
  version?: Maybe<Scalars["Int"]["output"]>;
};

export type TicketType = {
  __typename?: "TicketType";
  _id?: Maybe<Scalars["ID"]["output"]>;
  color?: Maybe<Scalars["String"]["output"]>;
  customFields?: Maybe<Array<Maybe<TicketTypeCustomField>>>;
  descriptionConfig?: Maybe<TicketTypeDescriptionConfig>;
  icon?: Maybe<Scalars["String"]["output"]>;
  isDefault?: Maybe<Scalars["Boolean"]["output"]>;
  isInternal?: Maybe<Scalars["Boolean"]["output"]>;
  isSystem?: Maybe<Scalars["Boolean"]["output"]>;
  name?: Maybe<Scalars["String"]["output"]>;
  partsConfig?: Maybe<TicketTypePartsConfig>;
};

export type TicketTypeCustomField = {
  __typename?: "TicketTypeCustomField";
  _id?: Maybe<Scalars["ID"]["output"]>;
  customAdditionalField?: Maybe<CustomAdditionalField>;
  description?: Maybe<Scalars["String"]["output"]>;
  isRequired?: Maybe<Scalars["Boolean"]["output"]>;
};

export type TicketTypeDescriptionConfig = {
  __typename?: "TicketTypeDescriptionConfig";
  fieldName?: Maybe<Scalars["String"]["output"]>;
  isRequired?: Maybe<Scalars["Boolean"]["output"]>;
  show?: Maybe<Scalars["Boolean"]["output"]>;
};

export type TicketTypePartsConfig = {
  __typename?: "TicketTypePartsConfig";
  fieldName?: Maybe<Scalars["String"]["output"]>;
  isRequired?: Maybe<Scalars["Boolean"]["output"]>;
  show?: Maybe<Scalars["Boolean"]["output"]>;
};

export type TierLimit = {
  __typename?: "TierLimit";
  type?: Maybe<Scalars["String"]["output"]>;
  value?: Maybe<Scalars["Float"]["output"]>;
};

export type TimeTracker = {
  __typename?: "TimeTracker";
  _id?: Maybe<Scalars["ID"]["output"]>;
  color?: Maybe<Scalars["String"]["output"]>;
  createdBy?: Maybe<Scalars["ID"]["output"]>;
  fieldType?: Maybe<FieldTypes>;
  label?: Maybe<Scalars["String"]["output"]>;
  oem?: Maybe<Oem>;
  slug?: Maybe<Scalars["String"]["output"]>;
  type?: Maybe<Types>;
};

export type TimeTrackerLog = {
  __typename?: "TimeTrackerLog";
  _id?: Maybe<Scalars["ID"]["output"]>;
  createdBy?: Maybe<User>;
  description?: Maybe<Scalars["String"]["output"]>;
  endDateTime?: Maybe<Scalars["DateTime"]["output"]>;
  isBillable?: Maybe<Scalars["Boolean"]["output"]>;
  startDateTime?: Maybe<Scalars["DateTime"]["output"]>;
  ticketTag?: Maybe<TimeTracker>;
  timeElapsedInSeconds?: Maybe<Scalars["Int"]["output"]>;
};

export type TranscriptionError = {
  __typename?: "TranscriptionError";
  code?: Maybe<Scalars["Int"]["output"]>;
  message?: Maybe<Scalars["String"]["output"]>;
};

export type TranscriptionResult = {
  __typename?: "TranscriptionResult";
  summary?: Maybe<Scalars["String"]["output"]>;
  title?: Maybe<Scalars["String"]["output"]>;
  transcript?: Maybe<Scalars["String"]["output"]>;
};

export enum Types {
  Facilities = "facilities",
  KnowledgeBase = "knowledgeBase",
  Machines = "machines",
  Parts = "parts",
  Tickets = "tickets",
}

export type UpdateOemAiAssistantConfiguration = {
  allowedExternalQueries?: InputMaybe<Scalars["Float"]["input"]>;
  allowedOcrScans?: InputMaybe<Scalars["Float"]["input"]>;
  allowedQueries?: InputMaybe<Scalars["Float"]["input"]>;
  allowedRecordingSeconds?: InputMaybe<Scalars["Float"]["input"]>;
  allowedStorage?: InputMaybe<Scalars["Float"]["input"]>;
  allowedUsers?: InputMaybe<Scalars["Float"]["input"]>;
  autoApproveOCR?: InputMaybe<Scalars["Boolean"]["input"]>;
  oem: Scalars["String"]["input"];
};

export type UpdateOemCalendarSyncConfiguration = {
  allowedConnectedAccounts?: InputMaybe<Scalars["Float"]["input"]>;
  oem: Scalars["ID"]["input"];
};

export type UsageTracking = {
  __typename?: "UsageTracking";
  logRocket?: Maybe<Scalars["Boolean"]["output"]>;
};

export type User = {
  __typename?: "User";
  _id: Scalars["ID"]["output"];
  about?: Maybe<Scalars["String"]["output"]>;
  access?: Maybe<Scalars["Boolean"]["output"]>;
  aiAssistantConfiguration?: Maybe<AiAssistantConfiguration>;
  autoAppendEmailSignature?: Maybe<Scalars["Boolean"]["output"]>;
  boards?: Maybe<Array<Maybe<Boards>>>;
  calendarSyncAccountLinked?: Maybe<Scalars["Boolean"]["output"]>;
  calendarSyncAccountStatus?: Maybe<Scalars["String"]["output"]>;
  callChannels?: Maybe<Array<Maybe<Scalars["String"]["output"]>>>;
  chatKeys?: Maybe<Scalars["JSON"]["output"]>;
  chatToken?: Maybe<Scalars["String"]["output"]>;
  chatUUID?: Maybe<Scalars["String"]["output"]>;
  chatUUIDMetadata?: Maybe<Scalars["JSON"]["output"]>;
  consumedRecordingSeconds?: Maybe<Scalars["Float"]["output"]>;
  contactId?: Maybe<Scalars["ID"]["output"]>;
  contactName?: Maybe<Scalars["String"]["output"]>;
  deleted?: Maybe<Scalars["Boolean"]["output"]>;
  department?: Maybe<Scalars["String"]["output"]>;
  email?: Maybe<Scalars["String"]["output"]>;
  emailNotification?: Maybe<Scalars["Boolean"]["output"]>;
  emailSignature?: Maybe<Scalars["String"]["output"]>;
  facility?: Maybe<Customer>;
  fileImporter?: Maybe<FileImporter>;
  firstName?: Maybe<Scalars["String"]["output"]>;
  firstSignInRedirectUrl?: Maybe<Scalars["String"]["output"]>;
  foldersAccessToken?: Maybe<Scalars["String"]["output"]>;
  function?: Maybe<Scalars["String"]["output"]>;
  hasAiAssistantAccess?: Maybe<Scalars["Boolean"]["output"]>;
  info?: Maybe<Scalars["String"]["output"]>;
  intercomHash?: Maybe<Scalars["String"]["output"]>;
  isOem?: Maybe<Scalars["Boolean"]["output"]>;
  lastName?: Maybe<Scalars["String"]["output"]>;
  linkedCalendarSyncEmailAddress?: Maybe<Scalars["String"]["output"]>;
  mobile?: Maybe<Scalars["String"]["output"]>;
  name?: Maybe<Scalars["String"]["output"]>;
  notification?: Maybe<Notification>;
  notificationChannel?: Maybe<Scalars["String"]["output"]>;
  notificationChannelGroupName?: Maybe<Scalars["String"]["output"]>;
  numberOfTeams?: Maybe<Scalars["Int"]["output"]>;
  oem?: Maybe<Oem>;
  organizationName?: Maybe<Scalars["String"]["output"]>;
  organizationNotificationChannel?: Maybe<Scalars["String"]["output"]>;
  organizationType?: Maybe<Scalars["String"]["output"]>;
  permissions?: Maybe<Array<Maybe<Scalars["String"]["output"]>>>;
  phone?: Maybe<Scalars["String"]["output"]>;
  productAccess?: Maybe<Array<Maybe<Scalars["String"]["output"]>>>;
  requestsChannelsGroupNames?: Maybe<Array<Maybe<Scalars["String"]["output"]>>>;
  role?: Maybe<EnumRoles>;
  skills?: Maybe<Array<Maybe<Skill>>>;
  teams?: Maybe<Array<Maybe<Team>>>;
  totalActiveTickets?: Maybe<Scalars["Int"]["output"]>;
  userCredentialsSent?: Maybe<Scalars["Boolean"]["output"]>;
  userType?: Maybe<Scalars["String"]["output"]>;
  username?: Maybe<Scalars["String"]["output"]>;
};

export type UserAiAssistantUsage = {
  __typename?: "UserAiAssistantUsage";
  allowedQueries?: Maybe<Scalars["Float"]["output"]>;
  consumedQueries?: Maybe<Scalars["Float"]["output"]>;
  oem?: Maybe<Scalars["ID"]["output"]>;
};

export type UserSchedule = {
  __typename?: "UserSchedule";
  _id?: Maybe<Scalars["ID"]["output"]>;
  oem?: Maybe<Scalars["ID"]["output"]>;
  schedule?: Maybe<Schedule>;
  scheduleType?: Maybe<Scalars["String"]["output"]>;
  ticket?: Maybe<Ticket>;
  user?: Maybe<Scalars["ID"]["output"]>;
  uuid?: Maybe<Scalars["String"]["output"]>;
};

export type VerifyGeneralSignUpToken = {
  __typename?: "VerifyGeneralSignUpToken";
  email?: Maybe<Scalars["String"]["output"]>;
  redirectUrl?: Maybe<Scalars["String"]["output"]>;
};

export enum VisibilityScope {
  External = "external",
  Internal = "internal",
}

export enum EnumRoles {
  Admin = "ADMIN",
  Gmu = "GMU",
  Oem = "OEM",
  Owner = "OWNER",
  Staff = "STAFF",
  Technician = "TECHNICIAN",
  User = "USER",
}

export type QrcMachineCredentials = {
  machineUuid: Scalars["String"]["input"];
};

export type UserCredentials = {
  password: Scalars["Base64"]["input"];
  username: Scalars["String"]["input"];
};

export type UserFacilityCredentials = {
  organization?: InputMaybe<Scalars["ID"]["input"]>;
  password: Scalars["Base64"]["input"];
  username: Scalars["String"]["input"];
};

export type UserOemCredentials = {
  organization?: InputMaybe<Scalars["ID"]["input"]>;
  password: Scalars["Base64"]["input"];
  signInToken?: InputMaybe<Scalars["String"]["input"]>;
  username: Scalars["String"]["input"];
};

export type ResolverTypeWrapper<T> = Promise<T> | T;

export type ResolverWithResolve<TResult, TParent, TContext, TArgs> = {
  resolve: ResolverFn<TResult, TParent, TContext, TArgs>;
};
export type Resolver<TResult, TParent = {}, TContext = {}, TArgs = {}> =
  | ResolverFn<TResult, TParent, TContext, TArgs>
  | ResolverWithResolve<TResult, TParent, TContext, TArgs>;

export type ResolverFn<TResult, TParent, TContext, TArgs> = (
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo,
) => Promise<TResult> | TResult;

export type SubscriptionSubscribeFn<TResult, TParent, TContext, TArgs> = (
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo,
) => AsyncIterable<TResult> | Promise<AsyncIterable<TResult>>;

export type SubscriptionResolveFn<TResult, TParent, TContext, TArgs> = (
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo,
) => TResult | Promise<TResult>;

export interface SubscriptionSubscriberObject<
  TResult,
  TKey extends string,
  TParent,
  TContext,
  TArgs,
> {
  subscribe: SubscriptionSubscribeFn<{ [key in TKey]: TResult }, TParent, TContext, TArgs>;
  resolve?: SubscriptionResolveFn<TResult, { [key in TKey]: TResult }, TContext, TArgs>;
}

export interface SubscriptionResolverObject<TResult, TParent, TContext, TArgs> {
  subscribe: SubscriptionSubscribeFn<any, TParent, TContext, TArgs>;
  resolve: SubscriptionResolveFn<TResult, any, TContext, TArgs>;
}

export type SubscriptionObject<TResult, TKey extends string, TParent, TContext, TArgs> =
  | SubscriptionSubscriberObject<TResult, TKey, TParent, TContext, TArgs>
  | SubscriptionResolverObject<TResult, TParent, TContext, TArgs>;

export type SubscriptionResolver<
  TResult,
  TKey extends string,
  TParent = {},
  TContext = {},
  TArgs = {},
> =
  | ((...args: any[]) => SubscriptionObject<TResult, TKey, TParent, TContext, TArgs>)
  | SubscriptionObject<TResult, TKey, TParent, TContext, TArgs>;

export type TypeResolveFn<TTypes, TParent = {}, TContext = {}> = (
  parent: TParent,
  context: TContext,
  info: GraphQLResolveInfo,
) => Maybe<TTypes> | Promise<Maybe<TTypes>>;

export type IsTypeOfResolverFn<T = {}, TContext = {}> = (
  obj: T,
  context: TContext,
  info: GraphQLResolveInfo,
) => boolean | Promise<boolean>;

export type NextResolverFn<T> = () => Promise<T>;

export type DirectiveResolverFn<TResult = {}, TParent = {}, TContext = {}, TArgs = {}> = (
  next: NextResolverFn<TResult>,
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo,
) => TResult | Promise<TResult>;

/** Mapping of union types */
export type ResolversUnionTypes<RefType extends Record<string, unknown>> = {
  AssetOrTemplate: Asset | AssetTemplate;
};

/** Mapping between all available schema types and the resolvers types */
export type ResolversTypes = {
  ActivityLog: ResolverTypeWrapper<ActivityLog>;
  AdditionalField: ResolverTypeWrapper<AdditionalField>;
  AiAssistant: ResolverTypeWrapper<AiAssistant>;
  AiAssistantChat: ResolverTypeWrapper<AiAssistantChat>;
  AiAssistantChatTurn: ResolverTypeWrapper<AiAssistantChatTurn>;
  AiAssistantConfiguration: ResolverTypeWrapper<AiAssistantConfiguration>;
  AiAssistantDocument: ResolverTypeWrapper<AiAssistantDocument>;
  AiAssistantDocuments: ResolverTypeWrapper<AiAssistantDocuments>;
  AiAssistantQueryResponse: ResolverTypeWrapper<AiAssistantQueryResponse>;
  AiAssistantQuerySearchResult: ResolverTypeWrapper<AiAssistantQuerySearchResult>;
  AiAssistantQuerySearchResultPartMetadata: ResolverTypeWrapper<AiAssistantQuerySearchResultPartMetadata>;
  AiAssistantUsage: ResolverTypeWrapper<AiAssistantUsage>;
  AiAssistantWithCount: ResolverTypeWrapper<AiAssistantWithCount>;
  AiNote: ResolverTypeWrapper<AiNote>;
  Analytics: ResolverTypeWrapper<Analytics>;
  ApiKey: ResolverTypeWrapper<ApiKey>;
  AppConfig: ResolverTypeWrapper<AppConfig>;
  AppFeaturesEnum: AppFeaturesEnum;
  Asset: ResolverTypeWrapper<Asset>;
  AssetAccess: ResolverTypeWrapper<AssetAccess>;
  AssetDetachedFromTemplate: ResolverTypeWrapper<AssetDetachedFromTemplate>;
  AssetOrTemplate: ResolverTypeWrapper<ResolversUnionTypes<ResolversTypes>["AssetOrTemplate"]>;
  AssetParent: ResolverTypeWrapper<AssetParent>;
  AssetPart: ResolverTypeWrapper<AssetPart>;
  AssetTemplate: ResolverTypeWrapper<AssetTemplate>;
  AssetTemplatePart: ResolverTypeWrapper<AssetTemplatePart>;
  AssetTemplateProcedure: ResolverTypeWrapper<AssetTemplateProcedure>;
  AssetType: ResolverTypeWrapper<AssetType>;
  AssignTicketTypeInput: AssignTicketTypeInput;
  AssignedWorkOrderReminder: ResolverTypeWrapper<AssignedWorkOrderReminder>;
  Author: ResolverTypeWrapper<Author>;
  Base64: ResolverTypeWrapper<Scalars["Base64"]["output"]>;
  BoardFor: BoardFor;
  Boards: ResolverTypeWrapper<Boards>;
  Boolean: ResolverTypeWrapper<Scalars["Boolean"]["output"]>;
  CalendarEvent: ResolverTypeWrapper<CalendarEvent>;
  CalendarSyncConfiguration: ResolverTypeWrapper<CalendarSyncConfiguration>;
  CalendarSyncUsage: ResolverTypeWrapper<CalendarSyncUsage>;
  Citation: ResolverTypeWrapper<Citation>;
  CompleteMultiPartUploadPayload: ResolverTypeWrapper<CompleteMultiPartUploadPayload>;
  CompletedMultipartUpload: CompletedMultipartUpload;
  CompletedPartList: CompletedPartList;
  ConnectionHistory: ResolverTypeWrapper<ConnectionHistory>;
  ConnectionRequest: ResolverTypeWrapper<ConnectionRequest>;
  Contact: ResolverTypeWrapper<Contact>;
  CreateMultiPartUploadPayload: ResolverTypeWrapper<CreateMultiPartUploadPayload>;
  CustomAdditionalField: ResolverTypeWrapper<CustomAdditionalField>;
  CustomField: ResolverTypeWrapper<CustomField>;
  Customer: ResolverTypeWrapper<Customer>;
  CustomerAddress: ResolverTypeWrapper<CustomerAddress>;
  CustomerMarker: ResolverTypeWrapper<CustomerMarker>;
  CustomerPortal: ResolverTypeWrapper<CustomerPortal>;
  CustomerPortalLoginResponse: ResolverTypeWrapper<CustomerPortalLoginResponse>;
  DateTime: ResolverTypeWrapper<Scalars["DateTime"]["output"]>;
  DeletedEvent: ResolverTypeWrapper<DeletedEvent>;
  Document: ResolverTypeWrapper<Document>;
  DocumentAnswers: ResolverTypeWrapper<DocumentAnswers>;
  DocumentChunk: ResolverTypeWrapper<DocumentChunk>;
  DocumentFolders: ResolverTypeWrapper<DocumentFolders>;
  DocumentLabel: ResolverTypeWrapper<DocumentLabel>;
  DocumentPage: ResolverTypeWrapper<DocumentPage>;
  DocumentTranslation: ResolverTypeWrapper<DocumentTranslation>;
  DraftJunkCount: ResolverTypeWrapper<DraftJunkCount>;
  DraftOperation: ResolverTypeWrapper<DraftOperation>;
  DraftTicket: ResolverTypeWrapper<DraftTicket>;
  DraftUnavailable: ResolverTypeWrapper<DraftUnavailable>;
  EmailAccount: ResolverTypeWrapper<EmailAccount>;
  EmailAddress: ResolverTypeWrapper<Scalars["EmailAddress"]["output"]>;
  EmailAttachment: ResolverTypeWrapper<EmailAttachment>;
  EmailDraft: ResolverTypeWrapper<EmailDraft>;
  EmailFolder: ResolverTypeWrapper<EmailFolder>;
  EmailMessage: ResolverTypeWrapper<EmailMessage>;
  EmailMessageWithFileURL: ResolverTypeWrapper<EmailMessageWithFileUrl>;
  EmailNotification: ResolverTypeWrapper<EmailNotification>;
  EmailParticipant: ResolverTypeWrapper<EmailParticipant>;
  EmailThread: ResolverTypeWrapper<EmailThread>;
  EmailThreadMessage: ResolverTypeWrapper<EmailThreadMessage>;
  EmailThreadWithTicket: ResolverTypeWrapper<EmailThreadWithTicket>;
  EnumTicketStatus: EnumTicketStatus;
  FieldAttachment: ResolverTypeWrapper<FieldAttachment>;
  FieldOption: ResolverTypeWrapper<FieldOption>;
  FieldTypes: FieldTypes;
  FileImportDataMeta: ResolverTypeWrapper<FileImportDataMeta>;
  FileImporter: ResolverTypeWrapper<FileImporter>;
  FileImporterData: ResolverTypeWrapper<FileImporterData>;
  Float: ResolverTypeWrapper<Scalars["Float"]["output"]>;
  GroupedTickets: ResolverTypeWrapper<GroupedTickets>;
  Guide: ResolverTypeWrapper<Guide>;
  HTML: ResolverTypeWrapper<Scalars["HTML"]["output"]>;
  ID: ResolverTypeWrapper<Scalars["ID"]["output"]>;
  Importer: ResolverTypeWrapper<Importer>;
  InputAddAiAssistantDocuments: InputAddAiAssistantDocuments;
  InputAddTicketAssignee: InputAddTicketAssignee;
  InputAddTicketAttachment: InputAddTicketAttachment;
  InputAddTicketAttachments: InputAddTicketAttachments;
  InputAddTicketFollower: InputAddTicketFollower;
  InputAddTicketResource: InputAddTicketResource;
  InputAddTimeTrackerLog: InputAddTimeTrackerLog;
  InputAddTimeTrackerLogPayload: InputAddTimeTrackerLogPayload;
  InputAppConfig: InputAppConfig;
  InputAssetAccess: InputAssetAccess;
  InputAssignAssetInventoryParts: InputAssignAssetInventoryParts;
  InputAssignAssetTemplateInventoryParts: InputAssignAssetTemplateInventoryParts;
  InputAssignAssetsToParent: InputAssignAssetsToParent;
  InputAssignInventoryPartsToTicket: InputAssignInventoryPartsToTicket;
  InputAssignMultipleAssetsToTeam: InputAssignMultipleAssetsToTeam;
  InputAssignMultipleCustomersToTeam: InputAssignMultipleCustomersToTeam;
  InputAssignMultipleUsersToTeam: InputAssignMultipleUsersToTeam;
  InputAssignOemImporters: InputAssignOemImporters;
  InputAssignOwnOemMultipleAssetsToOwnOemCustomer: InputAssignOwnOemMultipleAssetsToOwnOemCustomer;
  InputAssignUnassignMultipleSkillsOemUser: InputAssignUnassignMultipleSkillsOemUser;
  InputAssignedWorkOrderReminder: InputAssignedWorkOrderReminder;
  InputAttachProcedureToWorkOrder: InputAttachProcedureToWorkOrder;
  InputConfigureOemAPI: InputConfigureOemApi;
  InputConfigureOemEmail: InputConfigureOemEmail;
  InputConfigureOemPlans: InputConfigureOemPlans;
  InputCreate3DGuide: InputCreate3DGuide;
  InputCreateAiAssistantV2: InputCreateAiAssistantV2;
  InputCreateAiNote: InputCreateAiNote;
  InputCreateAssetTemplate: InputCreateAssetTemplate;
  InputCreateContact: InputCreateContact;
  InputCreateDocument: InputCreateDocument;
  InputCreateDocuments: InputCreateDocuments;
  InputCreateFacilityUser: InputCreateFacilityUser;
  InputCreateFacilityUserV2: InputCreateFacilityUserV2;
  InputCreateInventoryPart: InputCreateInventoryPart;
  InputCreateMachineHistoryNote: InputCreateMachineHistoryNote;
  InputCreateOem: InputCreateOem;
  InputCreateOemCustomer: InputCreateOemCustomer;
  InputCreateOemCustomerV2: InputCreateOemCustomerV2;
  InputCreateOemSupportAccount: InputCreateOemSupportAccount;
  InputCreateOwnOemAsset: InputCreateOwnOemAsset;
  InputCreateOwnOemAssetType: InputCreateOwnOemAssetType;
  InputCreateOwnOemReport: InputCreateOwnOemReport;
  InputCreateOwnOemResource: InputCreateOwnOemResource;
  InputCreateOwnOemSkills: InputCreateOwnOemSkills;
  InputCreateOwnOemTicket: InputCreateOwnOemTicket;
  InputCreateOwnTicket: InputCreateOwnTicket;
  InputCreatePreventiveMaintenanceEvent: InputCreatePreventiveMaintenanceEvent;
  InputCreateRequest: InputCreateRequest;
  InputCreateTeam: InputCreateTeam;
  InputCustomField: InputCustomField;
  InputCustomFieldOrder: InputCustomFieldOrder;
  InputCustomerPortalSignup: InputCustomerPortalSignup;
  InputDateRange: InputDateRange;
  InputDeleteOemTicketType: InputDeleteOemTicketType;
  InputDeleteOwnOemAssetType: InputDeleteOwnOemAssetType;
  InputDeleteOwnOemCustomAdditionalField: InputDeleteOwnOemCustomAdditionalField;
  InputDeleteOwnOemResource: InputDeleteOwnOemResource;
  InputDeleteOwnOemSkill: InputDeleteOwnOemSkill;
  InputDeleteOwnOemTicket: InputDeleteOwnOemTicket;
  InputDescriptionConfig: InputDescriptionConfig;
  InputDetachProcedureFromWorkOrder: InputDetachProcedureFromWorkOrder;
  InputDownloadWOPDF: InputDownloadWopdf;
  InputDuplicateOwnOemReport: InputDuplicateOwnOemReport;
  InputEmailInlineAttachments: InputEmailInlineAttachments;
  InputEmailNotificationSettings: InputEmailNotificationSettings;
  InputFieldAttachment: InputFieldAttachment;
  InputFieldOption: InputFieldOption;
  InputFinalizeProcedure: InputFinalizeProcedure;
  InputForwardedEmailAttachments: InputForwardedEmailAttachments;
  InputGetAnalytics: InputGetAnalytics;
  InputGetGeneralSignUpToken: InputGetGeneralSignUpToken;
  InputHandleImportUploadId: InputHandleImportUploadId;
  InputHandleOwnOemAssetQRAccess: InputHandleOwnOemAssetQrAccess;
  InputImporters: InputImporters;
  InputInsertCustomAdditionalField: InputInsertCustomAdditionalField;
  InputInsertTag: InputInsertTag;
  InputInventoryQueryParams: InputInventoryQueryParams;
  InputInviteContacts: InputInviteContacts;
  InputMachineHistoryNote: InputMachineHistoryNote;
  InputMachineHistoryNoteAttachment: InputMachineHistoryNoteAttachment;
  InputMoveEmailToFolder: InputMoveEmailToFolder;
  InputNotifyOnInternalNotePost: InputNotifyOnInternalNotePost;
  InputOemCustomField: InputOemCustomField;
  InputOemPlans: InputOemPlans;
  InputOperation: InputOperation;
  InputOption: InputOption;
  InputPMEInventoryPart: InputPmeInventoryPart;
  InputPMEProcedure: InputPmeProcedure;
  InputPartsConfig: InputPartsConfig;
  InputPlanTier: InputPlanTier;
  InputProcedureNodeValue: InputProcedureNodeValue;
  InputProcedureTemplateNode: InputProcedureTemplateNode;
  InputProcedureTemplateSignature: InputProcedureTemplateSignature;
  InputProductAccess: InputProductAccess;
  InputProductPlan: InputProductPlan;
  InputPublishAiNote: InputPublishAiNote;
  InputPublishTicketScheduleDraft: InputPublishTicketScheduleDraft;
  InputQueryAiAssistant: InputQueryAiAssistant;
  InputQueryOem: InputQueryOem;
  InputQueryParams: InputQueryParams;
  InputRemoveAiAssistantDocuments: InputRemoveAiAssistantDocuments;
  InputRemoveAssetInventoryPart: InputRemoveAssetInventoryPart;
  InputRemoveAssetTemplateInventoryPart: InputRemoveAssetTemplateInventoryPart;
  InputRemoveChannelsMemberships: InputRemoveChannelsMemberships;
  InputRemoveInventoryPartFromTicket: InputRemoveInventoryPartFromTicket;
  InputRemoveOwnOemAssetFromOwnOemCustomer: InputRemoveOwnOemAssetFromOwnOemCustomer;
  InputRemoveTicketAssignee: InputRemoveTicketAssignee;
  InputRemoveTicketAttachment: InputRemoveTicketAttachment;
  InputRemoveTicketFollower: InputRemoveTicketFollower;
  InputRemoveTicketResources: InputRemoveTicketResources;
  InputRemoveTimeTrackerLog: InputRemoveTimeTrackerLog;
  InputRenameAiAssistant: InputRenameAiAssistant;
  InputRenameAiAssistantChat: InputRenameAiAssistantChat;
  InputReportField: InputReportField;
  InputReportFilter: InputReportFilter;
  InputResetAssetToTemplate: InputResetAssetToTemplate;
  InputResultDocument: InputResultDocument;
  InputSaveProcedure: InputSaveProcedure;
  InputSaveProcedureTemplate: InputSaveProcedureTemplate;
  InputSendEmail: InputSendEmail;
  InputSignatureValue: InputSignatureValue;
  InputStatuses: InputStatuses;
  InputTableColumn: InputTableColumn;
  InputTableOption: InputTableOption;
  InputTicket: InputTicket;
  InputTicketInventoryPartPayload: InputTicketInventoryPartPayload;
  InputTicketMachinePart: InputTicketMachinePart;
  InputTicketType: InputTicketType;
  InputTicketTypeConfig: InputTicketTypeConfig;
  InputTierLimit: InputTierLimit;
  InputTranscribeAiNote: InputTranscribeAiNote;
  InputTranslateAiNoteSummary: InputTranslateAiNoteSummary;
  InputUnassignAssetFromParent: InputUnassignAssetFromParent;
  InputUnassignAssetFromTeam: InputUnassignAssetFromTeam;
  InputUnassignCustomerFromTeam: InputUnassignCustomerFromTeam;
  InputUnassignUserFromTeam: InputUnassignUserFromTeam;
  InputUnavailable: InputUnavailable;
  InputUpdate3DGuide: InputUpdate3DGuide;
  InputUpdateAiAssistant: InputUpdateAiAssistant;
  InputUpdateAiNoteSummary: InputUpdateAiNoteSummary;
  InputUpdateAiNoteTitle: InputUpdateAiNoteTitle;
  InputUpdateAssetTemplate: InputUpdateAssetTemplate;
  InputUpdateContact: InputUpdateContact;
  InputUpdateCrmOem: InputUpdateCrmOem;
  InputUpdateCustomAdditionalField: InputUpdateCustomAdditionalField;
  InputUpdateCustomFieldsOrder: InputUpdateCustomFieldsOrder;
  InputUpdateCustomerPortal: InputUpdateCustomerPortal;
  InputUpdateDescriptionTicket: InputUpdateDescriptionTicket;
  InputUpdateDocument: InputUpdateDocument;
  InputUpdateEmailThread: InputUpdateEmailThread;
  InputUpdateFacilityUser: InputUpdateFacilityUser;
  InputUpdateInventoryPart: InputUpdateInventoryPart;
  InputUpdateKnowledgeBase: InputUpdateKnowledgeBase;
  InputUpdateMachineHistoryNote: InputUpdateMachineHistoryNote;
  InputUpdateOemAiAssistantConfiguration: InputUpdateOemAiAssistantConfiguration;
  InputUpdateOemCustomer: InputUpdateOemCustomer;
  InputUpdateOemSupportAccount: InputUpdateOemSupportAccount;
  InputUpdateOemTicketType: InputUpdateOemTicketType;
  InputUpdateOwnOem: InputUpdateOwnOem;
  InputUpdateOwnOemAsset: InputUpdateOwnOemAsset;
  InputUpdateOwnOemAssetType: InputUpdateOwnOemAssetType;
  InputUpdateOwnOemReport: InputUpdateOwnOemReport;
  InputUpdateOwnOemResource: InputUpdateOwnOemResource;
  InputUpdateOwnOemSkill: InputUpdateOwnOemSkill;
  InputUpdateOwnOemTicket: InputUpdateOwnOemTicket;
  InputUpdatePersonalSettings: InputUpdatePersonalSettings;
  InputUpdatePreventiveMaintenanceEvent: InputUpdatePreventiveMaintenanceEvent;
  InputUpdateSelfFacilityUser: InputUpdateSelfFacilityUser;
  InputUpdateTag: InputUpdateTag;
  InputUpdateTeam: InputUpdateTeam;
  InputUpdateTicketScheduleDraft: InputUpdateTicketScheduleDraft;
  InputUpdateTimeTrackerLog: InputUpdateTimeTrackerLog;
  InputUpdateTitleTicket: InputUpdateTitleTicket;
  InputUploadSearchFeedback: InputUploadSearchFeedback;
  InputUsageTracking: InputUsageTracking;
  InputUserBoard: InputUserBoard;
  InputWorkOrderCreationNotifyTo: InputWorkOrderCreationNotifyTo;
  InstalledProduct: ResolverTypeWrapper<InstalledProduct>;
  Int: ResolverTypeWrapper<Scalars["Int"]["output"]>;
  IntegrationCustomersEnum: IntegrationCustomersEnum;
  InventoryPart: ResolverTypeWrapper<InventoryPart>;
  JSON: ResolverTypeWrapper<Scalars["JSON"]["output"]>;
  KanBanAssetType: ResolverTypeWrapper<KanBanAssetType>;
  KanbanColumnAsset: ResolverTypeWrapper<KanbanColumnAsset>;
  KanbanColumnCustomer: ResolverTypeWrapper<KanbanColumnCustomer>;
  KanbanColumnTicket: ResolverTypeWrapper<KanbanColumnTicket>;
  KnowledgeBase: ResolverTypeWrapper<KnowledgeBase>;
  LinkedTicket: ResolverTypeWrapper<LinkedTicket>;
  MJSON: ResolverTypeWrapper<Scalars["MJSON"]["output"]>;
  MachineHistory: ResolverTypeWrapper<MachineHistory>;
  MachineHistoryNote: ResolverTypeWrapper<MachineHistoryNote>;
  MachineHistoryNoteAttachment: ResolverTypeWrapper<MachineHistoryNoteAttachment>;
  Mixed: ResolverTypeWrapper<Scalars["Mixed"]["output"]>;
  Mutation: ResolverTypeWrapper<{}>;
  NodeAndFieldTypes: NodeAndFieldTypes;
  NonNegativeFloat: ResolverTypeWrapper<Scalars["NonNegativeFloat"]["output"]>;
  Notification: ResolverTypeWrapper<Notification>;
  NotificationUser: ResolverTypeWrapper<NotificationUser>;
  Oem: ResolverTypeWrapper<Oem>;
  OemAPI: ResolverTypeWrapper<OemApi>;
  OemAdmin: ResolverTypeWrapper<OemAdmin>;
  OemPlan: ResolverTypeWrapper<OemPlan>;
  OptionResponse: ResolverTypeWrapper<OptionResponse>;
  PMEAssetPart: ResolverTypeWrapper<PmeAssetPart>;
  PMEProcedure: ResolverTypeWrapper<PmeProcedure>;
  PaginatedActivityLogs: ResolverTypeWrapper<PaginatedActivityLogs>;
  PaginatedAiAssistantChats: ResolverTypeWrapper<PaginatedAiAssistantChats>;
  PaginatedAiNotes: ResolverTypeWrapper<PaginatedAiNotes>;
  PaginatedAssets: ResolverTypeWrapper<PaginatedAssets>;
  PaginatedAssistants: ResolverTypeWrapper<PaginatedAssistants>;
  PaginatedConnectionHistory: ResolverTypeWrapper<PaginatedConnectionHistory>;
  PaginatedContacts: ResolverTypeWrapper<PaginatedContacts>;
  PaginatedCustomerPortals: ResolverTypeWrapper<PaginatedCustomerPortals>;
  PaginatedCustomers: ResolverTypeWrapper<PaginatedCustomers>;
  PaginatedDocuments: ResolverTypeWrapper<PaginatedDocuments>;
  PaginatedEmailContacts: ResolverTypeWrapper<PaginatedEmailContacts>;
  PaginatedEmailDrafts: ResolverTypeWrapper<PaginatedEmailDrafts>;
  PaginatedEmailFolders: ResolverTypeWrapper<PaginatedEmailFolders>;
  PaginatedEmailThreads: ResolverTypeWrapper<PaginatedEmailThreads>;
  PaginatedGuide: ResolverTypeWrapper<PaginatedGuide>;
  PaginatedInventoryParts: ResolverTypeWrapper<PaginatedInventoryParts>;
  PaginatedKanbanAssets: ResolverTypeWrapper<PaginatedKanbanAssets>;
  PaginatedKanbanCustomers: ResolverTypeWrapper<PaginatedKanbanCustomers>;
  PaginatedKanbanTickets: ResolverTypeWrapper<PaginatedKanbanTickets>;
  PaginatedMachineHistory: ResolverTypeWrapper<PaginatedMachineHistory>;
  PaginatedModels: ResolverTypeWrapper<
    Omit<PaginatedModels, "models"> & {
      models?: Maybe<Array<Maybe<ResolversTypes["AssetOrTemplate"]>>>;
    }
  >;
  PaginatedPreventiveMaintenanceEvents: ResolverTypeWrapper<PaginatedPreventiveMaintenanceEvents>;
  PaginatedReports: ResolverTypeWrapper<PaginatedReports>;
  PaginatedRequests: ResolverTypeWrapper<PaginatedRequests>;
  PaginatedResources: ResolverTypeWrapper<PaginatedResources>;
  PaginatedSharedAssetTemplates: ResolverTypeWrapper<PaginatedSharedAssetTemplates>;
  PaginatedSharedAssetsOem: ResolverTypeWrapper<PaginatedSharedAssetsOem>;
  PaginatedTeams: ResolverTypeWrapper<PaginatedTeams>;
  PaginatedTickets: ResolverTypeWrapper<PaginatedTickets>;
  PaginatedUserSchedules: ResolverTypeWrapper<PaginatedUserSchedules>;
  PaginatedUsers: ResolverTypeWrapper<PaginatedUsers>;
  PaidFeaturesEnum: PaidFeaturesEnum;
  PlanTier: ResolverTypeWrapper<PlanTier>;
  PreventiveMaintenance: ResolverTypeWrapper<PreventiveMaintenance>;
  Procedure: ResolverTypeWrapper<Procedure>;
  ProcedureNode: ResolverTypeWrapper<ProcedureNode>;
  ProcedureSignature: ResolverTypeWrapper<ProcedureSignature>;
  ProcedureStates: ProcedureStates;
  ProcedureTemplate: ResolverTypeWrapper<ProcedureTemplate>;
  ProcedureTemplateList: ResolverTypeWrapper<ProcedureTemplateList>;
  ProcedureTemplateNode: ResolverTypeWrapper<ProcedureTemplateNode>;
  ProcedureTemplateSignature: ResolverTypeWrapper<ProcedureTemplateSignature>;
  ProductAccess: ResolverTypeWrapper<ProductAccess>;
  ProductPlan: ResolverTypeWrapper<ProductPlan>;
  PublishedMachine: ResolverTypeWrapper<PublishedMachine>;
  Query: ResolverTypeWrapper<{}>;
  QueryResponse: ResolverTypeWrapper<QueryResponse>;
  RemoveTicketTypeInput: RemoveTicketTypeInput;
  Report: ResolverTypeWrapper<Report>;
  ReportField: ResolverTypeWrapper<ReportField>;
  ReportFilter: ResolverTypeWrapper<ReportFilter>;
  ReportOptionFilter: ResolverTypeWrapper<ReportOptionFilter>;
  ReportOptions: ResolverTypeWrapper<ReportOptions>;
  Resource: ResolverTypeWrapper<Resource>;
  S3Payload: ResolverTypeWrapper<S3Payload>;
  SafeString: ResolverTypeWrapper<Scalars["SafeString"]["output"]>;
  Schedule: ResolverTypeWrapper<Schedule>;
  ScheduleInputType: ScheduleInputType;
  SharedOrganizationDetails: ResolverTypeWrapper<SharedOrganizationDetails>;
  SignupTokenVerification: ResolverTypeWrapper<SignupTokenVerification>;
  Skill: ResolverTypeWrapper<Skill>;
  Status: ResolverTypeWrapper<Status>;
  Statuses: ResolverTypeWrapper<Statuses>;
  String: ResolverTypeWrapper<Scalars["String"]["output"]>;
  TableColumn: ResolverTypeWrapper<TableColumn>;
  TableOption: ResolverTypeWrapper<TableOption>;
  Team: ResolverTypeWrapper<Team>;
  TemplateImporter: ResolverTypeWrapper<TemplateImporter>;
  Ticket: ResolverTypeWrapper<Ticket>;
  TicketAttachment: ResolverTypeWrapper<TicketAttachment>;
  TicketMachinePart: ResolverTypeWrapper<TicketMachinePart>;
  TicketMentionUsers: ResolverTypeWrapper<TicketMentionUsers>;
  TicketProcedure: ResolverTypeWrapper<TicketProcedure>;
  TicketScheduleDraft: ResolverTypeWrapper<TicketScheduleDraft>;
  TicketType: ResolverTypeWrapper<TicketType>;
  TicketTypeCustomField: ResolverTypeWrapper<TicketTypeCustomField>;
  TicketTypeDescriptionConfig: ResolverTypeWrapper<TicketTypeDescriptionConfig>;
  TicketTypePartsConfig: ResolverTypeWrapper<TicketTypePartsConfig>;
  TierLimit: ResolverTypeWrapper<TierLimit>;
  TimeTracker: ResolverTypeWrapper<TimeTracker>;
  TimeTrackerLog: ResolverTypeWrapper<TimeTrackerLog>;
  TranscriptionError: ResolverTypeWrapper<TranscriptionError>;
  TranscriptionResult: ResolverTypeWrapper<TranscriptionResult>;
  Types: Types;
  UpdateOemAiAssistantConfiguration: UpdateOemAiAssistantConfiguration;
  UpdateOemCalendarSyncConfiguration: UpdateOemCalendarSyncConfiguration;
  UsageTracking: ResolverTypeWrapper<UsageTracking>;
  User: ResolverTypeWrapper<User>;
  UserAiAssistantUsage: ResolverTypeWrapper<UserAiAssistantUsage>;
  UserSchedule: ResolverTypeWrapper<UserSchedule>;
  VerifyGeneralSignUpToken: ResolverTypeWrapper<VerifyGeneralSignUpToken>;
  VisibilityScope: VisibilityScope;
  enumRoles: EnumRoles;
  qrcMachineCredentials: QrcMachineCredentials;
  userCredentials: UserCredentials;
  userFacilityCredentials: UserFacilityCredentials;
  userOemCredentials: UserOemCredentials;
};

/** Mapping between all available schema types and the resolvers parents */
export type ResolversParentTypes = {
  ActivityLog: ActivityLog;
  AdditionalField: AdditionalField;
  AiAssistant: AiAssistant;
  AiAssistantChat: AiAssistantChat;
  AiAssistantChatTurn: AiAssistantChatTurn;
  AiAssistantConfiguration: AiAssistantConfiguration;
  AiAssistantDocument: AiAssistantDocument;
  AiAssistantDocuments: AiAssistantDocuments;
  AiAssistantQueryResponse: AiAssistantQueryResponse;
  AiAssistantQuerySearchResult: AiAssistantQuerySearchResult;
  AiAssistantQuerySearchResultPartMetadata: AiAssistantQuerySearchResultPartMetadata;
  AiAssistantUsage: AiAssistantUsage;
  AiAssistantWithCount: AiAssistantWithCount;
  AiNote: AiNote;
  Analytics: Analytics;
  ApiKey: ApiKey;
  AppConfig: AppConfig;
  Asset: Asset;
  AssetAccess: AssetAccess;
  AssetDetachedFromTemplate: AssetDetachedFromTemplate;
  AssetOrTemplate: ResolversUnionTypes<ResolversParentTypes>["AssetOrTemplate"];
  AssetParent: AssetParent;
  AssetPart: AssetPart;
  AssetTemplate: AssetTemplate;
  AssetTemplatePart: AssetTemplatePart;
  AssetTemplateProcedure: AssetTemplateProcedure;
  AssetType: AssetType;
  AssignTicketTypeInput: AssignTicketTypeInput;
  AssignedWorkOrderReminder: AssignedWorkOrderReminder;
  Author: Author;
  Base64: Scalars["Base64"]["output"];
  Boards: Boards;
  Boolean: Scalars["Boolean"]["output"];
  CalendarEvent: CalendarEvent;
  CalendarSyncConfiguration: CalendarSyncConfiguration;
  CalendarSyncUsage: CalendarSyncUsage;
  Citation: Citation;
  CompleteMultiPartUploadPayload: CompleteMultiPartUploadPayload;
  CompletedMultipartUpload: CompletedMultipartUpload;
  CompletedPartList: CompletedPartList;
  ConnectionHistory: ConnectionHistory;
  ConnectionRequest: ConnectionRequest;
  Contact: Contact;
  CreateMultiPartUploadPayload: CreateMultiPartUploadPayload;
  CustomAdditionalField: CustomAdditionalField;
  CustomField: CustomField;
  Customer: Customer;
  CustomerAddress: CustomerAddress;
  CustomerMarker: CustomerMarker;
  CustomerPortal: CustomerPortal;
  CustomerPortalLoginResponse: CustomerPortalLoginResponse;
  DateTime: Scalars["DateTime"]["output"];
  DeletedEvent: DeletedEvent;
  Document: Document;
  DocumentAnswers: DocumentAnswers;
  DocumentChunk: DocumentChunk;
  DocumentFolders: DocumentFolders;
  DocumentLabel: DocumentLabel;
  DocumentPage: DocumentPage;
  DocumentTranslation: DocumentTranslation;
  DraftJunkCount: DraftJunkCount;
  DraftOperation: DraftOperation;
  DraftTicket: DraftTicket;
  DraftUnavailable: DraftUnavailable;
  EmailAccount: EmailAccount;
  EmailAddress: Scalars["EmailAddress"]["output"];
  EmailAttachment: EmailAttachment;
  EmailDraft: EmailDraft;
  EmailFolder: EmailFolder;
  EmailMessage: EmailMessage;
  EmailMessageWithFileURL: EmailMessageWithFileUrl;
  EmailNotification: EmailNotification;
  EmailParticipant: EmailParticipant;
  EmailThread: EmailThread;
  EmailThreadMessage: EmailThreadMessage;
  EmailThreadWithTicket: EmailThreadWithTicket;
  FieldAttachment: FieldAttachment;
  FieldOption: FieldOption;
  FileImportDataMeta: FileImportDataMeta;
  FileImporter: FileImporter;
  FileImporterData: FileImporterData;
  Float: Scalars["Float"]["output"];
  GroupedTickets: GroupedTickets;
  Guide: Guide;
  HTML: Scalars["HTML"]["output"];
  ID: Scalars["ID"]["output"];
  Importer: Importer;
  InputAddAiAssistantDocuments: InputAddAiAssistantDocuments;
  InputAddTicketAssignee: InputAddTicketAssignee;
  InputAddTicketAttachment: InputAddTicketAttachment;
  InputAddTicketAttachments: InputAddTicketAttachments;
  InputAddTicketFollower: InputAddTicketFollower;
  InputAddTicketResource: InputAddTicketResource;
  InputAddTimeTrackerLog: InputAddTimeTrackerLog;
  InputAddTimeTrackerLogPayload: InputAddTimeTrackerLogPayload;
  InputAppConfig: InputAppConfig;
  InputAssetAccess: InputAssetAccess;
  InputAssignAssetInventoryParts: InputAssignAssetInventoryParts;
  InputAssignAssetTemplateInventoryParts: InputAssignAssetTemplateInventoryParts;
  InputAssignAssetsToParent: InputAssignAssetsToParent;
  InputAssignInventoryPartsToTicket: InputAssignInventoryPartsToTicket;
  InputAssignMultipleAssetsToTeam: InputAssignMultipleAssetsToTeam;
  InputAssignMultipleCustomersToTeam: InputAssignMultipleCustomersToTeam;
  InputAssignMultipleUsersToTeam: InputAssignMultipleUsersToTeam;
  InputAssignOemImporters: InputAssignOemImporters;
  InputAssignOwnOemMultipleAssetsToOwnOemCustomer: InputAssignOwnOemMultipleAssetsToOwnOemCustomer;
  InputAssignUnassignMultipleSkillsOemUser: InputAssignUnassignMultipleSkillsOemUser;
  InputAssignedWorkOrderReminder: InputAssignedWorkOrderReminder;
  InputAttachProcedureToWorkOrder: InputAttachProcedureToWorkOrder;
  InputConfigureOemAPI: InputConfigureOemApi;
  InputConfigureOemEmail: InputConfigureOemEmail;
  InputConfigureOemPlans: InputConfigureOemPlans;
  InputCreate3DGuide: InputCreate3DGuide;
  InputCreateAiAssistantV2: InputCreateAiAssistantV2;
  InputCreateAiNote: InputCreateAiNote;
  InputCreateAssetTemplate: InputCreateAssetTemplate;
  InputCreateContact: InputCreateContact;
  InputCreateDocument: InputCreateDocument;
  InputCreateDocuments: InputCreateDocuments;
  InputCreateFacilityUser: InputCreateFacilityUser;
  InputCreateFacilityUserV2: InputCreateFacilityUserV2;
  InputCreateInventoryPart: InputCreateInventoryPart;
  InputCreateMachineHistoryNote: InputCreateMachineHistoryNote;
  InputCreateOem: InputCreateOem;
  InputCreateOemCustomer: InputCreateOemCustomer;
  InputCreateOemCustomerV2: InputCreateOemCustomerV2;
  InputCreateOemSupportAccount: InputCreateOemSupportAccount;
  InputCreateOwnOemAsset: InputCreateOwnOemAsset;
  InputCreateOwnOemAssetType: InputCreateOwnOemAssetType;
  InputCreateOwnOemReport: InputCreateOwnOemReport;
  InputCreateOwnOemResource: InputCreateOwnOemResource;
  InputCreateOwnOemSkills: InputCreateOwnOemSkills;
  InputCreateOwnOemTicket: InputCreateOwnOemTicket;
  InputCreateOwnTicket: InputCreateOwnTicket;
  InputCreatePreventiveMaintenanceEvent: InputCreatePreventiveMaintenanceEvent;
  InputCreateRequest: InputCreateRequest;
  InputCreateTeam: InputCreateTeam;
  InputCustomField: InputCustomField;
  InputCustomFieldOrder: InputCustomFieldOrder;
  InputCustomerPortalSignup: InputCustomerPortalSignup;
  InputDateRange: InputDateRange;
  InputDeleteOemTicketType: InputDeleteOemTicketType;
  InputDeleteOwnOemAssetType: InputDeleteOwnOemAssetType;
  InputDeleteOwnOemCustomAdditionalField: InputDeleteOwnOemCustomAdditionalField;
  InputDeleteOwnOemResource: InputDeleteOwnOemResource;
  InputDeleteOwnOemSkill: InputDeleteOwnOemSkill;
  InputDeleteOwnOemTicket: InputDeleteOwnOemTicket;
  InputDescriptionConfig: InputDescriptionConfig;
  InputDetachProcedureFromWorkOrder: InputDetachProcedureFromWorkOrder;
  InputDownloadWOPDF: InputDownloadWopdf;
  InputDuplicateOwnOemReport: InputDuplicateOwnOemReport;
  InputEmailInlineAttachments: InputEmailInlineAttachments;
  InputEmailNotificationSettings: InputEmailNotificationSettings;
  InputFieldAttachment: InputFieldAttachment;
  InputFieldOption: InputFieldOption;
  InputFinalizeProcedure: InputFinalizeProcedure;
  InputForwardedEmailAttachments: InputForwardedEmailAttachments;
  InputGetAnalytics: InputGetAnalytics;
  InputGetGeneralSignUpToken: InputGetGeneralSignUpToken;
  InputHandleImportUploadId: InputHandleImportUploadId;
  InputHandleOwnOemAssetQRAccess: InputHandleOwnOemAssetQrAccess;
  InputImporters: InputImporters;
  InputInsertCustomAdditionalField: InputInsertCustomAdditionalField;
  InputInsertTag: InputInsertTag;
  InputInventoryQueryParams: InputInventoryQueryParams;
  InputInviteContacts: InputInviteContacts;
  InputMachineHistoryNote: InputMachineHistoryNote;
  InputMachineHistoryNoteAttachment: InputMachineHistoryNoteAttachment;
  InputMoveEmailToFolder: InputMoveEmailToFolder;
  InputNotifyOnInternalNotePost: InputNotifyOnInternalNotePost;
  InputOemCustomField: InputOemCustomField;
  InputOemPlans: InputOemPlans;
  InputOperation: InputOperation;
  InputOption: InputOption;
  InputPMEInventoryPart: InputPmeInventoryPart;
  InputPMEProcedure: InputPmeProcedure;
  InputPartsConfig: InputPartsConfig;
  InputPlanTier: InputPlanTier;
  InputProcedureNodeValue: InputProcedureNodeValue;
  InputProcedureTemplateNode: InputProcedureTemplateNode;
  InputProcedureTemplateSignature: InputProcedureTemplateSignature;
  InputProductAccess: InputProductAccess;
  InputProductPlan: InputProductPlan;
  InputPublishAiNote: InputPublishAiNote;
  InputPublishTicketScheduleDraft: InputPublishTicketScheduleDraft;
  InputQueryAiAssistant: InputQueryAiAssistant;
  InputQueryOem: InputQueryOem;
  InputQueryParams: InputQueryParams;
  InputRemoveAiAssistantDocuments: InputRemoveAiAssistantDocuments;
  InputRemoveAssetInventoryPart: InputRemoveAssetInventoryPart;
  InputRemoveAssetTemplateInventoryPart: InputRemoveAssetTemplateInventoryPart;
  InputRemoveChannelsMemberships: InputRemoveChannelsMemberships;
  InputRemoveInventoryPartFromTicket: InputRemoveInventoryPartFromTicket;
  InputRemoveOwnOemAssetFromOwnOemCustomer: InputRemoveOwnOemAssetFromOwnOemCustomer;
  InputRemoveTicketAssignee: InputRemoveTicketAssignee;
  InputRemoveTicketAttachment: InputRemoveTicketAttachment;
  InputRemoveTicketFollower: InputRemoveTicketFollower;
  InputRemoveTicketResources: InputRemoveTicketResources;
  InputRemoveTimeTrackerLog: InputRemoveTimeTrackerLog;
  InputRenameAiAssistant: InputRenameAiAssistant;
  InputRenameAiAssistantChat: InputRenameAiAssistantChat;
  InputReportField: InputReportField;
  InputReportFilter: InputReportFilter;
  InputResetAssetToTemplate: InputResetAssetToTemplate;
  InputResultDocument: InputResultDocument;
  InputSaveProcedure: InputSaveProcedure;
  InputSaveProcedureTemplate: InputSaveProcedureTemplate;
  InputSendEmail: InputSendEmail;
  InputSignatureValue: InputSignatureValue;
  InputStatuses: InputStatuses;
  InputTableColumn: InputTableColumn;
  InputTableOption: InputTableOption;
  InputTicket: InputTicket;
  InputTicketInventoryPartPayload: InputTicketInventoryPartPayload;
  InputTicketMachinePart: InputTicketMachinePart;
  InputTicketType: InputTicketType;
  InputTicketTypeConfig: InputTicketTypeConfig;
  InputTierLimit: InputTierLimit;
  InputTranscribeAiNote: InputTranscribeAiNote;
  InputTranslateAiNoteSummary: InputTranslateAiNoteSummary;
  InputUnassignAssetFromParent: InputUnassignAssetFromParent;
  InputUnassignAssetFromTeam: InputUnassignAssetFromTeam;
  InputUnassignCustomerFromTeam: InputUnassignCustomerFromTeam;
  InputUnassignUserFromTeam: InputUnassignUserFromTeam;
  InputUnavailable: InputUnavailable;
  InputUpdate3DGuide: InputUpdate3DGuide;
  InputUpdateAiAssistant: InputUpdateAiAssistant;
  InputUpdateAiNoteSummary: InputUpdateAiNoteSummary;
  InputUpdateAiNoteTitle: InputUpdateAiNoteTitle;
  InputUpdateAssetTemplate: InputUpdateAssetTemplate;
  InputUpdateContact: InputUpdateContact;
  InputUpdateCrmOem: InputUpdateCrmOem;
  InputUpdateCustomAdditionalField: InputUpdateCustomAdditionalField;
  InputUpdateCustomFieldsOrder: InputUpdateCustomFieldsOrder;
  InputUpdateCustomerPortal: InputUpdateCustomerPortal;
  InputUpdateDescriptionTicket: InputUpdateDescriptionTicket;
  InputUpdateDocument: InputUpdateDocument;
  InputUpdateEmailThread: InputUpdateEmailThread;
  InputUpdateFacilityUser: InputUpdateFacilityUser;
  InputUpdateInventoryPart: InputUpdateInventoryPart;
  InputUpdateKnowledgeBase: InputUpdateKnowledgeBase;
  InputUpdateMachineHistoryNote: InputUpdateMachineHistoryNote;
  InputUpdateOemAiAssistantConfiguration: InputUpdateOemAiAssistantConfiguration;
  InputUpdateOemCustomer: InputUpdateOemCustomer;
  InputUpdateOemSupportAccount: InputUpdateOemSupportAccount;
  InputUpdateOemTicketType: InputUpdateOemTicketType;
  InputUpdateOwnOem: InputUpdateOwnOem;
  InputUpdateOwnOemAsset: InputUpdateOwnOemAsset;
  InputUpdateOwnOemAssetType: InputUpdateOwnOemAssetType;
  InputUpdateOwnOemReport: InputUpdateOwnOemReport;
  InputUpdateOwnOemResource: InputUpdateOwnOemResource;
  InputUpdateOwnOemSkill: InputUpdateOwnOemSkill;
  InputUpdateOwnOemTicket: InputUpdateOwnOemTicket;
  InputUpdatePersonalSettings: InputUpdatePersonalSettings;
  InputUpdatePreventiveMaintenanceEvent: InputUpdatePreventiveMaintenanceEvent;
  InputUpdateSelfFacilityUser: InputUpdateSelfFacilityUser;
  InputUpdateTag: InputUpdateTag;
  InputUpdateTeam: InputUpdateTeam;
  InputUpdateTicketScheduleDraft: InputUpdateTicketScheduleDraft;
  InputUpdateTimeTrackerLog: InputUpdateTimeTrackerLog;
  InputUpdateTitleTicket: InputUpdateTitleTicket;
  InputUploadSearchFeedback: InputUploadSearchFeedback;
  InputUsageTracking: InputUsageTracking;
  InputUserBoard: InputUserBoard;
  InputWorkOrderCreationNotifyTo: InputWorkOrderCreationNotifyTo;
  InstalledProduct: InstalledProduct;
  Int: Scalars["Int"]["output"];
  InventoryPart: InventoryPart;
  JSON: Scalars["JSON"]["output"];
  KanBanAssetType: KanBanAssetType;
  KanbanColumnAsset: KanbanColumnAsset;
  KanbanColumnCustomer: KanbanColumnCustomer;
  KanbanColumnTicket: KanbanColumnTicket;
  KnowledgeBase: KnowledgeBase;
  LinkedTicket: LinkedTicket;
  MJSON: Scalars["MJSON"]["output"];
  MachineHistory: MachineHistory;
  MachineHistoryNote: MachineHistoryNote;
  MachineHistoryNoteAttachment: MachineHistoryNoteAttachment;
  Mixed: Scalars["Mixed"]["output"];
  Mutation: {};
  NonNegativeFloat: Scalars["NonNegativeFloat"]["output"];
  Notification: Notification;
  NotificationUser: NotificationUser;
  Oem: Oem;
  OemAPI: OemApi;
  OemAdmin: OemAdmin;
  OemPlan: OemPlan;
  OptionResponse: OptionResponse;
  PMEAssetPart: PmeAssetPart;
  PMEProcedure: PmeProcedure;
  PaginatedActivityLogs: PaginatedActivityLogs;
  PaginatedAiAssistantChats: PaginatedAiAssistantChats;
  PaginatedAiNotes: PaginatedAiNotes;
  PaginatedAssets: PaginatedAssets;
  PaginatedAssistants: PaginatedAssistants;
  PaginatedConnectionHistory: PaginatedConnectionHistory;
  PaginatedContacts: PaginatedContacts;
  PaginatedCustomerPortals: PaginatedCustomerPortals;
  PaginatedCustomers: PaginatedCustomers;
  PaginatedDocuments: PaginatedDocuments;
  PaginatedEmailContacts: PaginatedEmailContacts;
  PaginatedEmailDrafts: PaginatedEmailDrafts;
  PaginatedEmailFolders: PaginatedEmailFolders;
  PaginatedEmailThreads: PaginatedEmailThreads;
  PaginatedGuide: PaginatedGuide;
  PaginatedInventoryParts: PaginatedInventoryParts;
  PaginatedKanbanAssets: PaginatedKanbanAssets;
  PaginatedKanbanCustomers: PaginatedKanbanCustomers;
  PaginatedKanbanTickets: PaginatedKanbanTickets;
  PaginatedMachineHistory: PaginatedMachineHistory;
  PaginatedModels: Omit<PaginatedModels, "models"> & {
    models?: Maybe<Array<Maybe<ResolversParentTypes["AssetOrTemplate"]>>>;
  };
  PaginatedPreventiveMaintenanceEvents: PaginatedPreventiveMaintenanceEvents;
  PaginatedReports: PaginatedReports;
  PaginatedRequests: PaginatedRequests;
  PaginatedResources: PaginatedResources;
  PaginatedSharedAssetTemplates: PaginatedSharedAssetTemplates;
  PaginatedSharedAssetsOem: PaginatedSharedAssetsOem;
  PaginatedTeams: PaginatedTeams;
  PaginatedTickets: PaginatedTickets;
  PaginatedUserSchedules: PaginatedUserSchedules;
  PaginatedUsers: PaginatedUsers;
  PlanTier: PlanTier;
  PreventiveMaintenance: PreventiveMaintenance;
  Procedure: Procedure;
  ProcedureNode: ProcedureNode;
  ProcedureSignature: ProcedureSignature;
  ProcedureTemplate: ProcedureTemplate;
  ProcedureTemplateList: ProcedureTemplateList;
  ProcedureTemplateNode: ProcedureTemplateNode;
  ProcedureTemplateSignature: ProcedureTemplateSignature;
  ProductAccess: ProductAccess;
  ProductPlan: ProductPlan;
  PublishedMachine: PublishedMachine;
  Query: {};
  QueryResponse: QueryResponse;
  RemoveTicketTypeInput: RemoveTicketTypeInput;
  Report: Report;
  ReportField: ReportField;
  ReportFilter: ReportFilter;
  ReportOptionFilter: ReportOptionFilter;
  ReportOptions: ReportOptions;
  Resource: Resource;
  S3Payload: S3Payload;
  SafeString: Scalars["SafeString"]["output"];
  Schedule: Schedule;
  ScheduleInputType: ScheduleInputType;
  SharedOrganizationDetails: SharedOrganizationDetails;
  SignupTokenVerification: SignupTokenVerification;
  Skill: Skill;
  Status: Status;
  Statuses: Statuses;
  String: Scalars["String"]["output"];
  TableColumn: TableColumn;
  TableOption: TableOption;
  Team: Team;
  TemplateImporter: TemplateImporter;
  Ticket: Ticket;
  TicketAttachment: TicketAttachment;
  TicketMachinePart: TicketMachinePart;
  TicketMentionUsers: TicketMentionUsers;
  TicketProcedure: TicketProcedure;
  TicketScheduleDraft: TicketScheduleDraft;
  TicketType: TicketType;
  TicketTypeCustomField: TicketTypeCustomField;
  TicketTypeDescriptionConfig: TicketTypeDescriptionConfig;
  TicketTypePartsConfig: TicketTypePartsConfig;
  TierLimit: TierLimit;
  TimeTracker: TimeTracker;
  TimeTrackerLog: TimeTrackerLog;
  TranscriptionError: TranscriptionError;
  TranscriptionResult: TranscriptionResult;
  UpdateOemAiAssistantConfiguration: UpdateOemAiAssistantConfiguration;
  UpdateOemCalendarSyncConfiguration: UpdateOemCalendarSyncConfiguration;
  UsageTracking: UsageTracking;
  User: User;
  UserAiAssistantUsage: UserAiAssistantUsage;
  UserSchedule: UserSchedule;
  VerifyGeneralSignUpToken: VerifyGeneralSignUpToken;
  qrcMachineCredentials: QrcMachineCredentials;
  userCredentials: UserCredentials;
  userFacilityCredentials: UserFacilityCredentials;
  userOemCredentials: UserOemCredentials;
};

export type ConstraintDirectiveArgs = {
  contains?: Maybe<Scalars["String"]["input"]>;
  endsWith?: Maybe<Scalars["String"]["input"]>;
  exclusiveMax?: Maybe<Scalars["Float"]["input"]>;
  exclusiveMin?: Maybe<Scalars["Float"]["input"]>;
  format?: Maybe<Scalars["String"]["input"]>;
  max?: Maybe<Scalars["Float"]["input"]>;
  maxLength?: Maybe<Scalars["Int"]["input"]>;
  min?: Maybe<Scalars["Float"]["input"]>;
  minLength?: Maybe<Scalars["Int"]["input"]>;
  multipleOf?: Maybe<Scalars["Float"]["input"]>;
  notContains?: Maybe<Scalars["String"]["input"]>;
  pattern?: Maybe<Scalars["String"]["input"]>;
  startsWith?: Maybe<Scalars["String"]["input"]>;
  uniqueTypeName?: Maybe<Scalars["String"]["input"]>;
};

export type ConstraintDirectiveResolver<
  Result,
  Parent,
  ContextType = any,
  Args = ConstraintDirectiveArgs,
> = DirectiveResolverFn<Result, Parent, ContextType, Args>;

export type CostDirectiveArgs = {
  complexity?: Maybe<Scalars["Int"]["input"]>;
  multipliers?: Maybe<Array<Maybe<Scalars["String"]["input"]>>>;
  useMultipliers?: Maybe<Scalars["Boolean"]["input"]>;
};

export type CostDirectiveResolver<
  Result,
  Parent,
  ContextType = any,
  Args = CostDirectiveArgs,
> = DirectiveResolverFn<Result, Parent, ContextType, Args>;

export type HasRoleDirectiveArgs = {
  role?: Maybe<Array<Maybe<Scalars["String"]["input"]>>>;
};

export type HasRoleDirectiveResolver<
  Result,
  Parent,
  ContextType = any,
  Args = HasRoleDirectiveArgs,
> = DirectiveResolverFn<Result, Parent, ContextType, Args>;

export type OemOwnsDirectiveArgs = {
  paidFeature?: Maybe<Array<Maybe<Scalars["String"]["input"]>>>;
};

export type OemOwnsDirectiveResolver<
  Result,
  Parent,
  ContextType = any,
  Args = OemOwnsDirectiveArgs,
> = DirectiveResolverFn<Result, Parent, ContextType, Args>;

export type ActivityLogResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["ActivityLog"] = ResolversParentTypes["ActivityLog"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  action?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  actor?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  attribute?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  createdAt?: Resolver<Maybe<ResolversTypes["DateTime"]>, ParentType, ContextType>;
  meta?: Resolver<Maybe<ResolversTypes["MJSON"]>, ParentType, ContextType>;
  resource?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  resourceId?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type AdditionalFieldResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["AdditionalField"] = ResolversParentTypes["AdditionalField"],
> = {
  _id?: Resolver<ResolversTypes["ID"], ParentType, ContextType>;
  createdBy?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  created_at?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  enabled?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  fieldType?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  isAdditionalField?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  label?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  options?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["OptionResponse"]>>>,
    ParentType,
    ContextType
  >;
  slug?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  type?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  updated_at?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  visibilityScope?: Resolver<Maybe<ResolversTypes["VisibilityScope"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type AiAssistantResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["AiAssistant"] = ResolversParentTypes["AiAssistant"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  assistantName?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  assistantType?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  boxAccessToken?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  createdBy?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  description?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  documents?: Resolver<Maybe<ResolversTypes["AiAssistantDocuments"]>, ParentType, ContextType>;
  isSharedAssistant?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  machine?: Resolver<Maybe<ResolversTypes["Asset"]>, ParentType, ContextType>;
  machineID?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  oem?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  oemDetails?: Resolver<Maybe<ResolversTypes["Oem"]>, ParentType, ContextType>;
  status?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  template?: Resolver<Maybe<ResolversTypes["AssetTemplate"]>, ParentType, ContextType>;
  templateId?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type AiAssistantChatResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["AiAssistantChat"] = ResolversParentTypes["AiAssistantChat"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  aiAssistant?: Resolver<Maybe<ResolversTypes["AiAssistant"]>, ParentType, ContextType>;
  chatId?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  createdAt?: Resolver<Maybe<ResolversTypes["DateTime"]>, ParentType, ContextType>;
  createdBy?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  isSharedAssistantChat?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  oem?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  title?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  turns?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["AiAssistantChatTurn"]>>>,
    ParentType,
    ContextType
  >;
  updatedBy?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type AiAssistantChatTurnResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["AiAssistantChatTurn"] = ResolversParentTypes["AiAssistantChatTurn"],
> = {
  answer?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  id?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  query?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type AiAssistantConfigurationResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["AiAssistantConfiguration"] = ResolversParentTypes["AiAssistantConfiguration"],
> = {
  allowedExternalQueries?: Resolver<Maybe<ResolversTypes["Float"]>, ParentType, ContextType>;
  allowedOcrScans?: Resolver<Maybe<ResolversTypes["Float"]>, ParentType, ContextType>;
  allowedQueries?: Resolver<Maybe<ResolversTypes["Float"]>, ParentType, ContextType>;
  allowedRecordingSeconds?: Resolver<Maybe<ResolversTypes["Float"]>, ParentType, ContextType>;
  allowedStorage?: Resolver<Maybe<ResolversTypes["Float"]>, ParentType, ContextType>;
  allowedUsers?: Resolver<Maybe<ResolversTypes["Float"]>, ParentType, ContextType>;
  autoApproveOCR?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  consumedOcrScans?: Resolver<Maybe<ResolversTypes["Float"]>, ParentType, ContextType>;
  consumedQueries?: Resolver<Maybe<ResolversTypes["Float"]>, ParentType, ContextType>;
  consumedRecordingSeconds?: Resolver<Maybe<ResolversTypes["Float"]>, ParentType, ContextType>;
  consumedStorage?: Resolver<Maybe<ResolversTypes["Float"]>, ParentType, ContextType>;
  consumedUsers?: Resolver<ResolversTypes["Float"], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type AiAssistantDocumentResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["AiAssistantDocument"] = ResolversParentTypes["AiAssistantDocument"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  consumedStorage?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  externalStorageServiceDocumentID?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType
  >;
  status?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type AiAssistantDocumentsResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["AiAssistantDocuments"] = ResolversParentTypes["AiAssistantDocuments"],
> = {
  externalDocuments?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["AiAssistantDocument"]>>>,
    ParentType,
    ContextType
  >;
  internalDocuments?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["AiAssistantDocument"]>>>,
    ParentType,
    ContextType
  >;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type AiAssistantQueryResponseResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["AiAssistantQueryResponse"] = ResolversParentTypes["AiAssistantQueryResponse"],
> = {
  answer?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  chat_id?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  response_language?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  search_results?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["AiAssistantQuerySearchResult"]>>>,
    ParentType,
    ContextType
  >;
  turn_id?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type AiAssistantQuerySearchResultResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["AiAssistantQuerySearchResult"] = ResolversParentTypes["AiAssistantQuerySearchResult"],
> = {
  document_id?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  part_metadata?: Resolver<
    Maybe<ResolversTypes["AiAssistantQuerySearchResultPartMetadata"]>,
    ParentType,
    ContextType
  >;
  score?: Resolver<Maybe<ResolversTypes["Float"]>, ParentType, ContextType>;
  text?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type AiAssistantQuerySearchResultPartMetadataResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["AiAssistantQuerySearchResultPartMetadata"] = ResolversParentTypes["AiAssistantQuerySearchResultPartMetadata"],
> = {
  page?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type AiAssistantUsageResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["AiAssistantUsage"] = ResolversParentTypes["AiAssistantUsage"],
> = {
  consumedOcrScans?: Resolver<Maybe<ResolversTypes["Float"]>, ParentType, ContextType>;
  consumedQueries?: Resolver<Maybe<ResolversTypes["Float"]>, ParentType, ContextType>;
  consumedRecordingSeconds?: Resolver<Maybe<ResolversTypes["Float"]>, ParentType, ContextType>;
  consumedStorage?: Resolver<Maybe<ResolversTypes["Float"]>, ParentType, ContextType>;
  consumedUsers?: Resolver<ResolversTypes["Float"], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type AiAssistantWithCountResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["AiAssistantWithCount"] = ResolversParentTypes["AiAssistantWithCount"],
> = {
  aiAssistants?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["AiAssistant"]>>>,
    ParentType,
    ContextType
  >;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type AiNoteResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["AiNote"] = ResolversParentTypes["AiNote"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  audioUrl?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  createdAt?: Resolver<Maybe<ResolversTypes["DateTime"]>, ParentType, ContextType>;
  createdBy?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  languageCode?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  oem?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  publishedMachines?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["PublishedMachine"]>>>,
    ParentType,
    ContextType
  >;
  summary?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  title?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  transcript?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  transcriptionError?: Resolver<
    Maybe<ResolversTypes["TranscriptionError"]>,
    ParentType,
    ContextType
  >;
  transcriptionStatus?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  updatedBy?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type AnalyticsResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["Analytics"] = ResolversParentTypes["Analytics"],
> = {
  data?: Resolver<Maybe<ResolversTypes["MJSON"]>, ParentType, ContextType>;
  inputTableOptions?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["ReportField"]>>>,
    ParentType,
    ContextType
  >;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type ApiKeyResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["ApiKey"] = ResolversParentTypes["ApiKey"],
> = {
  apiKey?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type AppConfigResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["AppConfig"] = ResolversParentTypes["AppConfig"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  features?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["AppFeaturesEnum"]>>>,
    ParentType,
    ContextType
  >;
  maintenanceOn?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  plans?: Resolver<Maybe<Array<Maybe<ResolversTypes["ProductPlan"]>>>, ParentType, ContextType>;
  version?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type AssetResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["Asset"] = ResolversParentTypes["Asset"],
> = {
  _3dModelUrl?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  _3dModelUrlUploadedBy?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  aiAssistant?: Resolver<Maybe<ResolversTypes["AiAssistant"]>, ParentType, ContextType>;
  assetType?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  childrenCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  createdAt?: Resolver<Maybe<ResolversTypes["DateTime"]>, ParentType, ContextType>;
  createdBy?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  customFields?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["CustomField"]>>>,
    ParentType,
    ContextType
  >;
  customer?: Resolver<Maybe<ResolversTypes["Customer"]>, ParentType, ContextType>;
  description?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  detachedFromTemplate?: Resolver<
    Maybe<ResolversTypes["AssetDetachedFromTemplate"]>,
    ParentType,
    ContextType
  >;
  documentFolders?: Resolver<Maybe<ResolversTypes["DocumentFolders"]>, ParentType, ContextType>;
  documentationFiles?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  folderId?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  generalAccessUrl?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  hierarchy?: Resolver<Maybe<Array<Maybe<ResolversTypes["AssetParent"]>>>, ParentType, ContextType>;
  image?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  inventoryParts?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["AssetPart"]>>>,
    ParentType,
    ContextType
  >;
  isAsset3dModelDeletable?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  isBoxFoldersDisabled?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  isOwnAsset?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  isQRCodeEnabled?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  isQRCodeFlowEnabled?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  isSharedAsset?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  issues?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  name?: Resolver<ResolversTypes["String"], ParentType, ContextType>;
  numberOfTeams?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  oem?: Resolver<Maybe<ResolversTypes["Oem"]>, ParentType, ContextType>;
  serialNumber?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  sharedAssetAccess?: Resolver<Maybe<ResolversTypes["AssetAccess"]>, ParentType, ContextType>;
  sharedAssistant?: Resolver<Maybe<ResolversTypes["AiAssistant"]>, ParentType, ContextType>;
  showParent?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  slug?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  teams?: Resolver<Maybe<Array<Maybe<ResolversTypes["Team"]>>>, ParentType, ContextType>;
  template?: Resolver<Maybe<ResolversTypes["AssetTemplate"]>, ParentType, ContextType>;
  thumbnail?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  totalChildrenCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  totalOpenTickets?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  updatedAt?: Resolver<Maybe<ResolversTypes["DateTime"]>, ParentType, ContextType>;
  uuid?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type AssetAccessResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["AssetAccess"] = ResolversParentTypes["AssetAccess"],
> = {
  _3dModel?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  documentation?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  history?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  parts?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  preventiveMaintenance?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  qrCodes?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  subAssets?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type AssetDetachedFromTemplateResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["AssetDetachedFromTemplate"] = ResolversParentTypes["AssetDetachedFromTemplate"],
> = {
  description?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  documentation?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  image?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  inventoryParts?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type AssetOrTemplateResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["AssetOrTemplate"] = ResolversParentTypes["AssetOrTemplate"],
> = {
  __resolveType: TypeResolveFn<"Asset" | "AssetTemplate", ParentType, ContextType>;
};

export type AssetParentResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["AssetParent"] = ResolversParentTypes["AssetParent"],
> = {
  id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  name?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  serialNumber?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type AssetPartResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["AssetPart"] = ResolversParentTypes["AssetPart"],
> = {
  addedBy?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  part?: Resolver<Maybe<ResolversTypes["InventoryPart"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type AssetTemplateResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["AssetTemplate"] = ResolversParentTypes["AssetTemplate"],
> = {
  _3dModelUrl?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  _3dModelUrlUploadedBy?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  aiAssistant?: Resolver<Maybe<ResolversTypes["AiAssistant"]>, ParentType, ContextType>;
  assets?: Resolver<Maybe<Array<Maybe<ResolversTypes["Asset"]>>>, ParentType, ContextType>;
  createdAt?: Resolver<Maybe<ResolversTypes["DateTime"]>, ParentType, ContextType>;
  createdBy?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  description?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  documentFolders?: Resolver<Maybe<ResolversTypes["DocumentFolders"]>, ParentType, ContextType>;
  image?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  inventoryParts?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["AssetTemplatePart"]>>>,
    ParentType,
    ContextType
  >;
  oem?: Resolver<Maybe<ResolversTypes["Oem"]>, ParentType, ContextType>;
  procedures?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["AssetTemplateProcedure"]>>>,
    ParentType,
    ContextType
  >;
  productAccess?: Resolver<Maybe<ResolversTypes["ProductAccess"]>, ParentType, ContextType>;
  sharedBoxToken?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  templateId?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  thumbnail?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  title?: Resolver<ResolversTypes["String"], ParentType, ContextType>;
  updatedAt?: Resolver<Maybe<ResolversTypes["DateTime"]>, ParentType, ContextType>;
  visibility?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type AssetTemplatePartResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["AssetTemplatePart"] = ResolversParentTypes["AssetTemplatePart"],
> = {
  addedBy?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  part?: Resolver<Maybe<ResolversTypes["InventoryPart"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type AssetTemplateProcedureResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["AssetTemplateProcedure"] = ResolversParentTypes["AssetTemplateProcedure"],
> = {
  addedBy?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  procedure?: Resolver<Maybe<ResolversTypes["Procedure"]>, ParentType, ContextType>;
  procedureId?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type AssetTypeResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["AssetType"] = ResolversParentTypes["AssetType"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  name?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type AssignedWorkOrderReminderResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["AssignedWorkOrderReminder"] = ResolversParentTypes["AssignedWorkOrderReminder"],
> = {
  daysBefore?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  enabled?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type AuthorResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["Author"] = ResolversParentTypes["Author"],
> = {
  confidence?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  name?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface Base64ScalarConfig extends GraphQLScalarTypeConfig<ResolversTypes["Base64"], any> {
  name: "Base64";
}

export type BoardsResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["Boards"] = ResolversParentTypes["Boards"],
> = {
  boardFor?: Resolver<Maybe<ResolversTypes["BoardFor"]>, ParentType, ContextType>;
  customFields?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["CustomAdditionalField"]>>>,
    ParentType,
    ContextType
  >;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CalendarEventResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["CalendarEvent"] = ResolversParentTypes["CalendarEvent"],
> = {
  assignees?: Resolver<Maybe<Array<Maybe<ResolversTypes["ID"]>>>, ParentType, ContextType>;
  id?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  when?: Resolver<Maybe<ResolversTypes["JSON"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CalendarSyncConfigurationResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["CalendarSyncConfiguration"] = ResolversParentTypes["CalendarSyncConfiguration"],
> = {
  allowedConnectedAccounts?: Resolver<Maybe<ResolversTypes["Float"]>, ParentType, ContextType>;
  consumedConnectedAccounts?: Resolver<Maybe<ResolversTypes["Float"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CalendarSyncUsageResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["CalendarSyncUsage"] = ResolversParentTypes["CalendarSyncUsage"],
> = {
  consumedConnectedAccounts?: Resolver<Maybe<ResolversTypes["Float"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CitationResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["Citation"] = ResolversParentTypes["Citation"],
> = {
  highlight?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  id?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  keywords?: Resolver<Maybe<Array<Maybe<ResolversTypes["String"]>>>, ParentType, ContextType>;
  pageNumber?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  source?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CompleteMultiPartUploadPayloadResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["CompleteMultiPartUploadPayload"] = ResolversParentTypes["CompleteMultiPartUploadPayload"],
> = {
  url?: Resolver<ResolversTypes["String"], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type ConnectionHistoryResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["ConnectionHistory"] = ResolversParentTypes["ConnectionHistory"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  createdAt?: Resolver<Maybe<ResolversTypes["DateTime"]>, ParentType, ContextType>;
  createdBy?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  customer?: Resolver<Maybe<ResolversTypes["Customer"]>, ParentType, ContextType>;
  machine?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  month?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  resource?: Resolver<Maybe<ResolversTypes["MJSON"]>, ParentType, ContextType>;
  resourceId?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  ticket?: Resolver<Maybe<ResolversTypes["Ticket"]>, ParentType, ContextType>;
  type?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  updatedBy?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  year?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type ConnectionRequestResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["ConnectionRequest"] = ResolversParentTypes["ConnectionRequest"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  existingContactConnection?: Resolver<Maybe<ResolversTypes["Customer"]>, ParentType, ContextType>;
  resolveDate?: Resolver<Maybe<ResolversTypes["DateTime"]>, ParentType, ContextType>;
  resolvedBy?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  senderOem?: Resolver<Maybe<ResolversTypes["Oem"]>, ParentType, ContextType>;
  sentBy?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  sentDate?: Resolver<Maybe<ResolversTypes["DateTime"]>, ParentType, ContextType>;
  sentTo?: Resolver<Maybe<ResolversTypes["Oem"]>, ParentType, ContextType>;
  sentToConnection?: Resolver<Maybe<ResolversTypes["Customer"]>, ParentType, ContextType>;
  sentToContact?: Resolver<Maybe<ResolversTypes["Contact"]>, ParentType, ContextType>;
  sentToUser?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  status?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type ContactResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["Contact"] = ResolversParentTypes["Contact"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  accessStatus?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  canResendInvite?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  connection?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  createdBy?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  deleted?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  email?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  jobTitle?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  landline?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  name?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  oem?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  phoneNumber?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  updatedBy?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  user?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CreateMultiPartUploadPayloadResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["CreateMultiPartUploadPayload"] = ResolversParentTypes["CreateMultiPartUploadPayload"],
> = {
  UploadId?: Resolver<ResolversTypes["String"], ParentType, ContextType>;
  fileKey?: Resolver<ResolversTypes["String"], ParentType, ContextType>;
  signedUrls?: Resolver<Array<Maybe<ResolversTypes["String"]>>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CustomAdditionalFieldResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["CustomAdditionalField"] = ResolversParentTypes["CustomAdditionalField"],
> = {
  _id?: Resolver<ResolversTypes["ID"], ParentType, ContextType>;
  createdBy?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  created_at?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  description?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  enabled?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  fieldType?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  isAdditionalField?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  label?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  oem?: Resolver<Maybe<ResolversTypes["Oem"]>, ParentType, ContextType>;
  options?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["OptionResponse"]>>>,
    ParentType,
    ContextType
  >;
  order?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  slug?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  type?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  updated_at?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  visibilityScope?: Resolver<Maybe<ResolversTypes["VisibilityScope"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CustomFieldResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["CustomField"] = ResolversParentTypes["CustomField"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  fieldId?: Resolver<Maybe<ResolversTypes["CustomAdditionalField"]>, ParentType, ContextType>;
  reasonReferenceMap?: Resolver<Maybe<ResolversTypes["JSON"]>, ParentType, ContextType>;
  value?: Resolver<Maybe<ResolversTypes["JSON"]>, ParentType, ContextType>;
  values?: Resolver<Maybe<Array<Maybe<ResolversTypes["String"]>>>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CustomerResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["Customer"] = ResolversParentTypes["Customer"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  assetAccess?: Resolver<Maybe<ResolversTypes["AssetAccess"]>, ParentType, ContextType>;
  createdAt?: Resolver<Maybe<ResolversTypes["DateTime"]>, ParentType, ContextType>;
  createdBy?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  customFields?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["CustomField"]>>>,
    ParentType,
    ContextType
  >;
  customerAddress?: Resolver<Maybe<ResolversTypes["CustomerAddress"]>, ParentType, ContextType>;
  description?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  facilityId?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  facilityIdentifier?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  generalAccessUrl?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  hasCustomerPortal?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  isMachineDocumentationEnabled?: Resolver<
    Maybe<ResolversTypes["Boolean"]>,
    ParentType,
    ContextType
  >;
  isPreventiveMaintenanceEventsEnabled?: Resolver<
    Maybe<ResolversTypes["Boolean"]>,
    ParentType,
    ContextType
  >;
  isQRCodeEnabled?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  linkedOrg?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  machines?: Resolver<Maybe<Array<Maybe<ResolversTypes["Asset"]>>>, ParentType, ContextType>;
  name?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  numberOfTeams?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  oem?: Resolver<Maybe<ResolversTypes["Oem"]>, ParentType, ContextType>;
  qrCodeAccess?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  teams?: Resolver<Maybe<Array<Maybe<ResolversTypes["Team"]>>>, ParentType, ContextType>;
  totalMachines?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  totalOpenTickets?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  totalUsers?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  totalUsersWithAccess?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  type?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  updatedAt?: Resolver<Maybe<ResolversTypes["DateTime"]>, ParentType, ContextType>;
  urlOemFacility?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CustomerAddressResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["CustomerAddress"] = ResolversParentTypes["CustomerAddress"],
> = {
  coordinates?: Resolver<Maybe<Array<Maybe<ResolversTypes["Float"]>>>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CustomerMarkerResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["CustomerMarker"] = ResolversParentTypes["CustomerMarker"],
> = {
  _id?: Resolver<ResolversTypes["ID"], ParentType, ContextType>;
  customerAddress?: Resolver<Maybe<ResolversTypes["CustomerAddress"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CustomerPortalResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["CustomerPortal"] = ResolversParentTypes["CustomerPortal"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  assetAccess?: Resolver<Maybe<ResolversTypes["AssetAccess"]>, ParentType, ContextType>;
  connection?: Resolver<Maybe<ResolversTypes["Customer"]>, ParentType, ContextType>;
  contacts?: Resolver<Maybe<Array<Maybe<ResolversTypes["Contact"]>>>, ParentType, ContextType>;
  createdBy?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  deleted?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  name?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  oem?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  productAccess?: Resolver<Maybe<ResolversTypes["ProductAccess"]>, ParentType, ContextType>;
  updatedBy?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CustomerPortalLoginResponseResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["CustomerPortalLoginResponse"] = ResolversParentTypes["CustomerPortalLoginResponse"],
> = {
  isError?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  message?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  redirectUrl?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  refreshToken?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  token?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface DateTimeScalarConfig
  extends GraphQLScalarTypeConfig<ResolversTypes["DateTime"], any> {
  name: "DateTime";
}

export type DeletedEventResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["DeletedEvent"] = ResolversParentTypes["DeletedEvent"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  machine?: Resolver<Maybe<ResolversTypes["Asset"]>, ParentType, ContextType>;
  oem?: Resolver<ResolversTypes["Oem"], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type DocumentResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["Document"] = ResolversParentTypes["Document"],
> = {
  _id?: Resolver<ResolversTypes["ID"], ParentType, ContextType>;
  aiGeneratedTitle?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  authors?: Resolver<Maybe<Array<Maybe<ResolversTypes["Author"]>>>, ParentType, ContextType>;
  boxDocumentId?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  contributors?: Resolver<Maybe<Array<Maybe<ResolversTypes["Author"]>>>, ParentType, ContextType>;
  createdBy?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  customFields?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["CustomField"]>>>,
    ParentType,
    ContextType
  >;
  date?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  documentChunks?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["DocumentChunk"]>>>,
    ParentType,
    ContextType
  >;
  documentPages?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["DocumentPage"]>>>,
    ParentType,
    ContextType
  >;
  editors?: Resolver<Maybe<Array<Maybe<ResolversTypes["Author"]>>>, ParentType, ContextType>;
  language?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  ocrGeneratedContent?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  results?: Resolver<Maybe<Array<Maybe<ResolversTypes["DocumentChunk"]>>>, ParentType, ContextType>;
  size?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  status?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  title?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  updatedBy?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type DocumentAnswersResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["DocumentAnswers"] = ResolversParentTypes["DocumentAnswers"],
> = {
  answer?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  chunkID?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  citations?: Resolver<Maybe<Array<Maybe<ResolversTypes["Citation"]>>>, ParentType, ContextType>;
  documentId?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  highlight?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  keywords?: Resolver<Maybe<Array<Maybe<ResolversTypes["String"]>>>, ParentType, ContextType>;
  pageNumber?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type DocumentChunkResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["DocumentChunk"] = ResolversParentTypes["DocumentChunk"],
> = {
  _id?: Resolver<ResolversTypes["ID"], ParentType, ContextType>;
  boxDocumentId?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  chunkIndex?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  content?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  distance?: Resolver<Maybe<ResolversTypes["Float"]>, ParentType, ContextType>;
  documentAITitle?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  documentCustomFields?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["CustomField"]>>>,
    ParentType,
    ContextType
  >;
  documentDate?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  documentId?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  documentPage?: Resolver<Maybe<ResolversTypes["DocumentPage"]>, ParentType, ContextType>;
  documentTitle?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  ocrGeneratedContent?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  pageContent?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  pageNumber?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type DocumentFoldersResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["DocumentFolders"] = ResolversParentTypes["DocumentFolders"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  externalId?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  internalId?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type DocumentLabelResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["DocumentLabel"] = ResolversParentTypes["DocumentLabel"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  name?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type DocumentPageResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["DocumentPage"] = ResolversParentTypes["DocumentPage"],
> = {
  _id?: Resolver<ResolversTypes["ID"], ParentType, ContextType>;
  content?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  documentId?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  pageIndex?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  translations?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["DocumentTranslation"]>>>,
    ParentType,
    ContextType
  >;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type DocumentTranslationResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["DocumentTranslation"] = ResolversParentTypes["DocumentTranslation"],
> = {
  content?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  language?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  status?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type DraftJunkCountResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["DraftJunkCount"] = ResolversParentTypes["DraftJunkCount"],
> = {
  drafts?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  junk?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type DraftOperationResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["DraftOperation"] = ResolversParentTypes["DraftOperation"],
> = {
  operationType?: Resolver<ResolversTypes["String"], ParentType, ContextType>;
  ticket?: Resolver<Maybe<ResolversTypes["DraftTicket"]>, ParentType, ContextType>;
  unavailable?: Resolver<Maybe<ResolversTypes["DraftUnavailable"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type DraftTicketResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["DraftTicket"] = ResolversParentTypes["DraftTicket"],
> = {
  assignees?: Resolver<Maybe<Array<Maybe<ResolversTypes["ID"]>>>, ParentType, ContextType>;
  id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  resources?: Resolver<Maybe<Array<Maybe<ResolversTypes["ID"]>>>, ParentType, ContextType>;
  schedule?: Resolver<Maybe<ResolversTypes["Schedule"]>, ParentType, ContextType>;
  status?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type DraftUnavailableResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["DraftUnavailable"] = ResolversParentTypes["DraftUnavailable"],
> = {
  id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  schedule?: Resolver<Maybe<ResolversTypes["Schedule"]>, ParentType, ContextType>;
  scheduleType?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  toDelete?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  user?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type EmailAccountResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["EmailAccount"] = ResolversParentTypes["EmailAccount"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  emailAccountStatus?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  emailAddress?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  isSynced?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface EmailAddressScalarConfig
  extends GraphQLScalarTypeConfig<ResolversTypes["EmailAddress"], any> {
  name: "EmailAddress";
}

export type EmailAttachmentResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["EmailAttachment"] = ResolversParentTypes["EmailAttachment"],
> = {
  contentId?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  contentType?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  filename?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  grantId?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  id?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  isInline?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  size?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type EmailDraftResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["EmailDraft"] = ResolversParentTypes["EmailDraft"],
> = {
  draft?: Resolver<Maybe<ResolversTypes["JSON"]>, ParentType, ContextType>;
  fileUrl?: Resolver<Maybe<ResolversTypes["JSON"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type EmailFolderResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["EmailFolder"] = ResolversParentTypes["EmailFolder"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  attributes?: Resolver<Maybe<Array<Maybe<ResolversTypes["String"]>>>, ParentType, ContextType>;
  childCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  grantId?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  id?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  name?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  oem?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  parentId?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  unreadCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type EmailMessageResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["EmailMessage"] = ResolversParentTypes["EmailMessage"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  attachments?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["EmailAttachment"]>>>,
    ParentType,
    ContextType
  >;
  bcc?: Resolver<Maybe<Array<Maybe<ResolversTypes["EmailParticipant"]>>>, ParentType, ContextType>;
  body?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  cc?: Resolver<Maybe<Array<Maybe<ResolversTypes["EmailParticipant"]>>>, ParentType, ContextType>;
  date?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  folders?: Resolver<Maybe<Array<Maybe<ResolversTypes["String"]>>>, ParentType, ContextType>;
  from?: Resolver<Maybe<Array<Maybe<ResolversTypes["EmailParticipant"]>>>, ParentType, ContextType>;
  grantId?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  id?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  oem?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  replyTo?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["EmailParticipant"]>>>,
    ParentType,
    ContextType
  >;
  snippet?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  starred?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  subject?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  threadId?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  to?: Resolver<Maybe<Array<Maybe<ResolversTypes["EmailParticipant"]>>>, ParentType, ContextType>;
  unread?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type EmailMessageWithFileUrlResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["EmailMessageWithFileURL"] = ResolversParentTypes["EmailMessageWithFileURL"],
> = {
  fileUrl?: Resolver<Maybe<ResolversTypes["JSON"]>, ParentType, ContextType>;
  message?: Resolver<Maybe<ResolversTypes["EmailMessage"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type EmailNotificationResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["EmailNotification"] = ResolversParentTypes["EmailNotification"],
> = {
  maintenanceWorkOrderCreationNotifyTo?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["User"]>>>,
    ParentType,
    ContextType
  >;
  messageOnUnassignedWorkOrderNotifyTo?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["User"]>>>,
    ParentType,
    ContextType
  >;
  notifyOnMaintenanceWorkOrderCreation?: Resolver<
    Maybe<ResolversTypes["Boolean"]>,
    ParentType,
    ContextType
  >;
  notifyOnMessageOnUnassignedWorkOrder?: Resolver<
    Maybe<ResolversTypes["Boolean"]>,
    ParentType,
    ContextType
  >;
  notifyOnWorkOrderCreation?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  onAddedAsWorkOrderFollower?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  onAssignedTicketInternalNotePost?: Resolver<
    Maybe<ResolversTypes["Boolean"]>,
    ParentType,
    ContextType
  >;
  onAssignedWorkOrderReminder?: Resolver<
    Maybe<ResolversTypes["AssignedWorkOrderReminder"]>,
    ParentType,
    ContextType
  >;
  onAssignedWorkOrderUpdate?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  onMentionedInWorkOrderInternalNote?: Resolver<
    Maybe<ResolversTypes["Boolean"]>,
    ParentType,
    ContextType
  >;
  onMessageOnAssignedWorkOrder?: Resolver<
    Maybe<ResolversTypes["Boolean"]>,
    ParentType,
    ContextType
  >;
  onNewTicketAssigned?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  workOrderCreationNotifyTo?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["NotificationUser"]>>>,
    ParentType,
    ContextType
  >;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type EmailParticipantResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["EmailParticipant"] = ResolversParentTypes["EmailParticipant"],
> = {
  email?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  name?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type EmailThreadResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["EmailThread"] = ResolversParentTypes["EmailThread"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  earliestMessageDate?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  folders?: Resolver<Maybe<Array<Maybe<ResolversTypes["String"]>>>, ParentType, ContextType>;
  grantId?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  hasAttachments?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  id?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  latestMessageReceivedDate?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  latestMessageSentDate?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  messageIds?: Resolver<Maybe<Array<Maybe<ResolversTypes["String"]>>>, ParentType, ContextType>;
  messages?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["EmailThreadMessage"]>>>,
    ParentType,
    ContextType
  >;
  oem?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  participants?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["EmailParticipant"]>>>,
    ParentType,
    ContextType
  >;
  snippet?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  subject?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  unread?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type EmailThreadMessageResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["EmailThreadMessage"] = ResolversParentTypes["EmailThreadMessage"],
> = {
  bcc?: Resolver<Maybe<Array<Maybe<ResolversTypes["EmailParticipant"]>>>, ParentType, ContextType>;
  cc?: Resolver<Maybe<Array<Maybe<ResolversTypes["EmailParticipant"]>>>, ParentType, ContextType>;
  date?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  from?: Resolver<Maybe<Array<Maybe<ResolversTypes["EmailParticipant"]>>>, ParentType, ContextType>;
  id?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  replyTo?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["EmailParticipant"]>>>,
    ParentType,
    ContextType
  >;
  subject?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  to?: Resolver<Maybe<Array<Maybe<ResolversTypes["EmailParticipant"]>>>, ParentType, ContextType>;
  unread?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type EmailThreadWithTicketResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["EmailThreadWithTicket"] = ResolversParentTypes["EmailThreadWithTicket"],
> = {
  thread?: Resolver<Maybe<ResolversTypes["EmailThread"]>, ParentType, ContextType>;
  ticket?: Resolver<Maybe<ResolversTypes["Ticket"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type FieldAttachmentResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["FieldAttachment"] = ResolversParentTypes["FieldAttachment"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  name?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  size?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  type?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  url?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type FieldOptionResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["FieldOption"] = ResolversParentTypes["FieldOption"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  name?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type FileImportDataMetaResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["FileImportDataMeta"] = ResolversParentTypes["FileImportDataMeta"],
> = {
  templateId?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  templateSection?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type FileImporterResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["FileImporter"] = ResolversParentTypes["FileImporter"],
> = {
  _id?: Resolver<ResolversTypes["ID"], ParentType, ContextType>;
  importers?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["FileImporterData"]>>>,
    ParentType,
    ContextType
  >;
  licenseKey?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  user?: Resolver<Maybe<ResolversTypes["JSON"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type FileImporterDataResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["FileImporterData"] = ResolversParentTypes["FileImporterData"],
> = {
  _id?: Resolver<ResolversTypes["ID"], ParentType, ContextType>;
  fields?: Resolver<Maybe<ResolversTypes["JSON"]>, ParentType, ContextType>;
  meta?: Resolver<Maybe<ResolversTypes["FileImportDataMeta"]>, ParentType, ContextType>;
  settings?: Resolver<Maybe<ResolversTypes["JSON"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GroupedTicketsResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["GroupedTickets"] = ResolversParentTypes["GroupedTickets"],
> = {
  customerId?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  tickets?: Resolver<Maybe<Array<Maybe<ResolversTypes["Ticket"]>>>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GuideResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["Guide"] = ResolversParentTypes["Guide"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  createdBy?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  image?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  machine?: Resolver<Maybe<ResolversTypes["Asset"]>, ParentType, ContextType>;
  name?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  oem?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  sessionId?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface HtmlScalarConfig extends GraphQLScalarTypeConfig<ResolversTypes["HTML"], any> {
  name: "HTML";
}

export type ImporterResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["Importer"] = ResolversParentTypes["Importer"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  actions?: Resolver<Maybe<Array<ResolversTypes["String"]>>, ParentType, ContextType>;
  section?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type InstalledProductResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["InstalledProduct"] = ResolversParentTypes["InstalledProduct"],
> = {
  tier?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  type?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type InventoryPartResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["InventoryPart"] = ResolversParentTypes["InventoryPart"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  accessToken?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  articleNumber?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  createdAt?: Resolver<Maybe<ResolversTypes["DateTime"]>, ParentType, ContextType>;
  createdBy?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  customFields?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["CustomField"]>>>,
    ParentType,
    ContextType
  >;
  description?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  documentFolders?: Resolver<Maybe<ResolversTypes["DocumentFolders"]>, ParentType, ContextType>;
  image?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  machines?: Resolver<Maybe<Array<Maybe<ResolversTypes["Asset"]>>>, ParentType, ContextType>;
  name?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  oem?: Resolver<Maybe<ResolversTypes["Oem"]>, ParentType, ContextType>;
  thumbnail?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  tickets?: Resolver<Maybe<Array<Maybe<ResolversTypes["Ticket"]>>>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface JsonScalarConfig extends GraphQLScalarTypeConfig<ResolversTypes["JSON"], any> {
  name: "JSON";
}

export type KanBanAssetTypeResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["KanBanAssetType"] = ResolversParentTypes["KanBanAssetType"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  label?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type KanbanColumnAssetResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["KanbanColumnAsset"] = ResolversParentTypes["KanbanColumnAsset"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  assetType?: Resolver<Maybe<ResolversTypes["KanBanAssetType"]>, ParentType, ContextType>;
  boardFor?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  cards?: Resolver<Maybe<Array<Maybe<ResolversTypes["Asset"]>>>, ParentType, ContextType>;
  column?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  columnIndex?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  currentPage?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  customField?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  limit?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  skip?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  type?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type KanbanColumnCustomerResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["KanbanColumnCustomer"] = ResolversParentTypes["KanbanColumnCustomer"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  boardFor?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  cards?: Resolver<Maybe<Array<Maybe<ResolversTypes["Customer"]>>>, ParentType, ContextType>;
  column?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  columnIndex?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  currentPage?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  customField?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  limit?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  skip?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  type?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type KanbanColumnTicketResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["KanbanColumnTicket"] = ResolversParentTypes["KanbanColumnTicket"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  boardFor?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  cards?: Resolver<Maybe<Array<Maybe<ResolversTypes["Ticket"]>>>, ParentType, ContextType>;
  column?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  columnIndex?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  currentPage?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  customField?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  limit?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  skip?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  status?: Resolver<Maybe<ResolversTypes["Status"]>, ParentType, ContextType>;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  type?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type KnowledgeBaseResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["KnowledgeBase"] = ResolversParentTypes["KnowledgeBase"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  createdBy?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  description?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  folderId?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  supportedLanguages?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["String"]>>>,
    ParentType,
    ContextType
  >;
  title?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  updatedBy?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type LinkedTicketResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["LinkedTicket"] = ResolversParentTypes["LinkedTicket"],
> = {
  linkedAt?: Resolver<Maybe<ResolversTypes["DateTime"]>, ParentType, ContextType>;
  linkedBy?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  ticket?: Resolver<Maybe<ResolversTypes["Ticket"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface MjsonScalarConfig extends GraphQLScalarTypeConfig<ResolversTypes["MJSON"], any> {
  name: "MJSON";
}

export type MachineHistoryResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["MachineHistory"] = ResolversParentTypes["MachineHistory"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  createdAt?: Resolver<Maybe<ResolversTypes["DateTime"]>, ParentType, ContextType>;
  createdBy?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  customer?: Resolver<Maybe<ResolversTypes["Customer"]>, ParentType, ContextType>;
  machine?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  machineDetails?: Resolver<Maybe<ResolversTypes["Asset"]>, ParentType, ContextType>;
  month?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  note?: Resolver<Maybe<ResolversTypes["MachineHistoryNote"]>, ParentType, ContextType>;
  resource?: Resolver<Maybe<ResolversTypes["MJSON"]>, ParentType, ContextType>;
  resourceId?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  ticket?: Resolver<Maybe<ResolversTypes["Ticket"]>, ParentType, ContextType>;
  type?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  updatedBy?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  year?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type MachineHistoryNoteResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["MachineHistoryNote"] = ResolversParentTypes["MachineHistoryNote"],
> = {
  attachments?: Resolver<
    Array<Maybe<ResolversTypes["MachineHistoryNoteAttachment"]>>,
    ParentType,
    ContextType
  >;
  isInternal?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  message?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type MachineHistoryNoteAttachmentResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["MachineHistoryNoteAttachment"] = ResolversParentTypes["MachineHistoryNoteAttachment"],
> = {
  createdAt?: Resolver<Maybe<ResolversTypes["DateTime"]>, ParentType, ContextType>;
  name?: Resolver<ResolversTypes["String"], ParentType, ContextType>;
  size?: Resolver<ResolversTypes["Int"], ParentType, ContextType>;
  type?: Resolver<ResolversTypes["String"], ParentType, ContextType>;
  url?: Resolver<ResolversTypes["String"], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface MixedScalarConfig extends GraphQLScalarTypeConfig<ResolversTypes["Mixed"], any> {
  name: "Mixed";
}

export type MutationResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["Mutation"] = ResolversParentTypes["Mutation"],
> = {
  _completeMultiPartUploadS3?: Resolver<
    ResolversTypes["CompleteMultiPartUploadPayload"],
    ParentType,
    ContextType,
    RequireFields<Mutation_CompleteMultiPartUploadS3Args, "UploadId" | "fileKey">
  >;
  _createMultiPartUploadS3?: Resolver<
    ResolversTypes["CreateMultiPartUploadPayload"],
    ParentType,
    ContextType,
    RequireFields<
      Mutation_CreateMultiPartUploadS3Args,
      "fileSize" | "filename" | "filetype" | "partSize"
    >
  >;
  _safeSignS3?: Resolver<
    ResolversTypes["S3Payload"],
    ParentType,
    ContextType,
    RequireFields<Mutation_SafeSignS3Args, "filename" | "filetype">
  >;
  _signS3Download?: Resolver<
    ResolversTypes["S3Payload"],
    ParentType,
    ContextType,
    RequireFields<Mutation_SignS3DownloadArgs, "filename">
  >;
  _signS3MultiDownload?: Resolver<
    Maybe<Array<ResolversTypes["S3Payload"]>>,
    ParentType,
    ContextType,
    RequireFields<Mutation_SignS3MultiDownloadArgs, "keys">
  >;
  acceptConnectionRequest?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<MutationAcceptConnectionRequestArgs, "portalToAttach" | "requestId">
  >;
  activeInactiveOwnOemAdditionalField?: Resolver<
    Maybe<ResolversTypes["CustomAdditionalField"]>,
    ParentType,
    ContextType,
    RequireFields<MutationActiveInactiveOwnOemAdditionalFieldArgs, "_id" | "enabled">
  >;
  addAiAssistantDocuments?: Resolver<
    Maybe<ResolversTypes["ID"]>,
    ParentType,
    ContextType,
    Partial<MutationAddAiAssistantDocumentsArgs>
  >;
  addOwnOemAsset3DModel?: Resolver<
    Maybe<ResolversTypes["Asset"]>,
    ParentType,
    ContextType,
    RequireFields<MutationAddOwnOemAsset3DModelArgs, "_3dModelUrl" | "_id">
  >;
  addOwnOemAssetTemplate3DModel?: Resolver<
    Maybe<ResolversTypes["AssetTemplate"]>,
    ParentType,
    ContextType,
    RequireFields<MutationAddOwnOemAssetTemplate3DModelArgs, "_3dModelUrl" | "_id">
  >;
  addTicketAssignee?: Resolver<
    Maybe<ResolversTypes["Ticket"]>,
    ParentType,
    ContextType,
    RequireFields<MutationAddTicketAssigneeArgs, "input">
  >;
  addTicketAttachments?: Resolver<
    Maybe<ResolversTypes["Ticket"]>,
    ParentType,
    ContextType,
    RequireFields<MutationAddTicketAttachmentsArgs, "input">
  >;
  addTicketFollower?: Resolver<
    Maybe<ResolversTypes["Ticket"]>,
    ParentType,
    ContextType,
    RequireFields<MutationAddTicketFollowerArgs, "input">
  >;
  addTicketResource?: Resolver<
    Maybe<ResolversTypes["Ticket"]>,
    ParentType,
    ContextType,
    RequireFields<MutationAddTicketResourceArgs, "input">
  >;
  addTimeTrackerLog?: Resolver<
    Maybe<ResolversTypes["Ticket"]>,
    ParentType,
    ContextType,
    RequireFields<MutationAddTimeTrackerLogArgs, "input">
  >;
  addUserBoard?: Resolver<
    Maybe<ResolversTypes["User"]>,
    ParentType,
    ContextType,
    RequireFields<MutationAddUserBoardArgs, "board">
  >;
  approveInvite?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<MutationApproveInviteArgs, "token">
  >;
  approveOcrDocuments?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    Partial<MutationApproveOcrDocumentsArgs>
  >;
  assignAssetsToParent?: Resolver<
    Maybe<ResolversTypes["Asset"]>,
    ParentType,
    ContextType,
    RequireFields<MutationAssignAssetsToParentArgs, "input">
  >;
  assignMultipleAssetsToTeam?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["Asset"]>>>,
    ParentType,
    ContextType,
    RequireFields<MutationAssignMultipleAssetsToTeamArgs, "input">
  >;
  assignMultipleCustomersToTeam?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["Customer"]>>>,
    ParentType,
    ContextType,
    RequireFields<MutationAssignMultipleCustomersToTeamArgs, "input">
  >;
  assignMultipleSkillsToOemUser?: Resolver<
    Maybe<ResolversTypes["User"]>,
    ParentType,
    ContextType,
    RequireFields<MutationAssignMultipleSkillsToOemUserArgs, "input">
  >;
  assignMultipleUsersToTeam?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["User"]>>>,
    ParentType,
    ContextType,
    Partial<MutationAssignMultipleUsersToTeamArgs>
  >;
  assignOwnOemInventoryPartsToAsset?: Resolver<
    Maybe<ResolversTypes["Asset"]>,
    ParentType,
    ContextType,
    Partial<MutationAssignOwnOemInventoryPartsToAssetArgs>
  >;
  assignOwnOemInventoryPartsToAssetTemplate?: Resolver<
    Maybe<ResolversTypes["AssetTemplate"]>,
    ParentType,
    ContextType,
    Partial<MutationAssignOwnOemInventoryPartsToAssetTemplateArgs>
  >;
  assignOwnOemInventoryPartsToTicket?: Resolver<
    Maybe<ResolversTypes["Ticket"]>,
    ParentType,
    ContextType,
    RequireFields<MutationAssignOwnOemInventoryPartsToTicketArgs, "input">
  >;
  assignOwnOemMultipleAssetsToOwnOemCustomer?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["Asset"]>>>,
    ParentType,
    ContextType,
    RequireFields<MutationAssignOwnOemMultipleAssetsToOwnOemCustomerArgs, "input">
  >;
  assignTicketType?: Resolver<
    Maybe<ResolversTypes["Team"]>,
    ParentType,
    ContextType,
    RequireFields<MutationAssignTicketTypeArgs, "input">
  >;
  attachOwnOemProcedureToWorkOrder?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<MutationAttachOwnOemProcedureToWorkOrderArgs, "input">
  >;
  attachProceduresToTemplate?: Resolver<
    Maybe<ResolversTypes["AssetTemplate"]>,
    ParentType,
    ContextType,
    Partial<MutationAttachProceduresToTemplateArgs>
  >;
  checkValidFacilityUser?: Resolver<
    Maybe<ResolversTypes["Boolean"]>,
    ParentType,
    ContextType,
    RequireFields<MutationCheckValidFacilityUserArgs, "userId">
  >;
  configureOemTicketType?: Resolver<
    Maybe<ResolversTypes["TicketType"]>,
    ParentType,
    ContextType,
    RequireFields<MutationConfigureOemTicketTypeArgs, "id" | "input">
  >;
  create3DGuide?: Resolver<
    Maybe<ResolversTypes["Guide"]>,
    ParentType,
    ContextType,
    Partial<MutationCreate3DGuideArgs>
  >;
  createAiAssistant?: Resolver<
    Maybe<ResolversTypes["AiAssistant"]>,
    ParentType,
    ContextType,
    Partial<MutationCreateAiAssistantArgs>
  >;
  createAiAssistantChat?: Resolver<
    Maybe<ResolversTypes["AiAssistantChat"]>,
    ParentType,
    ContextType,
    RequireFields<MutationCreateAiAssistantChatArgs, "aiAssistantId">
  >;
  createAiAssistantV2?: Resolver<
    Maybe<ResolversTypes["AiAssistant"]>,
    ParentType,
    ContextType,
    Partial<MutationCreateAiAssistantV2Args>
  >;
  createAiNote?: Resolver<
    Maybe<ResolversTypes["AiNote"]>,
    ParentType,
    ContextType,
    RequireFields<MutationCreateAiNoteArgs, "input">
  >;
  createConnectionRequest?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<MutationCreateConnectionRequestArgs, "oemId">
  >;
  createContact?: Resolver<
    Maybe<ResolversTypes["Contact"]>,
    ParentType,
    ContextType,
    RequireFields<MutationCreateContactArgs, "input">
  >;
  createCustomerPortal?: Resolver<
    Maybe<ResolversTypes["CustomerPortal"]>,
    ParentType,
    ContextType,
    RequireFields<MutationCreateCustomerPortalArgs, "connectionId">
  >;
  createDocuments?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    Partial<MutationCreateDocumentsArgs>
  >;
  createMachineHistoryNote?: Resolver<
    Maybe<ResolversTypes["MachineHistory"]>,
    ParentType,
    ContextType,
    Partial<MutationCreateMachineHistoryNoteArgs>
  >;
  createOem?: Resolver<
    Maybe<ResolversTypes["Oem"]>,
    ParentType,
    ContextType,
    RequireFields<MutationCreateOemArgs, "input">
  >;
  createOemAPI?: Resolver<
    Maybe<ResolversTypes["OemAPI"]>,
    ParentType,
    ContextType,
    Partial<MutationCreateOemApiArgs>
  >;
  createOemCustomer?: Resolver<
    Maybe<ResolversTypes["Customer"]>,
    ParentType,
    ContextType,
    RequireFields<MutationCreateOemCustomerArgs, "input">
  >;
  createOemCustomerV2?: Resolver<
    Maybe<ResolversTypes["Customer"]>,
    ParentType,
    ContextType,
    RequireFields<MutationCreateOemCustomerV2Args, "input">
  >;
  createOemFacilityUser?: Resolver<
    Maybe<ResolversTypes["User"]>,
    ParentType,
    ContextType,
    RequireFields<MutationCreateOemFacilityUserArgs, "input">
  >;
  createOemSupportAccount?: Resolver<
    Maybe<ResolversTypes["User"]>,
    ParentType,
    ContextType,
    RequireFields<MutationCreateOemSupportAccountArgs, "input">
  >;
  createOemTicketType?: Resolver<
    Maybe<ResolversTypes["TicketType"]>,
    ParentType,
    ContextType,
    RequireFields<MutationCreateOemTicketTypeArgs, "input">
  >;
  createOwnOemAsset?: Resolver<
    Maybe<ResolversTypes["Asset"]>,
    ParentType,
    ContextType,
    Partial<MutationCreateOwnOemAssetArgs>
  >;
  createOwnOemAssetTemplate?: Resolver<
    Maybe<ResolversTypes["AssetTemplate"]>,
    ParentType,
    ContextType,
    RequireFields<MutationCreateOwnOemAssetTemplateArgs, "input">
  >;
  createOwnOemAssetType?: Resolver<
    Maybe<ResolversTypes["Oem"]>,
    ParentType,
    ContextType,
    RequireFields<MutationCreateOwnOemAssetTypeArgs, "input">
  >;
  createOwnOemCustomField?: Resolver<
    Maybe<ResolversTypes["CustomAdditionalField"]>,
    ParentType,
    ContextType,
    Partial<MutationCreateOwnOemCustomFieldArgs>
  >;
  createOwnOemInventoryPart?: Resolver<
    Maybe<ResolversTypes["InventoryPart"]>,
    ParentType,
    ContextType,
    RequireFields<MutationCreateOwnOemInventoryPartArgs, "input">
  >;
  createOwnOemReport?: Resolver<
    Maybe<ResolversTypes["Report"]>,
    ParentType,
    ContextType,
    Partial<MutationCreateOwnOemReportArgs>
  >;
  createOwnOemResource?: Resolver<
    Maybe<ResolversTypes["Resource"]>,
    ParentType,
    ContextType,
    RequireFields<MutationCreateOwnOemResourceArgs, "input">
  >;
  createOwnOemSkills?: Resolver<
    Maybe<ResolversTypes["Oem"]>,
    ParentType,
    ContextType,
    RequireFields<MutationCreateOwnOemSkillsArgs, "input">
  >;
  createOwnOemTicket?: Resolver<
    Maybe<ResolversTypes["Ticket"]>,
    ParentType,
    ContextType,
    RequireFields<MutationCreateOwnOemTicketArgs, "input">
  >;
  createOwnOemTimeTrackerTag?: Resolver<
    Maybe<ResolversTypes["TimeTracker"]>,
    ParentType,
    ContextType,
    RequireFields<MutationCreateOwnOemTimeTrackerTagArgs, "input">
  >;
  createOwnTicket?: Resolver<
    Maybe<ResolversTypes["Ticket"]>,
    ParentType,
    ContextType,
    RequireFields<MutationCreateOwnTicketArgs, "input">
  >;
  createPreventiveMaintenanceEvent?: Resolver<
    Maybe<ResolversTypes["PreventiveMaintenance"]>,
    ParentType,
    ContextType,
    RequireFields<MutationCreatePreventiveMaintenanceEventArgs, "input">
  >;
  createRequest?: Resolver<
    Maybe<ResolversTypes["Ticket"]>,
    ParentType,
    ContextType,
    RequireFields<MutationCreateRequestArgs, "input">
  >;
  createTeam?: Resolver<
    Maybe<ResolversTypes["Team"]>,
    ParentType,
    ContextType,
    Partial<MutationCreateTeamArgs>
  >;
  customerPortalSignup?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    Partial<MutationCustomerPortalSignupArgs>
  >;
  declineConnectionRequest?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<MutationDeclineConnectionRequestArgs, "requestId">
  >;
  declineInvite?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<MutationDeclineInviteArgs, "token">
  >;
  delete3DGuide?: Resolver<
    Maybe<ResolversTypes["ID"]>,
    ParentType,
    ContextType,
    Partial<MutationDelete3DGuideArgs>
  >;
  deleteAiAssistantChat?: Resolver<
    Maybe<ResolversTypes["ID"]>,
    ParentType,
    ContextType,
    RequireFields<MutationDeleteAiAssistantChatArgs, "aiAssistantChatId">
  >;
  deleteAiNote?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<MutationDeleteAiNoteArgs, "id">
  >;
  deleteContact?: Resolver<
    Maybe<ResolversTypes["ID"]>,
    ParentType,
    ContextType,
    Partial<MutationDeleteContactArgs>
  >;
  deleteCustomerPortal?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<MutationDeleteCustomerPortalArgs, "id">
  >;
  deleteDocuments?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    Partial<MutationDeleteDocumentsArgs>
  >;
  deleteMachineHistoryNote?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<MutationDeleteMachineHistoryNoteArgs, "id" | "machine">
  >;
  deleteOemCustomer?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<MutationDeleteOemCustomerArgs, "customerId">
  >;
  deleteOemFacilityUser?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<MutationDeleteOemFacilityUserArgs, "id">
  >;
  deleteOemSupportAccount?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<MutationDeleteOemSupportAccountArgs, "id">
  >;
  deleteOemTicketType?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<MutationDeleteOemTicketTypeArgs, "id">
  >;
  deleteOwnOemAsset?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<MutationDeleteOwnOemAssetArgs, "assetId">
  >;
  deleteOwnOemAsset3DModel?: Resolver<
    Maybe<ResolversTypes["Asset"]>,
    ParentType,
    ContextType,
    RequireFields<MutationDeleteOwnOemAsset3DModelArgs, "assetId">
  >;
  deleteOwnOemAssetImage?: Resolver<
    Maybe<ResolversTypes["Asset"]>,
    ParentType,
    ContextType,
    RequireFields<MutationDeleteOwnOemAssetImageArgs, "assetId">
  >;
  deleteOwnOemAssetTemplate?: Resolver<
    Maybe<ResolversTypes["AssetTemplate"]>,
    ParentType,
    ContextType,
    RequireFields<MutationDeleteOwnOemAssetTemplateArgs, "templateId">
  >;
  deleteOwnOemAssetTemplate3DModel?: Resolver<
    Maybe<ResolversTypes["AssetTemplate"]>,
    ParentType,
    ContextType,
    RequireFields<MutationDeleteOwnOemAssetTemplate3DModelArgs, "templateId">
  >;
  deleteOwnOemAssetType?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<MutationDeleteOwnOemAssetTypeArgs, "input">
  >;
  deleteOwnOemCustomAdditionalField?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    Partial<MutationDeleteOwnOemCustomAdditionalFieldArgs>
  >;
  deleteOwnOemInventoryPart?: Resolver<
    ResolversTypes["InventoryPart"],
    ParentType,
    ContextType,
    RequireFields<MutationDeleteOwnOemInventoryPartArgs, "id">
  >;
  deleteOwnOemProcedureTemplate?: Resolver<
    Maybe<ResolversTypes["ProcedureTemplate"]>,
    ParentType,
    ContextType,
    RequireFields<MutationDeleteOwnOemProcedureTemplateArgs, "id">
  >;
  deleteOwnOemReport?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<MutationDeleteOwnOemReportArgs, "id">
  >;
  deleteOwnOemResource?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<MutationDeleteOwnOemResourceArgs, "input">
  >;
  deleteOwnOemSkill?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<MutationDeleteOwnOemSkillArgs, "input">
  >;
  deleteOwnOemTicket?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<MutationDeleteOwnOemTicketArgs, "input">
  >;
  deleteOwnOemTimeTrackerTag?: Resolver<
    Maybe<ResolversTypes["TimeTracker"]>,
    ParentType,
    ContextType,
    RequireFields<MutationDeleteOwnOemTimeTrackerTagArgs, "id">
  >;
  deletePreventiveMaintenanceEvent?: Resolver<
    Maybe<ResolversTypes["DeletedEvent"]>,
    ParentType,
    ContextType,
    RequireFields<MutationDeletePreventiveMaintenanceEventArgs, "id">
  >;
  deleteTeam?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<MutationDeleteTeamArgs, "id">
  >;
  detachOwnOemAssetDocumentation?: Resolver<
    Maybe<ResolversTypes["Asset"]>,
    ParentType,
    ContextType,
    RequireFields<MutationDetachOwnOemAssetDocumentationArgs, "_id">
  >;
  detachOwnOemProcedureFromWorkOrder?: Resolver<
    Maybe<ResolversTypes["Procedure"]>,
    ParentType,
    ContextType,
    RequireFields<MutationDetachOwnOemProcedureFromWorkOrderArgs, "input">
  >;
  detachProcedureFromTemplate?: Resolver<
    Maybe<ResolversTypes["AssetTemplate"]>,
    ParentType,
    ContextType,
    RequireFields<MutationDetachProcedureFromTemplateArgs, "templateId">
  >;
  detectLanguage?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<MutationDetectLanguageArgs, "audioUrl">
  >;
  duplicateOwnOemProcedureTemplate?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<MutationDuplicateOwnOemProcedureTemplateArgs, "id">
  >;
  duplicateOwnOemReport?: Resolver<
    Maybe<ResolversTypes["Report"]>,
    ParentType,
    ContextType,
    Partial<MutationDuplicateOwnOemReportArgs>
  >;
  finalizeOwnOemProcedure?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<MutationFinalizeOwnOemProcedureArgs, "input">
  >;
  forgotPassword?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<MutationForgotPasswordArgs, "email">
  >;
  generateApiKey?: Resolver<Maybe<ResolversTypes["ApiKey"]>, ParentType, ContextType>;
  getGeneralSignUpToken?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<MutationGetGeneralSignUpTokenArgs, "input">
  >;
  handleImportUploadId?: Resolver<
    Maybe<ResolversTypes["Boolean"]>,
    ParentType,
    ContextType,
    RequireFields<MutationHandleImportUploadIdArgs, "input">
  >;
  handleOwnOemAssetQRAccess?: Resolver<
    Maybe<ResolversTypes["Asset"]>,
    ParentType,
    ContextType,
    Partial<MutationHandleOwnOemAssetQrAccessArgs>
  >;
  indexDocuments?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    Partial<MutationIndexDocumentsArgs>
  >;
  integrationAddOrUpdateOwnOemCustomersAndMachinesWithAddress?: Resolver<
    Maybe<ResolversTypes["Boolean"]>,
    ParentType,
    ContextType,
    RequireFields<
      MutationIntegrationAddOrUpdateOwnOemCustomersAndMachinesWithAddressArgs,
      "customer"
    >
  >;
  integrationAddOwnOemCustomersAndMachinesWithAddress?: Resolver<
    Maybe<ResolversTypes["Boolean"]>,
    ParentType,
    ContextType,
    RequireFields<MutationIntegrationAddOwnOemCustomersAndMachinesWithAddressArgs, "customer">
  >;
  inviteContacts?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<MutationInviteContactsArgs, "input">
  >;
  linkCalendarSyncAccount?: Resolver<
    Maybe<ResolversTypes["User"]>,
    ParentType,
    ContextType,
    RequireFields<MutationLinkCalendarSyncAccountArgs, "code">
  >;
  linkEmailAccount?: Resolver<
    Maybe<ResolversTypes["Oem"]>,
    ParentType,
    ContextType,
    RequireFields<MutationLinkEmailAccountArgs, "code">
  >;
  linkTickets?: Resolver<
    Maybe<ResolversTypes["Ticket"]>,
    ParentType,
    ContextType,
    RequireFields<MutationLinkTicketsArgs, "linkedTicketId" | "ticketId">
  >;
  login?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    Partial<MutationLoginArgs>
  >;
  loginCmsDashboard?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    Partial<MutationLoginCmsDashboardArgs>
  >;
  loginCustomerPortal?: Resolver<
    Maybe<ResolversTypes["CustomerPortalLoginResponse"]>,
    ParentType,
    ContextType,
    Partial<MutationLoginCustomerPortalArgs>
  >;
  loginFacilityApp?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    Partial<MutationLoginFacilityAppArgs>
  >;
  loginMobileFacility?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    Partial<MutationLoginMobileFacilityArgs>
  >;
  loginMobileOem?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    Partial<MutationLoginMobileOemArgs>
  >;
  loginOemDashboard?: Resolver<
    Maybe<ResolversTypes["JSON"]>,
    ParentType,
    ContextType,
    Partial<MutationLoginOemDashboardArgs>
  >;
  logout?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  machineQrcFacilityView?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    Partial<MutationMachineQrcFacilityViewArgs>
  >;
  moveEmailToFolder?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    Partial<MutationMoveEmailToFolderArgs>
  >;
  publishAiNote?: Resolver<
    Maybe<ResolversTypes["AiNote"]>,
    ParentType,
    ContextType,
    RequireFields<MutationPublishAiNoteArgs, "input">
  >;
  publishTicketScheduleDraft?: Resolver<
    Maybe<ResolversTypes["TicketScheduleDraft"]>,
    ParentType,
    ContextType,
    RequireFields<MutationPublishTicketScheduleDraftArgs, "input">
  >;
  raiseOemSupportQuery?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    Partial<MutationRaiseOemSupportQueryArgs>
  >;
  refreshTokens?: Resolver<
    Maybe<ResolversTypes["JSON"]>,
    ParentType,
    ContextType,
    RequireFields<MutationRefreshTokensArgs, "refreshToken">
  >;
  removeAiAssistant?: Resolver<
    Maybe<ResolversTypes["ID"]>,
    ParentType,
    ContextType,
    Partial<MutationRemoveAiAssistantArgs>
  >;
  removeAiAssistantDocuments?: Resolver<
    Maybe<ResolversTypes["ID"]>,
    ParentType,
    ContextType,
    Partial<MutationRemoveAiAssistantDocumentsArgs>
  >;
  removeCustomerInvite?: Resolver<
    Maybe<ResolversTypes["ID"]>,
    ParentType,
    ContextType,
    Partial<MutationRemoveCustomerInviteArgs>
  >;
  removeFirstSignInRedirectUrl?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  removeOwnOemAssetFromOwnOemCustomer?: Resolver<
    Maybe<ResolversTypes["Asset"]>,
    ParentType,
    ContextType,
    RequireFields<MutationRemoveOwnOemAssetFromOwnOemCustomerArgs, "input">
  >;
  removeOwnOemInventoryPartFromAsset?: Resolver<
    Maybe<ResolversTypes["Asset"]>,
    ParentType,
    ContextType,
    Partial<MutationRemoveOwnOemInventoryPartFromAssetArgs>
  >;
  removeOwnOemInventoryPartFromAssetTemplate?: Resolver<
    Maybe<ResolversTypes["AssetTemplate"]>,
    ParentType,
    ContextType,
    Partial<MutationRemoveOwnOemInventoryPartFromAssetTemplateArgs>
  >;
  removeOwnOemInventoryPartFromTicket?: Resolver<
    Maybe<ResolversTypes["Ticket"]>,
    ParentType,
    ContextType,
    RequireFields<MutationRemoveOwnOemInventoryPartFromTicketArgs, "input">
  >;
  removeTicketAssignee?: Resolver<
    Maybe<ResolversTypes["Ticket"]>,
    ParentType,
    ContextType,
    RequireFields<MutationRemoveTicketAssigneeArgs, "input">
  >;
  removeTicketAttachment?: Resolver<
    Maybe<ResolversTypes["Ticket"]>,
    ParentType,
    ContextType,
    RequireFields<MutationRemoveTicketAttachmentArgs, "input">
  >;
  removeTicketFollower?: Resolver<
    Maybe<ResolversTypes["Ticket"]>,
    ParentType,
    ContextType,
    RequireFields<MutationRemoveTicketFollowerArgs, "input">
  >;
  removeTicketResources?: Resolver<
    Maybe<ResolversTypes["Ticket"]>,
    ParentType,
    ContextType,
    RequireFields<MutationRemoveTicketResourcesArgs, "input">
  >;
  removeTicketType?: Resolver<
    Maybe<ResolversTypes["Team"]>,
    ParentType,
    ContextType,
    Partial<MutationRemoveTicketTypeArgs>
  >;
  removeTimeTrackerLogFromTicket?: Resolver<
    Maybe<ResolversTypes["Ticket"]>,
    ParentType,
    ContextType,
    RequireFields<MutationRemoveTimeTrackerLogFromTicketArgs, "input">
  >;
  removeUserBoard?: Resolver<
    Maybe<ResolversTypes["User"]>,
    ParentType,
    ContextType,
    RequireFields<MutationRemoveUserBoardArgs, "board">
  >;
  renameAiAssistant?: Resolver<
    Maybe<ResolversTypes["ID"]>,
    ParentType,
    ContextType,
    Partial<MutationRenameAiAssistantArgs>
  >;
  renameAiAssistantChat?: Resolver<
    Maybe<ResolversTypes["ID"]>,
    ParentType,
    ContextType,
    RequireFields<MutationRenameAiAssistantChatArgs, "input">
  >;
  resendCustomerInvite?: Resolver<
    Maybe<ResolversTypes["ID"]>,
    ParentType,
    ContextType,
    Partial<MutationResendCustomerInviteArgs>
  >;
  resetOemAPI?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<MutationResetOemApiArgs, "oemId">
  >;
  resetOwnOemAssetToTemplate?: Resolver<
    Maybe<ResolversTypes["Asset"]>,
    ParentType,
    ContextType,
    Partial<MutationResetOwnOemAssetToTemplateArgs>
  >;
  saveAppConfig?: Resolver<
    Maybe<ResolversTypes["AppConfig"]>,
    ParentType,
    ContextType,
    Partial<MutationSaveAppConfigArgs>
  >;
  saveOwnOemProcedure?: Resolver<
    Maybe<ResolversTypes["Procedure"]>,
    ParentType,
    ContextType,
    RequireFields<MutationSaveOwnOemProcedureArgs, "input">
  >;
  saveOwnOemProcedureTemplate?: Resolver<
    Maybe<ResolversTypes["ProcedureTemplate"]>,
    ParentType,
    ContextType,
    RequireFields<MutationSaveOwnOemProcedureTemplateArgs, "input">
  >;
  scanDocuments?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    Partial<MutationScanDocumentsArgs>
  >;
  sendEmail?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    Partial<MutationSendEmailArgs>
  >;
  submitIdeaSuggestion?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    Partial<MutationSubmitIdeaSuggestionArgs>
  >;
  transcribeAiNote?: Resolver<
    Maybe<ResolversTypes["TranscriptionResult"]>,
    ParentType,
    ContextType,
    RequireFields<MutationTranscribeAiNoteArgs, "input">
  >;
  translateAiNoteSummary?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<MutationTranslateAiNoteSummaryArgs, "input">
  >;
  unassignAssetFromParent?: Resolver<
    Maybe<ResolversTypes["Asset"]>,
    ParentType,
    ContextType,
    Partial<MutationUnassignAssetFromParentArgs>
  >;
  unassignAssetFromTeam?: Resolver<
    Maybe<ResolversTypes["Asset"]>,
    ParentType,
    ContextType,
    Partial<MutationUnassignAssetFromTeamArgs>
  >;
  unassignCustomerFromTeam?: Resolver<
    Maybe<ResolversTypes["Customer"]>,
    ParentType,
    ContextType,
    Partial<MutationUnassignCustomerFromTeamArgs>
  >;
  unassignUserFromTeam?: Resolver<
    Maybe<ResolversTypes["User"]>,
    ParentType,
    ContextType,
    Partial<MutationUnassignUserFromTeamArgs>
  >;
  unlinkCalendarSyncAccount?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  unlinkEmailAccount?: Resolver<
    Maybe<ResolversTypes["Oem"]>,
    ParentType,
    ContextType,
    RequireFields<MutationUnlinkEmailAccountArgs, "emailAddressId">
  >;
  unlinkTickets?: Resolver<
    Maybe<ResolversTypes["Ticket"]>,
    ParentType,
    ContextType,
    RequireFields<MutationUnlinkTicketsArgs, "linkedTicketId" | "ticketId">
  >;
  update3DGuide?: Resolver<
    Maybe<ResolversTypes["Guide"]>,
    ParentType,
    ContextType,
    Partial<MutationUpdate3DGuideArgs>
  >;
  updateAiAssistant?: Resolver<
    Maybe<ResolversTypes["ID"]>,
    ParentType,
    ContextType,
    Partial<MutationUpdateAiAssistantArgs>
  >;
  updateAiNoteSummary?: Resolver<
    Maybe<ResolversTypes["AiNote"]>,
    ParentType,
    ContextType,
    RequireFields<MutationUpdateAiNoteSummaryArgs, "input">
  >;
  updateAiNoteTitle?: Resolver<
    Maybe<ResolversTypes["AiNote"]>,
    ParentType,
    ContextType,
    RequireFields<MutationUpdateAiNoteTitleArgs, "input">
  >;
  updateContact?: Resolver<
    Maybe<ResolversTypes["Contact"]>,
    ParentType,
    ContextType,
    RequireFields<MutationUpdateContactArgs, "input">
  >;
  updateCrmOem?: Resolver<
    Maybe<ResolversTypes["OemAdmin"]>,
    ParentType,
    ContextType,
    RequireFields<MutationUpdateCrmOemArgs, "input">
  >;
  updateCustomerPortal?: Resolver<
    Maybe<ResolversTypes["CustomerPortal"]>,
    ParentType,
    ContextType,
    RequireFields<MutationUpdateCustomerPortalArgs, "input">
  >;
  updateDescriptionTicket?: Resolver<
    Maybe<ResolversTypes["Ticket"]>,
    ParentType,
    ContextType,
    RequireFields<MutationUpdateDescriptionTicketArgs, "input">
  >;
  updateDocument?: Resolver<
    Maybe<ResolversTypes["Document"]>,
    ParentType,
    ContextType,
    Partial<MutationUpdateDocumentArgs>
  >;
  updateEmailSignature?: Resolver<
    Maybe<ResolversTypes["User"]>,
    ParentType,
    ContextType,
    RequireFields<MutationUpdateEmailSignatureArgs, "autoAppendEmailSignature" | "signature">
  >;
  updateEmailThread?: Resolver<
    Maybe<ResolversTypes["EmailThreadWithTicket"]>,
    ParentType,
    ContextType,
    Partial<MutationUpdateEmailThreadArgs>
  >;
  updateMachineHistoryNote?: Resolver<
    Maybe<ResolversTypes["MachineHistory"]>,
    ParentType,
    ContextType,
    Partial<MutationUpdateMachineHistoryNoteArgs>
  >;
  updateOemAPI?: Resolver<
    Maybe<ResolversTypes["OemAPI"]>,
    ParentType,
    ContextType,
    Partial<MutationUpdateOemApiArgs>
  >;
  updateOemAiAssistantConfiguration?: Resolver<
    Maybe<ResolversTypes["AiAssistantConfiguration"]>,
    ParentType,
    ContextType,
    RequireFields<MutationUpdateOemAiAssistantConfigurationArgs, "input">
  >;
  updateOemCalendarSyncConfiguration?: Resolver<
    Maybe<ResolversTypes["CalendarSyncConfiguration"]>,
    ParentType,
    ContextType,
    RequireFields<MutationUpdateOemCalendarSyncConfigurationArgs, "input">
  >;
  updateOemCustomer?: Resolver<
    Maybe<ResolversTypes["Customer"]>,
    ParentType,
    ContextType,
    RequireFields<MutationUpdateOemCustomerArgs, "input">
  >;
  updateOemEmailConfiguration?: Resolver<
    Maybe<ResolversTypes["Oem"]>,
    ParentType,
    ContextType,
    Partial<MutationUpdateOemEmailConfigurationArgs>
  >;
  updateOemFacilityUser?: Resolver<
    Maybe<ResolversTypes["User"]>,
    ParentType,
    ContextType,
    RequireFields<MutationUpdateOemFacilityUserArgs, "input">
  >;
  updateOemPlans?: Resolver<
    Maybe<ResolversTypes["Oem"]>,
    ParentType,
    ContextType,
    Partial<MutationUpdateOemPlansArgs>
  >;
  updateOemSupportAccount?: Resolver<
    Maybe<ResolversTypes["User"]>,
    ParentType,
    ContextType,
    RequireFields<MutationUpdateOemSupportAccountArgs, "input">
  >;
  updateOemTicketType?: Resolver<
    Maybe<ResolversTypes["TicketType"]>,
    ParentType,
    ContextType,
    RequireFields<MutationUpdateOemTicketTypeArgs, "id" | "input">
  >;
  updateOwnOem?: Resolver<
    Maybe<ResolversTypes["Oem"]>,
    ParentType,
    ContextType,
    RequireFields<MutationUpdateOwnOemArgs, "input">
  >;
  updateOwnOemAsset?: Resolver<
    Maybe<ResolversTypes["Asset"]>,
    ParentType,
    ContextType,
    Partial<MutationUpdateOwnOemAssetArgs>
  >;
  updateOwnOemAssetTemplate?: Resolver<
    Maybe<ResolversTypes["AssetTemplate"]>,
    ParentType,
    ContextType,
    RequireFields<MutationUpdateOwnOemAssetTemplateArgs, "input">
  >;
  updateOwnOemAssetType?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<MutationUpdateOwnOemAssetTypeArgs, "input">
  >;
  updateOwnOemCustomField?: Resolver<
    Maybe<ResolversTypes["CustomAdditionalField"]>,
    ParentType,
    ContextType,
    Partial<MutationUpdateOwnOemCustomFieldArgs>
  >;
  updateOwnOemCustomFieldsOrder?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    Partial<MutationUpdateOwnOemCustomFieldsOrderArgs>
  >;
  updateOwnOemInventoryPart?: Resolver<
    Maybe<ResolversTypes["InventoryPart"]>,
    ParentType,
    ContextType,
    RequireFields<MutationUpdateOwnOemInventoryPartArgs, "input">
  >;
  updateOwnOemReport?: Resolver<
    Maybe<ResolversTypes["Report"]>,
    ParentType,
    ContextType,
    Partial<MutationUpdateOwnOemReportArgs>
  >;
  updateOwnOemResource?: Resolver<
    Maybe<ResolversTypes["Resource"]>,
    ParentType,
    ContextType,
    RequireFields<MutationUpdateOwnOemResourceArgs, "input">
  >;
  updateOwnOemSkill?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<MutationUpdateOwnOemSkillArgs, "input">
  >;
  updateOwnOemTicket?: Resolver<
    Maybe<ResolversTypes["Ticket"]>,
    ParentType,
    ContextType,
    RequireFields<MutationUpdateOwnOemTicketArgs, "input">
  >;
  updateOwnOemTimeTrackerTag?: Resolver<
    Maybe<ResolversTypes["TimeTracker"]>,
    ParentType,
    ContextType,
    RequireFields<MutationUpdateOwnOemTimeTrackerTagArgs, "input">
  >;
  updatePersonalSettings?: Resolver<
    Maybe<ResolversTypes["User"]>,
    ParentType,
    ContextType,
    Partial<MutationUpdatePersonalSettingsArgs>
  >;
  updatePreventiveMaintenanceEvent?: Resolver<
    Maybe<ResolversTypes["PreventiveMaintenance"]>,
    ParentType,
    ContextType,
    RequireFields<MutationUpdatePreventiveMaintenanceEventArgs, "input">
  >;
  updateSelfFacilityUser?: Resolver<
    Maybe<ResolversTypes["User"]>,
    ParentType,
    ContextType,
    RequireFields<MutationUpdateSelfFacilityUserArgs, "input">
  >;
  updateTeam?: Resolver<
    Maybe<ResolversTypes["Team"]>,
    ParentType,
    ContextType,
    Partial<MutationUpdateTeamArgs>
  >;
  updateTicketScheduleDraft?: Resolver<
    Maybe<ResolversTypes["TicketScheduleDraft"]>,
    ParentType,
    ContextType,
    RequireFields<MutationUpdateTicketScheduleDraftArgs, "input">
  >;
  updateTimeTrackerLog?: Resolver<
    Maybe<ResolversTypes["Ticket"]>,
    ParentType,
    ContextType,
    RequireFields<MutationUpdateTimeTrackerLogArgs, "input">
  >;
  updateTitleTicket?: Resolver<
    Maybe<ResolversTypes["Ticket"]>,
    ParentType,
    ContextType,
    RequireFields<MutationUpdateTitleTicketArgs, "input">
  >;
  uploadSearchFeedback?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    Partial<MutationUploadSearchFeedbackArgs>
  >;
  verifyGeneralSignUpToken?: Resolver<
    Maybe<ResolversTypes["VerifyGeneralSignUpToken"]>,
    ParentType,
    ContextType,
    RequireFields<MutationVerifyGeneralSignUpTokenArgs, "token">
  >;
  verifySignUpToken?: Resolver<
    Maybe<ResolversTypes["SignupTokenVerification"]>,
    ParentType,
    ContextType,
    RequireFields<MutationVerifySignUpTokenArgs, "token">
  >;
};

export interface NonNegativeFloatScalarConfig
  extends GraphQLScalarTypeConfig<ResolversTypes["NonNegativeFloat"], any> {
  name: "NonNegativeFloat";
}

export type NotificationResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["Notification"] = ResolversParentTypes["Notification"],
> = {
  email?: Resolver<Maybe<ResolversTypes["EmailNotification"]>, ParentType, ContextType>;
  status?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type NotificationUserResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["NotificationUser"] = ResolversParentTypes["NotificationUser"],
> = {
  user?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  workOrderTypes?: Resolver<Maybe<Array<Maybe<ResolversTypes["String"]>>>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type OemResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["Oem"] = ResolversParentTypes["Oem"],
> = {
  _id?: Resolver<ResolversTypes["ID"], ParentType, ContextType>;
  aiAssistantConfiguration?: Resolver<
    Maybe<ResolversTypes["AiAssistantConfiguration"]>,
    ParentType,
    ContextType
  >;
  aiAssistantUsage?: Resolver<Maybe<ResolversTypes["AiAssistantUsage"]>, ParentType, ContextType>;
  allowAssigneesAcrossTeams?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  allowFollowersMyWorkOrders?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  allowHourlyPMEOption?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  allowTechnicianToAssignWorkOrders?: Resolver<
    Maybe<ResolversTypes["Boolean"]>,
    ParentType,
    ContextType
  >;
  api?: Resolver<Maybe<ResolversTypes["OemAPI"]>, ParentType, ContextType>;
  assetTypes?: Resolver<Maybe<Array<Maybe<ResolversTypes["AssetType"]>>>, ParentType, ContextType>;
  assetsCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  backgroundColor?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  brandLogo?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  calendarSyncConfiguration?: Resolver<
    Maybe<ResolversTypes["CalendarSyncConfiguration"]>,
    ParentType,
    ContextType
  >;
  calendarSyncUsage?: Resolver<Maybe<ResolversTypes["CalendarSyncUsage"]>, ParentType, ContextType>;
  channelsAddedInGroup?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  coverPhoto?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  deepOfflineFunctionalityEnabled?: Resolver<
    Maybe<ResolversTypes["Boolean"]>,
    ParentType,
    ContextType
  >;
  description?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  emailAccountLinked?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  emailAccountStatus?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  emailAccounts?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["EmailAccount"]>>>,
    ParentType,
    ContextType
  >;
  heading?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  headline?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  importerTemplates?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["TemplateImporter"]>>>,
    ParentType,
    ContextType
  >;
  installedProducts?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["InstalledProduct"]>>>,
    ParentType,
    ContextType
  >;
  linkedConnection?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  linkedEmailAddress?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  location?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  logo?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  maximumAllowedEmailAddresses?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  name?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  notification?: Resolver<Maybe<ResolversTypes["Notification"]>, ParentType, ContextType>;
  paidFeatures?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["PaidFeaturesEnum"]>>>,
    ParentType,
    ContextType
  >;
  paragraph?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  signupSource?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  skills?: Resolver<Maybe<Array<Maybe<ResolversTypes["Skill"]>>>, ParentType, ContextType>;
  slug?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  statuses?: Resolver<Maybe<Array<Maybe<ResolversTypes["Statuses"]>>>, ParentType, ContextType>;
  subHeading?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  textColor?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  thumbnail?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  ticketTypes?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["TicketType"]>>>,
    ParentType,
    ContextType
  >;
  urlOem?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  urlOemFacility?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  usageTracking?: Resolver<Maybe<ResolversTypes["UsageTracking"]>, ParentType, ContextType>;
  website?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type OemApiResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["OemAPI"] = ResolversParentTypes["OemAPI"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  consumedQuota?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  key?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  quota?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  requestRate?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  token?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type OemAdminResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["OemAdmin"] = ResolversParentTypes["OemAdmin"],
> = {
  admin?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  oem?: Resolver<Maybe<ResolversTypes["Oem"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type OemPlanResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["OemPlan"] = ResolversParentTypes["OemPlan"],
> = {
  tier?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  type?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  users?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type OptionResponseResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["OptionResponse"] = ResolversParentTypes["OptionResponse"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  color?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  description?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  value?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PmeAssetPartResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PMEAssetPart"] = ResolversParentTypes["PMEAssetPart"],
> = {
  addedBy?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  part?: Resolver<Maybe<ResolversTypes["InventoryPart"]>, ParentType, ContextType>;
  quantity?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PmeProcedureResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PMEProcedure"] = ResolversParentTypes["PMEProcedure"],
> = {
  addedBy?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  procedure?: Resolver<Maybe<ResolversTypes["Procedure"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PaginatedActivityLogsResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PaginatedActivityLogs"] = ResolversParentTypes["PaginatedActivityLogs"],
> = {
  currentPage?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  limit?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  logs?: Resolver<Maybe<Array<Maybe<ResolversTypes["ActivityLog"]>>>, ParentType, ContextType>;
  skip?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PaginatedAiAssistantChatsResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PaginatedAiAssistantChats"] = ResolversParentTypes["PaginatedAiAssistantChats"],
> = {
  aiAssistantChats?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["AiAssistantChat"]>>>,
    ParentType,
    ContextType
  >;
  currentPage?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  limit?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  skip?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PaginatedAiNotesResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PaginatedAiNotes"] = ResolversParentTypes["PaginatedAiNotes"],
> = {
  aiNotes?: Resolver<Maybe<Array<Maybe<ResolversTypes["AiNote"]>>>, ParentType, ContextType>;
  currentPage?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  limit?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  skip?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PaginatedAssetsResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PaginatedAssets"] = ResolversParentTypes["PaginatedAssets"],
> = {
  assets?: Resolver<Maybe<Array<Maybe<ResolversTypes["Asset"]>>>, ParentType, ContextType>;
  currentPage?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  limit?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  skip?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PaginatedAssistantsResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PaginatedAssistants"] = ResolversParentTypes["PaginatedAssistants"],
> = {
  assistants?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["AiAssistant"]>>>,
    ParentType,
    ContextType
  >;
  currentPage?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  limit?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  skip?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PaginatedConnectionHistoryResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PaginatedConnectionHistory"] = ResolversParentTypes["PaginatedConnectionHistory"],
> = {
  currentPage?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  history?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["MachineHistory"]>>>,
    ParentType,
    ContextType
  >;
  limit?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  machineCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  skip?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PaginatedContactsResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PaginatedContacts"] = ResolversParentTypes["PaginatedContacts"],
> = {
  contacts?: Resolver<Maybe<Array<Maybe<ResolversTypes["Contact"]>>>, ParentType, ContextType>;
  currentPage?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  limit?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  skip?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PaginatedCustomerPortalsResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PaginatedCustomerPortals"] = ResolversParentTypes["PaginatedCustomerPortals"],
> = {
  currentPage?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  customerPortals?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["CustomerPortal"]>>>,
    ParentType,
    ContextType
  >;
  isConnectionAlreadyEstablished?: Resolver<
    Maybe<ResolversTypes["Boolean"]>,
    ParentType,
    ContextType
  >;
  isExistingContactLinkable?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  limit?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  skip?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PaginatedCustomersResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PaginatedCustomers"] = ResolversParentTypes["PaginatedCustomers"],
> = {
  currentPage?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  customers?: Resolver<Maybe<Array<Maybe<ResolversTypes["Customer"]>>>, ParentType, ContextType>;
  limit?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  skip?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PaginatedDocumentsResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PaginatedDocuments"] = ResolversParentTypes["PaginatedDocuments"],
> = {
  currentPage?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  documents?: Resolver<Maybe<Array<Maybe<ResolversTypes["Document"]>>>, ParentType, ContextType>;
  limit?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  skip?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PaginatedEmailContactsResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PaginatedEmailContacts"] = ResolversParentTypes["PaginatedEmailContacts"],
> = {
  contacts?: Resolver<Maybe<Array<Maybe<ResolversTypes["JSON"]>>>, ParentType, ContextType>;
  pageToken?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PaginatedEmailDraftsResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PaginatedEmailDrafts"] = ResolversParentTypes["PaginatedEmailDrafts"],
> = {
  drafts?: Resolver<Maybe<Array<Maybe<ResolversTypes["JSON"]>>>, ParentType, ContextType>;
  pageToken?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PaginatedEmailFoldersResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PaginatedEmailFolders"] = ResolversParentTypes["PaginatedEmailFolders"],
> = {
  currentPage?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  folders?: Resolver<Maybe<Array<Maybe<ResolversTypes["EmailFolder"]>>>, ParentType, ContextType>;
  limit?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  skip?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PaginatedEmailThreadsResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PaginatedEmailThreads"] = ResolversParentTypes["PaginatedEmailThreads"],
> = {
  currentPage?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  pageToken?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  threads?: Resolver<Maybe<Array<Maybe<ResolversTypes["EmailThread"]>>>, ParentType, ContextType>;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PaginatedGuideResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PaginatedGuide"] = ResolversParentTypes["PaginatedGuide"],
> = {
  currentPage?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  guides?: Resolver<Maybe<Array<Maybe<ResolversTypes["Guide"]>>>, ParentType, ContextType>;
  limit?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  skip?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PaginatedInventoryPartsResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PaginatedInventoryParts"] = ResolversParentTypes["PaginatedInventoryParts"],
> = {
  currentPage?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  limit?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  parts?: Resolver<Maybe<Array<Maybe<ResolversTypes["InventoryPart"]>>>, ParentType, ContextType>;
  skip?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PaginatedKanbanAssetsResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PaginatedKanbanAssets"] = ResolversParentTypes["PaginatedKanbanAssets"],
> = {
  columns?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["KanbanColumnAsset"]>>>,
    ParentType,
    ContextType
  >;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PaginatedKanbanCustomersResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PaginatedKanbanCustomers"] = ResolversParentTypes["PaginatedKanbanCustomers"],
> = {
  columns?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["KanbanColumnCustomer"]>>>,
    ParentType,
    ContextType
  >;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PaginatedKanbanTicketsResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PaginatedKanbanTickets"] = ResolversParentTypes["PaginatedKanbanTickets"],
> = {
  columns?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["KanbanColumnTicket"]>>>,
    ParentType,
    ContextType
  >;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PaginatedMachineHistoryResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PaginatedMachineHistory"] = ResolversParentTypes["PaginatedMachineHistory"],
> = {
  currentPage?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  history?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["MachineHistory"]>>>,
    ParentType,
    ContextType
  >;
  limit?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  skip?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  ticketCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PaginatedModelsResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PaginatedModels"] = ResolversParentTypes["PaginatedModels"],
> = {
  currentPage?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  limit?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  models?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["AssetOrTemplate"]>>>,
    ParentType,
    ContextType
  >;
  skip?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PaginatedPreventiveMaintenanceEventsResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PaginatedPreventiveMaintenanceEvents"] = ResolversParentTypes["PaginatedPreventiveMaintenanceEvents"],
> = {
  events?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["PreventiveMaintenance"]>>>,
    ParentType,
    ContextType
  >;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PaginatedReportsResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PaginatedReports"] = ResolversParentTypes["PaginatedReports"],
> = {
  currentPage?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  limit?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  reports?: Resolver<Maybe<Array<Maybe<ResolversTypes["Report"]>>>, ParentType, ContextType>;
  skip?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PaginatedRequestsResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PaginatedRequests"] = ResolversParentTypes["PaginatedRequests"],
> = {
  currentPage?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  limit?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  requests?: Resolver<Maybe<Array<Maybe<ResolversTypes["Ticket"]>>>, ParentType, ContextType>;
  skip?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PaginatedResourcesResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PaginatedResources"] = ResolversParentTypes["PaginatedResources"],
> = {
  currentPage?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  limit?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  resources?: Resolver<Maybe<Array<Maybe<ResolversTypes["Resource"]>>>, ParentType, ContextType>;
  skip?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PaginatedSharedAssetTemplatesResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PaginatedSharedAssetTemplates"] = ResolversParentTypes["PaginatedSharedAssetTemplates"],
> = {
  currentPage?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  limit?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  skip?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  templates?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["AssetTemplate"]>>>,
    ParentType,
    ContextType
  >;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PaginatedSharedAssetsOemResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PaginatedSharedAssetsOem"] = ResolversParentTypes["PaginatedSharedAssetsOem"],
> = {
  currentPage?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  limit?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  oems?: Resolver<Maybe<Array<Maybe<ResolversTypes["Oem"]>>>, ParentType, ContextType>;
  skip?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PaginatedTeamsResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PaginatedTeams"] = ResolversParentTypes["PaginatedTeams"],
> = {
  currentPage?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  limit?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  skip?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  teams?: Resolver<Maybe<Array<Maybe<ResolversTypes["Team"]>>>, ParentType, ContextType>;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PaginatedTicketsResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PaginatedTickets"] = ResolversParentTypes["PaginatedTickets"],
> = {
  currentPage?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  limit?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  skip?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  tickets?: Resolver<Maybe<Array<Maybe<ResolversTypes["Ticket"]>>>, ParentType, ContextType>;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PaginatedUserSchedulesResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PaginatedUserSchedules"] = ResolversParentTypes["PaginatedUserSchedules"],
> = {
  currentPage?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  limit?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  skip?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  userSchedules?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["UserSchedule"]>>>,
    ParentType,
    ContextType
  >;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PaginatedUsersResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PaginatedUsers"] = ResolversParentTypes["PaginatedUsers"],
> = {
  currentPage?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  limit?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  skip?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  users?: Resolver<Maybe<Array<Maybe<ResolversTypes["User"]>>>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PlanTierResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PlanTier"] = ResolversParentTypes["PlanTier"],
> = {
  allowedFeatures?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["String"]>>>,
    ParentType,
    ContextType
  >;
  limits?: Resolver<Maybe<Array<Maybe<ResolversTypes["TierLimit"]>>>, ParentType, ContextType>;
  name?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  price?: Resolver<Maybe<ResolversTypes["Float"]>, ParentType, ContextType>;
  type?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PreventiveMaintenanceResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PreventiveMaintenance"] = ResolversParentTypes["PreventiveMaintenance"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  createdBy?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  description?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  eventDate?: Resolver<ResolversTypes["DateTime"], ParentType, ContextType>;
  frequency?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  inventoryParts?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["PMEAssetPart"]>>>,
    ParentType,
    ContextType
  >;
  machine?: Resolver<ResolversTypes["Asset"], ParentType, ContextType>;
  nextCreationDate?: Resolver<Maybe<ResolversTypes["DateTime"]>, ParentType, ContextType>;
  oem?: Resolver<ResolversTypes["Oem"], ParentType, ContextType>;
  procedures?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["PMEProcedure"]>>>,
    ParentType,
    ContextType
  >;
  repeatIn?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  repeatInNumber?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  startDate?: Resolver<Maybe<ResolversTypes["DateTime"]>, ParentType, ContextType>;
  ticketCreationIn?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  ticketCreationNumber?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  tickets?: Resolver<Maybe<Array<Maybe<ResolversTypes["Ticket"]>>>, ParentType, ContextType>;
  timezoneOffset?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  title?: Resolver<ResolversTypes["String"], ParentType, ContextType>;
  user?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type ProcedureResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["Procedure"] = ResolversParentTypes["Procedure"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  allowUsersToAddMoreSignatures?: Resolver<
    Maybe<ResolversTypes["Boolean"]>,
    ParentType,
    ContextType
  >;
  children?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["ProcedureNode"]>>>,
    ParentType,
    ContextType
  >;
  createdAt?: Resolver<Maybe<ResolversTypes["DateTime"]>, ParentType, ContextType>;
  description?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  name?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  oem?: Resolver<Maybe<ResolversTypes["Oem"]>, ParentType, ContextType>;
  pageHeader?: Resolver<Maybe<ResolversTypes["FieldAttachment"]>, ParentType, ContextType>;
  pdfUrl?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  procedureId?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  signatures?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["ProcedureSignature"]>>>,
    ParentType,
    ContextType
  >;
  state?: Resolver<ResolversTypes["ProcedureStates"], ParentType, ContextType>;
  submittedBy?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  updatedAt?: Resolver<Maybe<ResolversTypes["DateTime"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type ProcedureNodeResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["ProcedureNode"] = ResolversParentTypes["ProcedureNode"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  allowExtraRows?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  attachments?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["FieldAttachment"]>>>,
    ParentType,
    ContextType
  >;
  children?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["ProcedureNode"]>>>,
    ParentType,
    ContextType
  >;
  description?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  isRequired?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  name?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  options?: Resolver<Maybe<Array<Maybe<ResolversTypes["FieldOption"]>>>, ParentType, ContextType>;
  tableOption?: Resolver<Maybe<ResolversTypes["TableOption"]>, ParentType, ContextType>;
  type?: Resolver<Maybe<ResolversTypes["NodeAndFieldTypes"]>, ParentType, ContextType>;
  value?: Resolver<Maybe<ResolversTypes["Mixed"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type ProcedureSignatureResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["ProcedureSignature"] = ResolversParentTypes["ProcedureSignature"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  date?: Resolver<Maybe<ResolversTypes["DateTime"]>, ParentType, ContextType>;
  isAdditionalSignature?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  name?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  signatoryTitle?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  signatureUrl?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type ProcedureTemplateResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["ProcedureTemplate"] = ResolversParentTypes["ProcedureTemplate"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  allowUsersToAddMoreSignatures?: Resolver<
    Maybe<ResolversTypes["Boolean"]>,
    ParentType,
    ContextType
  >;
  children?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["ProcedureTemplateNode"]>>>,
    ParentType,
    ContextType
  >;
  createdAt?: Resolver<Maybe<ResolversTypes["DateTime"]>, ParentType, ContextType>;
  createdBy?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  description?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  name?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  oem?: Resolver<Maybe<ResolversTypes["Oem"]>, ParentType, ContextType>;
  pageHeader?: Resolver<Maybe<ResolversTypes["FieldAttachment"]>, ParentType, ContextType>;
  signatures?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["ProcedureTemplateSignature"]>>>,
    ParentType,
    ContextType
  >;
  updatedAt?: Resolver<Maybe<ResolversTypes["DateTime"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type ProcedureTemplateListResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["ProcedureTemplateList"] = ResolversParentTypes["ProcedureTemplateList"],
> = {
  procedures?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["ProcedureTemplate"]>>>,
    ParentType,
    ContextType
  >;
  totalCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type ProcedureTemplateNodeResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["ProcedureTemplateNode"] = ResolversParentTypes["ProcedureTemplateNode"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  allowExtraRows?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  attachments?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["FieldAttachment"]>>>,
    ParentType,
    ContextType
  >;
  children?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["ProcedureTemplateNode"]>>>,
    ParentType,
    ContextType
  >;
  description?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  isRequired?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  name?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  options?: Resolver<Maybe<Array<Maybe<ResolversTypes["FieldOption"]>>>, ParentType, ContextType>;
  tableOption?: Resolver<Maybe<ResolversTypes["TableOption"]>, ParentType, ContextType>;
  type?: Resolver<Maybe<ResolversTypes["NodeAndFieldTypes"]>, ParentType, ContextType>;
  value?: Resolver<Maybe<Array<Maybe<ResolversTypes["JSON"]>>>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type ProcedureTemplateSignatureResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["ProcedureTemplateSignature"] = ResolversParentTypes["ProcedureTemplateSignature"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  signatoryTitle?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type ProductAccessResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["ProductAccess"] = ResolversParentTypes["ProductAccess"],
> = {
  _3dModel?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  allowCopying?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  documentation?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  parts?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  procedures?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type ProductPlanResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["ProductPlan"] = ResolversParentTypes["ProductPlan"],
> = {
  conflictingPurchases?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["String"]>>>,
    ParentType,
    ContextType
  >;
  name?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  tiers?: Resolver<Maybe<Array<Maybe<ResolversTypes["PlanTier"]>>>, ParentType, ContextType>;
  type?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PublishedMachineResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["PublishedMachine"] = ResolversParentTypes["PublishedMachine"],
> = {
  machine?: Resolver<Maybe<ResolversTypes["Asset"]>, ParentType, ContextType>;
  machineHistoryNoteId?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  publishedAt?: Resolver<Maybe<ResolversTypes["DateTime"]>, ParentType, ContextType>;
  publishedBy?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type QueryResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["Query"] = ResolversParentTypes["Query"],
> = {
  _checkAuth?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  apiKey?: Resolver<Maybe<ResolversTypes["ApiKey"]>, ParentType, ContextType>;
  appUrl?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  checkIfTicketsExistForType?: Resolver<
    Maybe<ResolversTypes["Boolean"]>,
    ParentType,
    ContextType,
    RequireFields<QueryCheckIfTicketsExistForTypeArgs, "ticketTypeId">
  >;
  checkTicketHasStatus?: Resolver<
    Maybe<ResolversTypes["Boolean"]>,
    ParentType,
    ContextType,
    RequireFields<QueryCheckTicketHasStatusArgs, "statusId">
  >;
  connection?: Resolver<ResolversTypes["String"], ParentType, ContextType>;
  currentUser?: Resolver<
    Maybe<ResolversTypes["User"]>,
    ParentType,
    ContextType,
    Partial<QueryCurrentUserArgs>
  >;
  downloadEmailFile?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<QueryDownloadEmailFileArgs, "emailAddressId" | "fileId" | "messageId" | "uuid">
  >;
  downloadProcedurePDF?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<QueryDownloadProcedurePdfArgs, "id" | "timezone" | "uuid">
  >;
  downloadWOPDF?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    Partial<QueryDownloadWopdfArgs>
  >;
  expensiveQuery?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  get3DGuideById?: Resolver<
    Maybe<ResolversTypes["Guide"]>,
    ParentType,
    ContextType,
    RequireFields<QueryGet3DGuideByIdArgs, "id">
  >;
  get3DModels?: Resolver<
    Maybe<ResolversTypes["PaginatedModels"]>,
    ParentType,
    ContextType,
    Partial<QueryGet3DModelsArgs>
  >;
  getAiAssistant?: Resolver<
    Maybe<ResolversTypes["AiAssistant"]>,
    ParentType,
    ContextType,
    Partial<QueryGetAiAssistantArgs>
  >;
  getAiAssistantChat?: Resolver<
    Maybe<ResolversTypes["AiAssistantChat"]>,
    ParentType,
    ContextType,
    Partial<QueryGetAiAssistantChatArgs>
  >;
  getAiAssistantUsage?: Resolver<
    Maybe<ResolversTypes["UserAiAssistantUsage"]>,
    ParentType,
    ContextType,
    RequireFields<QueryGetAiAssistantUsageArgs, "oemId">
  >;
  getAppConfig?: Resolver<Maybe<ResolversTypes["AppConfig"]>, ParentType, ContextType>;
  getAssetTemplate3DAuthToken?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<QueryGetAssetTemplate3DAuthTokenArgs, "id">
  >;
  getBoxFolderAccessToken?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<QueryGetBoxFolderAccessTokenArgs, "assetId">
  >;
  getBoxFolderToken?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    Partial<QueryGetBoxFolderTokenArgs>
  >;
  getCalendarSyncAuthUrl?: Resolver<ResolversTypes["String"], ParentType, ContextType>;
  getCustomerInventoryPart?: Resolver<
    Maybe<ResolversTypes["InventoryPart"]>,
    ParentType,
    ContextType,
    RequireFields<QueryGetCustomerInventoryPartArgs, "id">
  >;
  getCustomerPortal?: Resolver<
    Maybe<ResolversTypes["CustomerPortal"]>,
    ParentType,
    ContextType,
    RequireFields<QueryGetCustomerPortalArgs, "id">
  >;
  getDataCsv?: Resolver<
    Maybe<ResolversTypes["Boolean"]>,
    ParentType,
    ContextType,
    Partial<QueryGetDataCsvArgs>
  >;
  getDocumentAnswers?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["DocumentAnswers"]>>>,
    ParentType,
    ContextType,
    RequireFields<QueryGetDocumentAnswersArgs, "jobId">
  >;
  getDocumentAuthors?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["String"]>>>,
    ParentType,
    ContextType
  >;
  getDocumentById?: Resolver<
    Maybe<ResolversTypes["Document"]>,
    ParentType,
    ContextType,
    RequireFields<QueryGetDocumentByIdArgs, "documentId">
  >;
  getDocumentChunksByIds?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["DocumentChunk"]>>>,
    ParentType,
    ContextType,
    RequireFields<QueryGetDocumentChunksByIdsArgs, "documentChunkIds">
  >;
  getDraft?: Resolver<
    Maybe<ResolversTypes["EmailDraft"]>,
    ParentType,
    ContextType,
    RequireFields<QueryGetDraftArgs, "draftId" | "emailAddressId">
  >;
  getDraftAndJunkEmailCount?: Resolver<
    Maybe<ResolversTypes["DraftJunkCount"]>,
    ParentType,
    ContextType,
    RequireFields<QueryGetDraftAndJunkEmailCountArgs, "emailAddressId">
  >;
  getEmailAuthUrl?: Resolver<ResolversTypes["String"], ParentType, ContextType>;
  getEmailFileUploadUrl?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  getEmailMessage?: Resolver<
    Maybe<ResolversTypes["EmailMessageWithFileURL"]>,
    ParentType,
    ContextType,
    RequireFields<QueryGetEmailMessageArgs, "emailAddressId" | "messageId">
  >;
  getEmailMessageNylas?: Resolver<
    Maybe<ResolversTypes["EmailMessageWithFileURL"]>,
    ParentType,
    ContextType,
    RequireFields<QueryGetEmailMessageNylasArgs, "emailAddressId" | "messageId">
  >;
  getEmailThread?: Resolver<
    Maybe<ResolversTypes["EmailThreadWithTicket"]>,
    ParentType,
    ContextType,
    RequireFields<QueryGetEmailThreadArgs, "emailAddressId" | "threadId">
  >;
  getEmailThreadNylas?: Resolver<
    Maybe<ResolversTypes["EmailThreadWithTicket"]>,
    ParentType,
    ContextType,
    RequireFields<QueryGetEmailThreadNylasArgs, "emailAddressId" | "threadId">
  >;
  getInventoryPart?: Resolver<
    Maybe<ResolversTypes["InventoryPart"]>,
    ParentType,
    ContextType,
    RequireFields<QueryGetInventoryPartArgs, "id">
  >;
  getKnowledgeBase?: Resolver<Maybe<ResolversTypes["KnowledgeBase"]>, ParentType, ContextType>;
  getMachine3DAuthToken?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    RequireFields<QueryGetMachine3DAuthTokenArgs, "id">
  >;
  getNewChatToken?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  getOemBySlug?: Resolver<
    Maybe<ResolversTypes["Oem"]>,
    ParentType,
    ContextType,
    RequireFields<QueryGetOemBySlugArgs, "slug">
  >;
  getOwnAiNote?: Resolver<
    Maybe<ResolversTypes["AiNote"]>,
    ParentType,
    ContextType,
    RequireFields<QueryGetOwnAiNoteArgs, "id">
  >;
  getOwnCustomerAssetById?: Resolver<
    Maybe<ResolversTypes["Asset"]>,
    ParentType,
    ContextType,
    RequireFields<QueryGetOwnCustomerAssetByIdArgs, "id">
  >;
  getOwnCustomerAssetByUuid?: Resolver<
    Maybe<ResolversTypes["Asset"]>,
    ParentType,
    ContextType,
    RequireFields<QueryGetOwnCustomerAssetByUuidArgs, "uuid">
  >;
  getOwnFacilityTicketById?: Resolver<
    Maybe<ResolversTypes["Ticket"]>,
    ParentType,
    ContextType,
    RequireFields<QueryGetOwnFacilityTicketByIdArgs, "id">
  >;
  getOwnOem?: Resolver<Maybe<ResolversTypes["Oem"]>, ParentType, ContextType>;
  getOwnOemAnalytics?: Resolver<
    Maybe<ResolversTypes["Analytics"]>,
    ParentType,
    ContextType,
    Partial<QueryGetOwnOemAnalyticsArgs>
  >;
  getOwnOemAssetById?: Resolver<
    Maybe<ResolversTypes["Asset"]>,
    ParentType,
    ContextType,
    RequireFields<QueryGetOwnOemAssetByIdArgs, "id">
  >;
  getOwnOemAssetTemplate?: Resolver<
    Maybe<ResolversTypes["AssetTemplate"]>,
    ParentType,
    ContextType,
    RequireFields<QueryGetOwnOemAssetTemplateArgs, "templateId">
  >;
  getOwnOemCustomerById?: Resolver<
    Maybe<ResolversTypes["Customer"]>,
    ParentType,
    ContextType,
    RequireFields<QueryGetOwnOemCustomerByIdArgs, "id">
  >;
  getOwnOemProcedureById?: Resolver<
    Maybe<ResolversTypes["Procedure"]>,
    ParentType,
    ContextType,
    RequireFields<QueryGetOwnOemProcedureByIdArgs, "id">
  >;
  getOwnOemProcedureTemplate?: Resolver<
    Maybe<ResolversTypes["ProcedureTemplate"]>,
    ParentType,
    ContextType,
    Partial<QueryGetOwnOemProcedureTemplateArgs>
  >;
  getOwnOemReportByID?: Resolver<
    Maybe<ResolversTypes["Report"]>,
    ParentType,
    ContextType,
    RequireFields<QueryGetOwnOemReportByIdArgs, "id">
  >;
  getOwnOemTicketById?: Resolver<
    Maybe<ResolversTypes["Ticket"]>,
    ParentType,
    ContextType,
    RequireFields<QueryGetOwnOemTicketByIdArgs, "id">
  >;
  getOwnTicketScheduleDraft?: Resolver<
    Maybe<ResolversTypes["TicketScheduleDraft"]>,
    ParentType,
    ContextType
  >;
  getPreventiveMaintenanceEventByID?: Resolver<
    Maybe<ResolversTypes["PreventiveMaintenance"]>,
    ParentType,
    ContextType,
    RequireFields<QueryGetPreventiveMaintenanceEventByIdArgs, "id">
  >;
  getRequest?: Resolver<
    Maybe<ResolversTypes["Ticket"]>,
    ParentType,
    ContextType,
    RequireFields<QueryGetRequestArgs, "requestId">
  >;
  getSharedOrganizationDetails?: Resolver<
    Maybe<ResolversTypes["SharedOrganizationDetails"]>,
    ParentType,
    ContextType,
    RequireFields<QueryGetSharedOrganizationDetailsArgs, "connectionId">
  >;
  getTeam?: Resolver<
    Maybe<ResolversTypes["Team"]>,
    ParentType,
    ContextType,
    RequireFields<QueryGetTeamArgs, "id">
  >;
  list3DGuides?: Resolver<
    Maybe<ResolversTypes["PaginatedGuide"]>,
    ParentType,
    ContextType,
    Partial<QueryList3DGuidesArgs>
  >;
  listAdditionalFields?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["AdditionalField"]>>>,
    ParentType,
    ContextType,
    Partial<QueryListAdditionalFieldsArgs>
  >;
  listAiAssistantChats?: Resolver<
    Maybe<ResolversTypes["PaginatedAiAssistantChats"]>,
    ParentType,
    ContextType,
    Partial<QueryListAiAssistantChatsArgs>
  >;
  listAiAssistants?: Resolver<
    Maybe<ResolversTypes["AiAssistantWithCount"]>,
    ParentType,
    ContextType,
    Partial<QueryListAiAssistantsArgs>
  >;
  listAllImporters?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["Importer"]>>>,
    ParentType,
    ContextType
  >;
  listAllOemCustomersInArea?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["CustomerMarker"]>>>,
    ParentType,
    ContextType,
    Partial<QueryListAllOemCustomersInAreaArgs>
  >;
  listAllOwnOemAssets?: Resolver<
    Maybe<ResolversTypes["PaginatedAssets"]>,
    ParentType,
    ContextType,
    Partial<QueryListAllOwnOemAssetsArgs>
  >;
  listAllOwnOemCustomers?: Resolver<
    Maybe<ResolversTypes["PaginatedCustomers"]>,
    ParentType,
    ContextType,
    Partial<QueryListAllOwnOemCustomersArgs>
  >;
  listAllOwnOemReports?: Resolver<
    Maybe<ResolversTypes["PaginatedReports"]>,
    ParentType,
    ContextType,
    Partial<QueryListAllOwnOemReportsArgs>
  >;
  listAllPreventiveMaintenanceEvents?: Resolver<
    Maybe<ResolversTypes["PaginatedPreventiveMaintenanceEvents"]>,
    ParentType,
    ContextType,
    RequireFields<QueryListAllPreventiveMaintenanceEventsArgs, "params">
  >;
  listAssetsWithLinkedTemplates?: Resolver<
    Array<ResolversTypes["String"]>,
    ParentType,
    ContextType,
    Partial<QueryListAssetsWithLinkedTemplatesArgs>
  >;
  listAssignableUsers?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["User"]>>>,
    ParentType,
    ContextType,
    RequireFields<QueryListAssignableUsersArgs, "ticketId">
  >;
  listConnectionRequests?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["ConnectionRequest"]>>>,
    ParentType,
    ContextType,
    Partial<QueryListConnectionRequestsArgs>
  >;
  listContacts?: Resolver<
    Maybe<ResolversTypes["PaginatedContacts"]>,
    ParentType,
    ContextType,
    Partial<QueryListContactsArgs>
  >;
  listCustomerPortals?: Resolver<
    Maybe<ResolversTypes["PaginatedCustomerPortals"]>,
    ParentType,
    ContextType,
    Partial<QueryListCustomerPortalsArgs>
  >;
  listCustomersWithTickets?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["String"]>>>,
    ParentType,
    ContextType,
    Partial<QueryListCustomersWithTicketsArgs>
  >;
  listDocuments?: Resolver<
    Maybe<ResolversTypes["PaginatedDocuments"]>,
    ParentType,
    ContextType,
    Partial<QueryListDocumentsArgs>
  >;
  listDrafts?: Resolver<
    Maybe<ResolversTypes["PaginatedEmailDrafts"]>,
    ParentType,
    ContextType,
    RequireFields<QueryListDraftsArgs, "emailAddressId">
  >;
  listEmailContacts?: Resolver<
    Maybe<ResolversTypes["PaginatedEmailContacts"]>,
    ParentType,
    ContextType,
    RequireFields<QueryListEmailContactsArgs, "emailAddressId">
  >;
  listEmailFolders?: Resolver<
    Maybe<ResolversTypes["PaginatedEmailFolders"]>,
    ParentType,
    ContextType,
    Partial<QueryListEmailFoldersArgs>
  >;
  listEmailFoldersNylas?: Resolver<
    Maybe<ResolversTypes["PaginatedEmailFolders"]>,
    ParentType,
    ContextType,
    RequireFields<QueryListEmailFoldersNylasArgs, "emailAddressId">
  >;
  listEmailThreadsByFolder?: Resolver<
    Maybe<ResolversTypes["PaginatedEmailThreads"]>,
    ParentType,
    ContextType,
    Partial<QueryListEmailThreadsByFolderArgs>
  >;
  listEmailThreadsByFolderNylas?: Resolver<
    Maybe<ResolversTypes["PaginatedEmailThreads"]>,
    ParentType,
    ContextType,
    RequireFields<QueryListEmailThreadsByFolderNylasArgs, "emailAddressId" | "folder" | "search">
  >;
  listEmailThreadsByTicket?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["EmailThread"]>>>,
    ParentType,
    ContextType,
    RequireFields<QueryListEmailThreadsByTicketArgs, "emailAddressId" | "ticketId">
  >;
  listEmailThreadsByTicketNylas?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["JSON"]>>>,
    ParentType,
    ContextType,
    RequireFields<QueryListEmailThreadsByTicketNylasArgs, "emailAddressId" | "ticketId">
  >;
  listGroupedByCustomersTickets?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["GroupedTickets"]>>>,
    ParentType,
    ContextType,
    Partial<QueryListGroupedByCustomersTicketsArgs>
  >;
  listOemCustomersInArea?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["CustomerMarker"]>>>,
    ParentType,
    ContextType,
    Partial<QueryListOemCustomersInAreaArgs>
  >;
  listOwnAiNotes?: Resolver<
    Maybe<ResolversTypes["PaginatedAiNotes"]>,
    ParentType,
    ContextType,
    Partial<QueryListOwnAiNotesArgs>
  >;
  listOwnAssetHierarchyTickets?: Resolver<
    Maybe<ResolversTypes["PaginatedTickets"]>,
    ParentType,
    ContextType,
    Partial<QueryListOwnAssetHierarchyTicketsArgs>
  >;
  listOwnCustomerAssets?: Resolver<
    Maybe<ResolversTypes["PaginatedAssets"]>,
    ParentType,
    ContextType,
    Partial<QueryListOwnCustomerAssetsArgs>
  >;
  listOwnCustomerTickets?: Resolver<
    Maybe<ResolversTypes["PaginatedTickets"]>,
    ParentType,
    ContextType,
    Partial<QueryListOwnCustomerTicketsArgs>
  >;
  listOwnFacilityClosedTickets?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["Ticket"]>>>,
    ParentType,
    ContextType
  >;
  listOwnFacilityOpenTickets?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["Ticket"]>>>,
    ParentType,
    ContextType
  >;
  listOwnOemActivityLogs?: Resolver<
    Maybe<ResolversTypes["PaginatedActivityLogs"]>,
    ParentType,
    ContextType,
    Partial<QueryListOwnOemActivityLogsArgs>
  >;
  listOwnOemAllTickets?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["Ticket"]>>>,
    ParentType,
    ContextType,
    Partial<QueryListOwnOemAllTicketsArgs>
  >;
  listOwnOemAssetTemplates?: Resolver<
    Array<Maybe<ResolversTypes["AssetTemplate"]>>,
    ParentType,
    ContextType,
    RequireFields<QueryListOwnOemAssetTemplatesArgs, "params">
  >;
  listOwnOemAssetTemplatesMissingTemplateIds?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["String"]>>>,
    ParentType,
    ContextType,
    RequireFields<QueryListOwnOemAssetTemplatesMissingTemplateIdsArgs, "templateIds">
  >;
  listOwnOemAssetTicketHistoryById?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["Ticket"]>>>,
    ParentType,
    ContextType,
    RequireFields<QueryListOwnOemAssetTicketHistoryByIdArgs, "id">
  >;
  listOwnOemAssetsMissingSerialNumbers?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["String"]>>>,
    ParentType,
    ContextType,
    RequireFields<QueryListOwnOemAssetsMissingSerialNumbersArgs, "serialNumbers">
  >;
  listOwnOemClosedTickets?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["Ticket"]>>>,
    ParentType,
    ContextType
  >;
  listOwnOemConnectionHistory?: Resolver<
    Maybe<ResolversTypes["PaginatedConnectionHistory"]>,
    ParentType,
    ContextType,
    Partial<QueryListOwnOemConnectionHistoryArgs>
  >;
  listOwnOemCustomFields?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["CustomAdditionalField"]>>>,
    ParentType,
    ContextType,
    Partial<QueryListOwnOemCustomFieldsArgs>
  >;
  listOwnOemCustomersMissingClientIds?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["String"]>>>,
    ParentType,
    ContextType,
    RequireFields<QueryListOwnOemCustomersMissingClientIdsArgs, "clientIds">
  >;
  listOwnOemDuplicateTickets?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["String"]>>>,
    ParentType,
    ContextType,
    RequireFields<QueryListOwnOemDuplicateTicketsArgs, "ticketIds">
  >;
  listOwnOemFacilityUsers?: Resolver<
    Maybe<ResolversTypes["PaginatedUsers"]>,
    ParentType,
    ContextType,
    Partial<QueryListOwnOemFacilityUsersArgs>
  >;
  listOwnOemInventoryPart?: Resolver<
    Maybe<ResolversTypes["PaginatedInventoryParts"]>,
    ParentType,
    ContextType,
    Partial<QueryListOwnOemInventoryPartArgs>
  >;
  listOwnOemKanbanAssets?: Resolver<
    Maybe<ResolversTypes["PaginatedKanbanAssets"]>,
    ParentType,
    ContextType,
    Partial<QueryListOwnOemKanbanAssetsArgs>
  >;
  listOwnOemKanbanCustomers?: Resolver<
    Maybe<ResolversTypes["PaginatedKanbanCustomers"]>,
    ParentType,
    ContextType,
    Partial<QueryListOwnOemKanbanCustomersArgs>
  >;
  listOwnOemKanbanTickets?: Resolver<
    Maybe<ResolversTypes["PaginatedKanbanTickets"]>,
    ParentType,
    ContextType,
    Partial<QueryListOwnOemKanbanTicketsArgs>
  >;
  listOwnOemMachineHistory?: Resolver<
    Maybe<ResolversTypes["PaginatedMachineHistory"]>,
    ParentType,
    ContextType,
    Partial<QueryListOwnOemMachineHistoryArgs>
  >;
  listOwnOemOpenTickets?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["Ticket"]>>>,
    ParentType,
    ContextType,
    Partial<QueryListOwnOemOpenTicketsArgs>
  >;
  listOwnOemPlans?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["OemPlan"]>>>,
    ParentType,
    ContextType
  >;
  listOwnOemProcedureTemplates?: Resolver<
    Array<Maybe<ResolversTypes["ProcedureTemplate"]>>,
    ParentType,
    ContextType,
    RequireFields<QueryListOwnOemProcedureTemplatesArgs, "params">
  >;
  listOwnOemProcedureTemplatesWithCount?: Resolver<
    ResolversTypes["ProcedureTemplateList"],
    ParentType,
    ContextType,
    RequireFields<QueryListOwnOemProcedureTemplatesWithCountArgs, "params">
  >;
  listOwnOemResources?: Resolver<
    Maybe<ResolversTypes["PaginatedResources"]>,
    ParentType,
    ContextType,
    Partial<QueryListOwnOemResourcesArgs>
  >;
  listOwnOemSharedAssetHistory?: Resolver<
    Maybe<ResolversTypes["PaginatedMachineHistory"]>,
    ParentType,
    ContextType,
    Partial<QueryListOwnOemSharedAssetHistoryArgs>
  >;
  listOwnOemSupportAccounts?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["User"]>>>,
    ParentType,
    ContextType,
    Partial<QueryListOwnOemSupportAccountsArgs>
  >;
  listOwnOemTickets?: Resolver<
    Maybe<ResolversTypes["PaginatedTickets"]>,
    ParentType,
    ContextType,
    Partial<QueryListOwnOemTicketsArgs>
  >;
  listOwnOemTimeTrackers?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["TimeTracker"]>>>,
    ParentType,
    ContextType,
    RequireFields<QueryListOwnOemTimeTrackersArgs, "fieldType" | "type">
  >;
  listOwnOemUserClosedTickets?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["Ticket"]>>>,
    ParentType,
    ContextType
  >;
  listOwnOemUserOpenTickets?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["Ticket"]>>>,
    ParentType,
    ContextType
  >;
  listOwnTicketMentionableUsers?: Resolver<
    Maybe<ResolversTypes["TicketMentionUsers"]>,
    ParentType,
    ContextType,
    RequireFields<QueryListOwnTicketMentionableUsersArgs, "ticketId">
  >;
  listOwnUserSchedules?: Resolver<
    Maybe<ResolversTypes["PaginatedUserSchedules"]>,
    ParentType,
    ContextType,
    Partial<QueryListOwnUserSchedulesArgs>
  >;
  listPreventiveMaintenanceEvents?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["PreventiveMaintenance"]>>>,
    ParentType,
    ContextType,
    RequireFields<QueryListPreventiveMaintenanceEventsArgs, "params">
  >;
  listRequestableAssets?: Resolver<
    Maybe<ResolversTypes["PaginatedAssets"]>,
    ParentType,
    ContextType,
    Partial<QueryListRequestableAssetsArgs>
  >;
  listRequests?: Resolver<
    Maybe<ResolversTypes["PaginatedRequests"]>,
    ParentType,
    ContextType,
    Partial<QueryListRequestsArgs>
  >;
  listSharedAiAssistants?: Resolver<
    Maybe<ResolversTypes["PaginatedAssistants"]>,
    ParentType,
    ContextType,
    Partial<QueryListSharedAiAssistantsArgs>
  >;
  listSharedAssetTemplates?: Resolver<
    Maybe<ResolversTypes["PaginatedSharedAssetTemplates"]>,
    ParentType,
    ContextType,
    RequireFields<QueryListSharedAssetTemplatesArgs, "params">
  >;
  listSharedAssetsOrganizations?: Resolver<
    Maybe<ResolversTypes["PaginatedSharedAssetsOem"]>,
    ParentType,
    ContextType,
    Partial<QueryListSharedAssetsOrganizationsArgs>
  >;
  listSharedPreventiveMaintenanceEvents?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["PreventiveMaintenance"]>>>,
    ParentType,
    ContextType,
    RequireFields<QueryListSharedPreventiveMaintenanceEventsArgs, "params">
  >;
  listTeams?: Resolver<
    Maybe<ResolversTypes["PaginatedTeams"]>,
    ParentType,
    ContextType,
    Partial<QueryListTeamsArgs>
  >;
  listTicketTypes?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["TicketType"]>>>,
    ParentType,
    ContextType
  >;
  listUsersCalendarEvents?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["CalendarEvent"]>>>,
    ParentType,
    ContextType,
    RequireFields<QueryListUsersCalendarEventsArgs, "endDate" | "startDate">
  >;
  listUsersDuplicatedEmails?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["String"]>>>,
    ParentType,
    ContextType,
    RequireFields<QueryListUsersDuplicatedEmailsArgs, "emails">
  >;
  oemById?: Resolver<
    Maybe<ResolversTypes["Oem"]>,
    ParentType,
    ContextType,
    RequireFields<QueryOemByIdArgs, "id">
  >;
  oemByIdWithAdmin?: Resolver<
    Maybe<ResolversTypes["OemAdmin"]>,
    ParentType,
    ContextType,
    RequireFields<QueryOemByIdWithAdminArgs, "id">
  >;
  oems?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["Oem"]>>>,
    ParentType,
    ContextType,
    RequireFields<QueryOemsArgs, "params">
  >;
  ping?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  queryAiAssistant?: Resolver<
    Maybe<ResolversTypes["AiAssistantQueryResponse"]>,
    ParentType,
    ContextType,
    RequireFields<QueryQueryAiAssistantArgs, "input">
  >;
  queryDocuments?: Resolver<
    Maybe<ResolversTypes["QueryResponse"]>,
    ParentType,
    ContextType,
    RequireFields<QueryQueryDocumentsArgs, "query">
  >;
  test?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  tickets?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["Ticket"]>>>,
    ParentType,
    ContextType,
    RequireFields<QueryTicketsArgs, "params">
  >;
  totalTickets?: Resolver<
    Maybe<ResolversTypes["Int"]>,
    ParentType,
    ContextType,
    Partial<QueryTotalTicketsArgs>
  >;
};

export type QueryResponseResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["QueryResponse"] = ResolversParentTypes["QueryResponse"],
> = {
  groupedResults?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["Document"]>>>,
    ParentType,
    ContextType
  >;
  jobId?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type ReportResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["Report"] = ResolversParentTypes["Report"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  chartType?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  data?: Resolver<Maybe<ResolversTypes["MJSON"]>, ParentType, ContextType>;
  entity?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  filters?: Resolver<Maybe<Array<Maybe<ResolversTypes["ReportFilter"]>>>, ParentType, ContextType>;
  inputs?: Resolver<Maybe<ResolversTypes["ReportOptions"]>, ParentType, ContextType>;
  oem?: Resolver<Maybe<ResolversTypes["Oem"]>, ParentType, ContextType>;
  segment?: Resolver<Maybe<ResolversTypes["ReportField"]>, ParentType, ContextType>;
  table?: Resolver<Maybe<Array<Maybe<ResolversTypes["ReportField"]>>>, ParentType, ContextType>;
  title?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  xAxis?: Resolver<Maybe<ResolversTypes["ReportField"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type ReportFieldResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["ReportField"] = ResolversParentTypes["ReportField"],
> = {
  label?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  value?: Resolver<Maybe<ResolversTypes["Mixed"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type ReportFilterResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["ReportFilter"] = ResolversParentTypes["ReportFilter"],
> = {
  condition?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  field?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  filterType?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  value?: Resolver<Maybe<Array<Maybe<ResolversTypes["ReportField"]>>>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type ReportOptionFilterResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["ReportOptionFilter"] = ResolversParentTypes["ReportOptionFilter"],
> = {
  condition?: Resolver<Maybe<Array<Maybe<ResolversTypes["String"]>>>, ParentType, ContextType>;
  field?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  filterType?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  value?: Resolver<Maybe<Array<Maybe<ResolversTypes["ReportField"]>>>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type ReportOptionsResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["ReportOptions"] = ResolversParentTypes["ReportOptions"],
> = {
  filter?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["ReportOptionFilter"]>>>,
    ParentType,
    ContextType
  >;
  segment?: Resolver<Maybe<Array<Maybe<ResolversTypes["ReportField"]>>>, ParentType, ContextType>;
  table?: Resolver<Maybe<Array<Maybe<ResolversTypes["ReportField"]>>>, ParentType, ContextType>;
  xAxis?: Resolver<Maybe<Array<Maybe<ResolversTypes["ReportField"]>>>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type ResourceResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["Resource"] = ResolversParentTypes["Resource"],
> = {
  _id?: Resolver<ResolversTypes["ID"], ParentType, ContextType>;
  name?: Resolver<ResolversTypes["String"], ParentType, ContextType>;
  oem?: Resolver<ResolversTypes["Oem"], ParentType, ContextType>;
  resourceId?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  ticketsCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  type?: Resolver<ResolversTypes["String"], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type S3PayloadResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["S3Payload"] = ResolversParentTypes["S3Payload"],
> = {
  id?: Resolver<ResolversTypes["String"], ParentType, ContextType>;
  signedRequest?: Resolver<ResolversTypes["String"], ParentType, ContextType>;
  url?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface SafeStringScalarConfig
  extends GraphQLScalarTypeConfig<ResolversTypes["SafeString"], any> {
  name: "SafeString";
}

export type ScheduleResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["Schedule"] = ResolversParentTypes["Schedule"],
> = {
  endTime?: Resolver<Maybe<ResolversTypes["DateTime"]>, ParentType, ContextType>;
  endTimezone?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  isAllDay?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  isTimezoneEnabled?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  startTime?: Resolver<Maybe<ResolversTypes["DateTime"]>, ParentType, ContextType>;
  startTimezone?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type SharedOrganizationDetailsResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["SharedOrganizationDetails"] = ResolversParentTypes["SharedOrganizationDetails"],
> = {
  assetAccess?: Resolver<Maybe<ResolversTypes["AssetAccess"]>, ParentType, ContextType>;
  oem?: Resolver<Maybe<ResolversTypes["Oem"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type SignupTokenVerificationResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["SignupTokenVerification"] = ResolversParentTypes["SignupTokenVerification"],
> = {
  email?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  emailMismatch?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  id?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  isError?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  isInvalidToken?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  isInvitationExpired?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  message?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  notApprovedByOwner?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  userAdded?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  userExists?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type SkillResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["Skill"] = ResolversParentTypes["Skill"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  name?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type StatusResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["Status"] = ResolversParentTypes["Status"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  color?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  label?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type StatusesResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["Statuses"] = ResolversParentTypes["Statuses"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  color?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  label?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type TableColumnResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["TableColumn"] = ResolversParentTypes["TableColumn"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  heading?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  uniqueId?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  width?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type TableOptionResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["TableOption"] = ResolversParentTypes["TableOption"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  columns?: Resolver<Maybe<Array<Maybe<ResolversTypes["TableColumn"]>>>, ParentType, ContextType>;
  rowCount?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type TeamResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["Team"] = ResolversParentTypes["Team"],
> = {
  _id?: Resolver<ResolversTypes["ID"], ParentType, ContextType>;
  createdAt?: Resolver<Maybe<ResolversTypes["DateTime"]>, ParentType, ContextType>;
  description?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  disallowedTicketTypes?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["TicketType"]>>>,
    ParentType,
    ContextType
  >;
  members?: Resolver<Maybe<Array<Maybe<ResolversTypes["User"]>>>, ParentType, ContextType>;
  name?: Resolver<ResolversTypes["String"], ParentType, ContextType>;
  numberOfMembers?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  oem?: Resolver<Maybe<ResolversTypes["Oem"]>, ParentType, ContextType>;
  teamColor?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type TemplateImporterResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["TemplateImporter"] = ResolversParentTypes["TemplateImporter"],
> = {
  templateId?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  templateSection?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type TicketResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["Ticket"] = ResolversParentTypes["Ticket"],
> = {
  _id?: Resolver<ResolversTypes["ID"], ParentType, ContextType>;
  assignee?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  assignees?: Resolver<Maybe<Array<Maybe<ResolversTypes["User"]>>>, ParentType, ContextType>;
  attachments?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["TicketAttachment"]>>>,
    ParentType,
    ContextType
  >;
  createdAt?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  createdBy?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  createdByContact?: Resolver<Maybe<ResolversTypes["Contact"]>, ParentType, ContextType>;
  customFields?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["CustomField"]>>>,
    ParentType,
    ContextType
  >;
  description?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  facility?: Resolver<Maybe<ResolversTypes["Customer"]>, ParentType, ContextType>;
  followers?: Resolver<Maybe<Array<Maybe<ResolversTypes["User"]>>>, ParentType, ContextType>;
  hasOriginalEventBeenDeleted?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  inventoryParts?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["TicketMachinePart"]>>>,
    ParentType,
    ContextType
  >;
  isMyTicket?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  lastUpdatedAt?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  linkedTickets?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["LinkedTicket"]>>>,
    ParentType,
    ContextType
  >;
  machine?: Resolver<Maybe<ResolversTypes["Asset"]>, ParentType, ContextType>;
  maintenanceOn?: Resolver<Maybe<ResolversTypes["DateTime"]>, ParentType, ContextType>;
  notes?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  numberOfTeams?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  oem?: Resolver<Maybe<ResolversTypes["Oem"]>, ParentType, ContextType>;
  preventiveMaintenance?: Resolver<
    Maybe<ResolversTypes["PreventiveMaintenance"]>,
    ParentType,
    ContextType
  >;
  procedures?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["TicketProcedure"]>>>,
    ParentType,
    ContextType
  >;
  resources?: Resolver<Maybe<Array<Maybe<ResolversTypes["Resource"]>>>, ParentType, ContextType>;
  schedule?: Resolver<Maybe<ResolversTypes["Schedule"]>, ParentType, ContextType>;
  status?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  teams?: Resolver<Maybe<Array<Maybe<ResolversTypes["Team"]>>>, ParentType, ContextType>;
  ticketChatChannels?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["String"]>>>,
    ParentType,
    ContextType
  >;
  ticketId?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  ticketInternalNotesChatChannels?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["String"]>>>,
    ParentType,
    ContextType
  >;
  ticketType?: Resolver<Maybe<ResolversTypes["TicketType"]>, ParentType, ContextType>;
  timeTrackerLogs?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["TimeTrackerLog"]>>>,
    ParentType,
    ContextType
  >;
  title?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  totalTimeLoggedInSeconds?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  unread?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  updatedAt?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  url?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  user?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type TicketAttachmentResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["TicketAttachment"] = ResolversParentTypes["TicketAttachment"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  createdAt?: Resolver<Maybe<ResolversTypes["DateTime"]>, ParentType, ContextType>;
  name?: Resolver<ResolversTypes["String"], ParentType, ContextType>;
  size?: Resolver<ResolversTypes["Int"], ParentType, ContextType>;
  type?: Resolver<ResolversTypes["String"], ParentType, ContextType>;
  url?: Resolver<ResolversTypes["String"], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type TicketMachinePartResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["TicketMachinePart"] = ResolversParentTypes["TicketMachinePart"],
> = {
  addedBy?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  part?: Resolver<Maybe<ResolversTypes["InventoryPart"]>, ParentType, ContextType>;
  quantity?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type TicketMentionUsersResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["TicketMentionUsers"] = ResolversParentTypes["TicketMentionUsers"],
> = {
  facilityUsers?: Resolver<Maybe<Array<Maybe<ResolversTypes["User"]>>>, ParentType, ContextType>;
  oemUsers?: Resolver<Maybe<Array<Maybe<ResolversTypes["User"]>>>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type TicketProcedureResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["TicketProcedure"] = ResolversParentTypes["TicketProcedure"],
> = {
  addedBy?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  procedure?: Resolver<Maybe<ResolversTypes["Procedure"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type TicketScheduleDraftResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["TicketScheduleDraft"] = ResolversParentTypes["TicketScheduleDraft"],
> = {
  _id?: Resolver<ResolversTypes["ID"], ParentType, ContextType>;
  draft?: Resolver<Maybe<Array<Maybe<ResolversTypes["DraftOperation"]>>>, ParentType, ContextType>;
  version?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type TicketTypeResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["TicketType"] = ResolversParentTypes["TicketType"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  color?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  customFields?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["TicketTypeCustomField"]>>>,
    ParentType,
    ContextType
  >;
  descriptionConfig?: Resolver<
    Maybe<ResolversTypes["TicketTypeDescriptionConfig"]>,
    ParentType,
    ContextType
  >;
  icon?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  isDefault?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  isInternal?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  isSystem?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  name?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  partsConfig?: Resolver<Maybe<ResolversTypes["TicketTypePartsConfig"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type TicketTypeCustomFieldResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["TicketTypeCustomField"] = ResolversParentTypes["TicketTypeCustomField"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  customAdditionalField?: Resolver<
    Maybe<ResolversTypes["CustomAdditionalField"]>,
    ParentType,
    ContextType
  >;
  description?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  isRequired?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type TicketTypeDescriptionConfigResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["TicketTypeDescriptionConfig"] = ResolversParentTypes["TicketTypeDescriptionConfig"],
> = {
  fieldName?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  isRequired?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  show?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type TicketTypePartsConfigResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["TicketTypePartsConfig"] = ResolversParentTypes["TicketTypePartsConfig"],
> = {
  fieldName?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  isRequired?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  show?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type TierLimitResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["TierLimit"] = ResolversParentTypes["TierLimit"],
> = {
  type?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  value?: Resolver<Maybe<ResolversTypes["Float"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type TimeTrackerResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["TimeTracker"] = ResolversParentTypes["TimeTracker"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  color?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  createdBy?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  fieldType?: Resolver<Maybe<ResolversTypes["FieldTypes"]>, ParentType, ContextType>;
  label?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  oem?: Resolver<Maybe<ResolversTypes["Oem"]>, ParentType, ContextType>;
  slug?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  type?: Resolver<Maybe<ResolversTypes["Types"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type TimeTrackerLogResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["TimeTrackerLog"] = ResolversParentTypes["TimeTrackerLog"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  createdBy?: Resolver<Maybe<ResolversTypes["User"]>, ParentType, ContextType>;
  description?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  endDateTime?: Resolver<Maybe<ResolversTypes["DateTime"]>, ParentType, ContextType>;
  isBillable?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  startDateTime?: Resolver<Maybe<ResolversTypes["DateTime"]>, ParentType, ContextType>;
  ticketTag?: Resolver<Maybe<ResolversTypes["TimeTracker"]>, ParentType, ContextType>;
  timeElapsedInSeconds?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type TranscriptionErrorResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["TranscriptionError"] = ResolversParentTypes["TranscriptionError"],
> = {
  code?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  message?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type TranscriptionResultResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["TranscriptionResult"] = ResolversParentTypes["TranscriptionResult"],
> = {
  summary?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  title?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  transcript?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type UsageTrackingResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["UsageTracking"] = ResolversParentTypes["UsageTracking"],
> = {
  logRocket?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type UserResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["User"] = ResolversParentTypes["User"],
> = {
  _id?: Resolver<ResolversTypes["ID"], ParentType, ContextType>;
  about?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  access?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  aiAssistantConfiguration?: Resolver<
    Maybe<ResolversTypes["AiAssistantConfiguration"]>,
    ParentType,
    ContextType
  >;
  autoAppendEmailSignature?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  boards?: Resolver<Maybe<Array<Maybe<ResolversTypes["Boards"]>>>, ParentType, ContextType>;
  calendarSyncAccountLinked?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  calendarSyncAccountStatus?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  callChannels?: Resolver<Maybe<Array<Maybe<ResolversTypes["String"]>>>, ParentType, ContextType>;
  chatKeys?: Resolver<Maybe<ResolversTypes["JSON"]>, ParentType, ContextType>;
  chatToken?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  chatUUID?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  chatUUIDMetadata?: Resolver<Maybe<ResolversTypes["JSON"]>, ParentType, ContextType>;
  consumedRecordingSeconds?: Resolver<Maybe<ResolversTypes["Float"]>, ParentType, ContextType>;
  contactId?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  contactName?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  deleted?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  department?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  email?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  emailNotification?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  emailSignature?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  facility?: Resolver<Maybe<ResolversTypes["Customer"]>, ParentType, ContextType>;
  fileImporter?: Resolver<Maybe<ResolversTypes["FileImporter"]>, ParentType, ContextType>;
  firstName?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  firstSignInRedirectUrl?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  foldersAccessToken?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  function?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  hasAiAssistantAccess?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  info?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  intercomHash?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  isOem?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  lastName?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  linkedCalendarSyncEmailAddress?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType
  >;
  mobile?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  name?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  notification?: Resolver<Maybe<ResolversTypes["Notification"]>, ParentType, ContextType>;
  notificationChannel?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  notificationChannelGroupName?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  numberOfTeams?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  oem?: Resolver<Maybe<ResolversTypes["Oem"]>, ParentType, ContextType>;
  organizationName?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  organizationNotificationChannel?: Resolver<
    Maybe<ResolversTypes["String"]>,
    ParentType,
    ContextType
  >;
  organizationType?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  permissions?: Resolver<Maybe<Array<Maybe<ResolversTypes["String"]>>>, ParentType, ContextType>;
  phone?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  productAccess?: Resolver<Maybe<Array<Maybe<ResolversTypes["String"]>>>, ParentType, ContextType>;
  requestsChannelsGroupNames?: Resolver<
    Maybe<Array<Maybe<ResolversTypes["String"]>>>,
    ParentType,
    ContextType
  >;
  role?: Resolver<Maybe<ResolversTypes["enumRoles"]>, ParentType, ContextType>;
  skills?: Resolver<Maybe<Array<Maybe<ResolversTypes["Skill"]>>>, ParentType, ContextType>;
  teams?: Resolver<Maybe<Array<Maybe<ResolversTypes["Team"]>>>, ParentType, ContextType>;
  totalActiveTickets?: Resolver<Maybe<ResolversTypes["Int"]>, ParentType, ContextType>;
  userCredentialsSent?: Resolver<Maybe<ResolversTypes["Boolean"]>, ParentType, ContextType>;
  userType?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  username?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type UserAiAssistantUsageResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["UserAiAssistantUsage"] = ResolversParentTypes["UserAiAssistantUsage"],
> = {
  allowedQueries?: Resolver<Maybe<ResolversTypes["Float"]>, ParentType, ContextType>;
  consumedQueries?: Resolver<Maybe<ResolversTypes["Float"]>, ParentType, ContextType>;
  oem?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type UserScheduleResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["UserSchedule"] = ResolversParentTypes["UserSchedule"],
> = {
  _id?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  oem?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  schedule?: Resolver<Maybe<ResolversTypes["Schedule"]>, ParentType, ContextType>;
  scheduleType?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  ticket?: Resolver<Maybe<ResolversTypes["Ticket"]>, ParentType, ContextType>;
  user?: Resolver<Maybe<ResolversTypes["ID"]>, ParentType, ContextType>;
  uuid?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type VerifyGeneralSignUpTokenResolvers<
  ContextType = any,
  ParentType extends ResolversParentTypes["VerifyGeneralSignUpToken"] = ResolversParentTypes["VerifyGeneralSignUpToken"],
> = {
  email?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  redirectUrl?: Resolver<Maybe<ResolversTypes["String"]>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type Resolvers<ContextType = any> = {
  ActivityLog?: ActivityLogResolvers<ContextType>;
  AdditionalField?: AdditionalFieldResolvers<ContextType>;
  AiAssistant?: AiAssistantResolvers<ContextType>;
  AiAssistantChat?: AiAssistantChatResolvers<ContextType>;
  AiAssistantChatTurn?: AiAssistantChatTurnResolvers<ContextType>;
  AiAssistantConfiguration?: AiAssistantConfigurationResolvers<ContextType>;
  AiAssistantDocument?: AiAssistantDocumentResolvers<ContextType>;
  AiAssistantDocuments?: AiAssistantDocumentsResolvers<ContextType>;
  AiAssistantQueryResponse?: AiAssistantQueryResponseResolvers<ContextType>;
  AiAssistantQuerySearchResult?: AiAssistantQuerySearchResultResolvers<ContextType>;
  AiAssistantQuerySearchResultPartMetadata?: AiAssistantQuerySearchResultPartMetadataResolvers<ContextType>;
  AiAssistantUsage?: AiAssistantUsageResolvers<ContextType>;
  AiAssistantWithCount?: AiAssistantWithCountResolvers<ContextType>;
  AiNote?: AiNoteResolvers<ContextType>;
  Analytics?: AnalyticsResolvers<ContextType>;
  ApiKey?: ApiKeyResolvers<ContextType>;
  AppConfig?: AppConfigResolvers<ContextType>;
  Asset?: AssetResolvers<ContextType>;
  AssetAccess?: AssetAccessResolvers<ContextType>;
  AssetDetachedFromTemplate?: AssetDetachedFromTemplateResolvers<ContextType>;
  AssetOrTemplate?: AssetOrTemplateResolvers<ContextType>;
  AssetParent?: AssetParentResolvers<ContextType>;
  AssetPart?: AssetPartResolvers<ContextType>;
  AssetTemplate?: AssetTemplateResolvers<ContextType>;
  AssetTemplatePart?: AssetTemplatePartResolvers<ContextType>;
  AssetTemplateProcedure?: AssetTemplateProcedureResolvers<ContextType>;
  AssetType?: AssetTypeResolvers<ContextType>;
  AssignedWorkOrderReminder?: AssignedWorkOrderReminderResolvers<ContextType>;
  Author?: AuthorResolvers<ContextType>;
  Base64?: GraphQLScalarType;
  Boards?: BoardsResolvers<ContextType>;
  CalendarEvent?: CalendarEventResolvers<ContextType>;
  CalendarSyncConfiguration?: CalendarSyncConfigurationResolvers<ContextType>;
  CalendarSyncUsage?: CalendarSyncUsageResolvers<ContextType>;
  Citation?: CitationResolvers<ContextType>;
  CompleteMultiPartUploadPayload?: CompleteMultiPartUploadPayloadResolvers<ContextType>;
  ConnectionHistory?: ConnectionHistoryResolvers<ContextType>;
  ConnectionRequest?: ConnectionRequestResolvers<ContextType>;
  Contact?: ContactResolvers<ContextType>;
  CreateMultiPartUploadPayload?: CreateMultiPartUploadPayloadResolvers<ContextType>;
  CustomAdditionalField?: CustomAdditionalFieldResolvers<ContextType>;
  CustomField?: CustomFieldResolvers<ContextType>;
  Customer?: CustomerResolvers<ContextType>;
  CustomerAddress?: CustomerAddressResolvers<ContextType>;
  CustomerMarker?: CustomerMarkerResolvers<ContextType>;
  CustomerPortal?: CustomerPortalResolvers<ContextType>;
  CustomerPortalLoginResponse?: CustomerPortalLoginResponseResolvers<ContextType>;
  DateTime?: GraphQLScalarType;
  DeletedEvent?: DeletedEventResolvers<ContextType>;
  Document?: DocumentResolvers<ContextType>;
  DocumentAnswers?: DocumentAnswersResolvers<ContextType>;
  DocumentChunk?: DocumentChunkResolvers<ContextType>;
  DocumentFolders?: DocumentFoldersResolvers<ContextType>;
  DocumentLabel?: DocumentLabelResolvers<ContextType>;
  DocumentPage?: DocumentPageResolvers<ContextType>;
  DocumentTranslation?: DocumentTranslationResolvers<ContextType>;
  DraftJunkCount?: DraftJunkCountResolvers<ContextType>;
  DraftOperation?: DraftOperationResolvers<ContextType>;
  DraftTicket?: DraftTicketResolvers<ContextType>;
  DraftUnavailable?: DraftUnavailableResolvers<ContextType>;
  EmailAccount?: EmailAccountResolvers<ContextType>;
  EmailAddress?: GraphQLScalarType;
  EmailAttachment?: EmailAttachmentResolvers<ContextType>;
  EmailDraft?: EmailDraftResolvers<ContextType>;
  EmailFolder?: EmailFolderResolvers<ContextType>;
  EmailMessage?: EmailMessageResolvers<ContextType>;
  EmailMessageWithFileURL?: EmailMessageWithFileUrlResolvers<ContextType>;
  EmailNotification?: EmailNotificationResolvers<ContextType>;
  EmailParticipant?: EmailParticipantResolvers<ContextType>;
  EmailThread?: EmailThreadResolvers<ContextType>;
  EmailThreadMessage?: EmailThreadMessageResolvers<ContextType>;
  EmailThreadWithTicket?: EmailThreadWithTicketResolvers<ContextType>;
  FieldAttachment?: FieldAttachmentResolvers<ContextType>;
  FieldOption?: FieldOptionResolvers<ContextType>;
  FileImportDataMeta?: FileImportDataMetaResolvers<ContextType>;
  FileImporter?: FileImporterResolvers<ContextType>;
  FileImporterData?: FileImporterDataResolvers<ContextType>;
  GroupedTickets?: GroupedTicketsResolvers<ContextType>;
  Guide?: GuideResolvers<ContextType>;
  HTML?: GraphQLScalarType;
  Importer?: ImporterResolvers<ContextType>;
  InstalledProduct?: InstalledProductResolvers<ContextType>;
  InventoryPart?: InventoryPartResolvers<ContextType>;
  JSON?: GraphQLScalarType;
  KanBanAssetType?: KanBanAssetTypeResolvers<ContextType>;
  KanbanColumnAsset?: KanbanColumnAssetResolvers<ContextType>;
  KanbanColumnCustomer?: KanbanColumnCustomerResolvers<ContextType>;
  KanbanColumnTicket?: KanbanColumnTicketResolvers<ContextType>;
  KnowledgeBase?: KnowledgeBaseResolvers<ContextType>;
  LinkedTicket?: LinkedTicketResolvers<ContextType>;
  MJSON?: GraphQLScalarType;
  MachineHistory?: MachineHistoryResolvers<ContextType>;
  MachineHistoryNote?: MachineHistoryNoteResolvers<ContextType>;
  MachineHistoryNoteAttachment?: MachineHistoryNoteAttachmentResolvers<ContextType>;
  Mixed?: GraphQLScalarType;
  Mutation?: MutationResolvers<ContextType>;
  NonNegativeFloat?: GraphQLScalarType;
  Notification?: NotificationResolvers<ContextType>;
  NotificationUser?: NotificationUserResolvers<ContextType>;
  Oem?: OemResolvers<ContextType>;
  OemAPI?: OemApiResolvers<ContextType>;
  OemAdmin?: OemAdminResolvers<ContextType>;
  OemPlan?: OemPlanResolvers<ContextType>;
  OptionResponse?: OptionResponseResolvers<ContextType>;
  PMEAssetPart?: PmeAssetPartResolvers<ContextType>;
  PMEProcedure?: PmeProcedureResolvers<ContextType>;
  PaginatedActivityLogs?: PaginatedActivityLogsResolvers<ContextType>;
  PaginatedAiAssistantChats?: PaginatedAiAssistantChatsResolvers<ContextType>;
  PaginatedAiNotes?: PaginatedAiNotesResolvers<ContextType>;
  PaginatedAssets?: PaginatedAssetsResolvers<ContextType>;
  PaginatedAssistants?: PaginatedAssistantsResolvers<ContextType>;
  PaginatedConnectionHistory?: PaginatedConnectionHistoryResolvers<ContextType>;
  PaginatedContacts?: PaginatedContactsResolvers<ContextType>;
  PaginatedCustomerPortals?: PaginatedCustomerPortalsResolvers<ContextType>;
  PaginatedCustomers?: PaginatedCustomersResolvers<ContextType>;
  PaginatedDocuments?: PaginatedDocumentsResolvers<ContextType>;
  PaginatedEmailContacts?: PaginatedEmailContactsResolvers<ContextType>;
  PaginatedEmailDrafts?: PaginatedEmailDraftsResolvers<ContextType>;
  PaginatedEmailFolders?: PaginatedEmailFoldersResolvers<ContextType>;
  PaginatedEmailThreads?: PaginatedEmailThreadsResolvers<ContextType>;
  PaginatedGuide?: PaginatedGuideResolvers<ContextType>;
  PaginatedInventoryParts?: PaginatedInventoryPartsResolvers<ContextType>;
  PaginatedKanbanAssets?: PaginatedKanbanAssetsResolvers<ContextType>;
  PaginatedKanbanCustomers?: PaginatedKanbanCustomersResolvers<ContextType>;
  PaginatedKanbanTickets?: PaginatedKanbanTicketsResolvers<ContextType>;
  PaginatedMachineHistory?: PaginatedMachineHistoryResolvers<ContextType>;
  PaginatedModels?: PaginatedModelsResolvers<ContextType>;
  PaginatedPreventiveMaintenanceEvents?: PaginatedPreventiveMaintenanceEventsResolvers<ContextType>;
  PaginatedReports?: PaginatedReportsResolvers<ContextType>;
  PaginatedRequests?: PaginatedRequestsResolvers<ContextType>;
  PaginatedResources?: PaginatedResourcesResolvers<ContextType>;
  PaginatedSharedAssetTemplates?: PaginatedSharedAssetTemplatesResolvers<ContextType>;
  PaginatedSharedAssetsOem?: PaginatedSharedAssetsOemResolvers<ContextType>;
  PaginatedTeams?: PaginatedTeamsResolvers<ContextType>;
  PaginatedTickets?: PaginatedTicketsResolvers<ContextType>;
  PaginatedUserSchedules?: PaginatedUserSchedulesResolvers<ContextType>;
  PaginatedUsers?: PaginatedUsersResolvers<ContextType>;
  PlanTier?: PlanTierResolvers<ContextType>;
  PreventiveMaintenance?: PreventiveMaintenanceResolvers<ContextType>;
  Procedure?: ProcedureResolvers<ContextType>;
  ProcedureNode?: ProcedureNodeResolvers<ContextType>;
  ProcedureSignature?: ProcedureSignatureResolvers<ContextType>;
  ProcedureTemplate?: ProcedureTemplateResolvers<ContextType>;
  ProcedureTemplateList?: ProcedureTemplateListResolvers<ContextType>;
  ProcedureTemplateNode?: ProcedureTemplateNodeResolvers<ContextType>;
  ProcedureTemplateSignature?: ProcedureTemplateSignatureResolvers<ContextType>;
  ProductAccess?: ProductAccessResolvers<ContextType>;
  ProductPlan?: ProductPlanResolvers<ContextType>;
  PublishedMachine?: PublishedMachineResolvers<ContextType>;
  Query?: QueryResolvers<ContextType>;
  QueryResponse?: QueryResponseResolvers<ContextType>;
  Report?: ReportResolvers<ContextType>;
  ReportField?: ReportFieldResolvers<ContextType>;
  ReportFilter?: ReportFilterResolvers<ContextType>;
  ReportOptionFilter?: ReportOptionFilterResolvers<ContextType>;
  ReportOptions?: ReportOptionsResolvers<ContextType>;
  Resource?: ResourceResolvers<ContextType>;
  S3Payload?: S3PayloadResolvers<ContextType>;
  SafeString?: GraphQLScalarType;
  Schedule?: ScheduleResolvers<ContextType>;
  SharedOrganizationDetails?: SharedOrganizationDetailsResolvers<ContextType>;
  SignupTokenVerification?: SignupTokenVerificationResolvers<ContextType>;
  Skill?: SkillResolvers<ContextType>;
  Status?: StatusResolvers<ContextType>;
  Statuses?: StatusesResolvers<ContextType>;
  TableColumn?: TableColumnResolvers<ContextType>;
  TableOption?: TableOptionResolvers<ContextType>;
  Team?: TeamResolvers<ContextType>;
  TemplateImporter?: TemplateImporterResolvers<ContextType>;
  Ticket?: TicketResolvers<ContextType>;
  TicketAttachment?: TicketAttachmentResolvers<ContextType>;
  TicketMachinePart?: TicketMachinePartResolvers<ContextType>;
  TicketMentionUsers?: TicketMentionUsersResolvers<ContextType>;
  TicketProcedure?: TicketProcedureResolvers<ContextType>;
  TicketScheduleDraft?: TicketScheduleDraftResolvers<ContextType>;
  TicketType?: TicketTypeResolvers<ContextType>;
  TicketTypeCustomField?: TicketTypeCustomFieldResolvers<ContextType>;
  TicketTypeDescriptionConfig?: TicketTypeDescriptionConfigResolvers<ContextType>;
  TicketTypePartsConfig?: TicketTypePartsConfigResolvers<ContextType>;
  TierLimit?: TierLimitResolvers<ContextType>;
  TimeTracker?: TimeTrackerResolvers<ContextType>;
  TimeTrackerLog?: TimeTrackerLogResolvers<ContextType>;
  TranscriptionError?: TranscriptionErrorResolvers<ContextType>;
  TranscriptionResult?: TranscriptionResultResolvers<ContextType>;
  UsageTracking?: UsageTrackingResolvers<ContextType>;
  User?: UserResolvers<ContextType>;
  UserAiAssistantUsage?: UserAiAssistantUsageResolvers<ContextType>;
  UserSchedule?: UserScheduleResolvers<ContextType>;
  VerifyGeneralSignUpToken?: VerifyGeneralSignUpTokenResolvers<ContextType>;
};

export type DirectiveResolvers<ContextType = any> = {
  constraint?: ConstraintDirectiveResolver<any, any, ContextType>;
  cost?: CostDirectiveResolver<any, any, ContextType>;
  hasRole?: HasRoleDirectiveResolver<any, any, ContextType>;
  oemOwns?: OemOwnsDirectiveResolver<any, any, ContextType>;
};

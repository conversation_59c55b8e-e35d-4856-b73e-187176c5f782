{"error_messages": {"aiAssistantUsersLimitReached": "There's a limit of {{}} user(s) who can manage AI features on your plan. To empower more users contact our support team for custom solutions.", "invalid_time_tracker_tag": "Selected tag is invalid", "tag_already_exists": "Tag already exists", "status_already_exists": "Status already exists", "type_already_exists": "Type already exists", "status_empty_value": "One or more statuses have empty value", "invalid_ticket_id": "Work Order ID is invalid", "forbidden": "Forbidden", "monthly_limit_reached": "Token monthly limit reached", "invalid_machine": "Machine does not belong to organization", "invalid_guide": "Guide does not belong to organization", "unauthorized": "Unauthorized 'apiToken'", "invalid_id_utf_encode": "Invalid id. Ensure the id is UTF-8 encoded.", "email_linking_limit": "Email account linking limit reached", "email_linking_max_limit": "New limit cannot be less than already linked account", "email_already_linked": "Email address is already linked", "invalid_email_address": "Invalid email address", "already_exists_id": "with id {id} already exists", "already_exists_serialNumber": "with serial number {serialNumber} already exists", "signature_exceeds_max_limit": "Signature must not exceed 2000 characters", "invalid_event_creation_date": "Time for work order creation cannot be in the past", "ticket_not_linked": "Work orders are not linked.", "ticket_already_linked": "Work orders are already linked.", "bulk_upload_ticket_already_exists": "Ticket with inserted ID already exists", "invalid_connection_id": "Invalid Connection ID", "invalid_contact_id": "Invalid Contact ID", "invitation_expired": "Invitation expired", "not_found": "{} not found", "invalid_product_access": "Invalid product access", "oem_not_owns_product": "You can not activate a product unless you have purchased the product plan", "invalid": "Invalid {}", "already_linked_email": "This email address is already associated with connection {}. Please use another email address or contact to invite.", "plan_limit_reached": "Plan limit has been reached", "invite_not_allowed": "Cannot invite contact {} as they are part of another organization", "email_change_not_allowed": "Cannot change email once contact is invited", "email_already_in_oem": "Cannot invite emails of your own OEM", "request_already_sent": "Connection request already sent", "request_declined_recently": "Connection request was declined recently, please try again later", "technician_role_forbidden": "Technician role cannot request for products.", "already_invited": "You are already invited, please check your email inbox for the invitation", "already_accessible": "You already have access to this organization's products"}, "ai_notetaker": {"max_limit_reached": "Translation failed as it has exceeded maximum allowed length"}, "procedure_pdf": {"deleted_facility": "Deleted facility", "deleted_connection": "Deleted connection", "deleted_asset": "Deleted asset", "facility_name": "Facility name", "connection_name": "Connection name", "facility_address": "Facility address", "connection_address": "Connection address", "machine": "Machine", "asset": "Asset name", "work_order_title": "Work order title", "work_order_id": "Work order ID", "work_order_creation_date": "Work order creation date", "id": "ID", "n_a": "N/A"}, "work_order_pdf": {"deleted_facility": "Deleted facility", "deleted_connection": "Deleted connection", "deleted_asset": "Deleted asset", "facility": "Facility", "connection": "Connection", "machine": "Machine", "asset": "Asset name", "work_order_title": "Work order title", "work_order_created": "Work order created", "work_order_id": "Work order ID", "scheduled_date_and_time": "Scheduled date and time", "reporter": "Reporter", "work_order_description": "Work order description", "facility_name": "Facility name", "connection_name": "Connection name", "facility_address": "Facility address", "connection_address": "Connection address", "work_order_creation_date": "Work order creation date", "id": "ID", "n_a": "N/A"}, "work_order": {"not_found": "Work Order not found", "service_request_created": "Service request created", "spare_part_order_created": "Spare part order created"}, "ai_assistant_chat": {"not_found": "AI Assistant <PERSON><PERSON> not found"}, "asset": {"asset": "<PERSON><PERSON>", "assign_team": "Need to assign at least one team to the created asset", "cannot_assign_asset": "Unassign the attached asset from a facility to assign to an asset", "cannot_assign_facility": "Unassign the attached asset from another asset to assign to a facility", "cannot_assign_to_both": "Asset cannot be assigned to both facility and another asset", "cannot_delete_asset_with_parent_and_children": "Cannot delete asset with parent asset and sub-assets", "cannot_unassign_facility": "Cannot unassign asset from facility as it is not a parent asset", "created": "<PERSON>set created successfully!", "name_serial_number_required": "Name and serial number are required", "not_allowed_to_delete": "You are not allowed to delete this asset", "not_allowed_to_delete_image": "You are not allowed to delete this asset's image", "not_found": "Asset not found", "parent_not_found": "Parent asset not found", "updated": "Asset updated successfully!"}, "asset_template": {"template_in_use": "Products can only be deleted if all assets created from this product are deleted first.", "dont_own": "You do not own this asset template", "not_found": "Asset template not found"}, "asset_type": {"already_exists": "Asset type already exists", "cannot_delete": "This asset type is assigned to asset(s). You cannot delete this asset type at this time. To delete this asset, please first change the asset types from all assets.", "not_found": "Asset type not found"}, "facility": {"not_found": "Facility not found"}, "inventory_parts": {"already_assigned": "One or more inventory parts are already assigned", "invalid": "One or more inventory parts specified are invalid"}, "oem": {"not_found": "Oem not found"}, "signup": {"already_exists": "User with this email already exists", "email_mismatch": "Email mismatch", "invitation_expired": "Invitation expired", "invalid_invitation": "Invalid invitation", "invitation_accepted": "Successfully accepted invite"}, "ticket": {"not_found": "Ticket not found"}, "team": {"not_found": "Team not found"}, "schedule": {"created": "Schedule created successfully!", "no_access": "Scheduler is not available for this organization", "invalid_date_time": "End date time must be after start date time"}, "user": {"not_found": "User not found!"}}
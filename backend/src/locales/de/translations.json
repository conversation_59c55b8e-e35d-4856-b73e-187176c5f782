{"error_messages": {"aiAssistantUsersLimitReached": "<PERSON>s gibt ein Limit von {{}} <PERSON><PERSON><PERSON>, die KI-Funktionen in Ihrem Tarif verwalten können. Wenn Sie mehr Benutzer befähigen möchten, wenden Sie sich an unser Support-Team für individuelle Lösungen.", "invalid_time_tracker_tag": "Ausgewählter Tag ist ungültig", "tag_already_exists": "Tag existiert bereits", "status_already_exists": "Status existiert bereits", "type_already_exists": "Typ existiert bereits", "status_empty_value": "Ein oder mehrere Status haben einen leeren Wert", "forbidden": "Verbotene", "monthly_limit_reached": "Monatliches Token-Limit erreicht", "invalid_machine": "Die Maschine gehört nicht der Organisation", "invalid_guide": "Der Leitfaden gehört nicht zur Organisation", "unauthorized": "Nicht autorisierter 'apiToken'", "invalid_id_utf_encode": "Ungültige ID. <PERSON><PERSON><PERSON>, dass die ID in UTF-8 kodiert ist.", "email_linking_limit": "Limit für E-Mail-Kontoverknüpfung erreicht", "already_exists_id": "mit id {id} existiert bereits", "email_linking_max_limit": "Das neue Limit kann nicht niedriger sein als das bereits verknüpfte Konto", "email_already_linked": "E-Mail-Adresse ist bereits verlinkt", "invalid_email_address": "Ungültige E-Mail Adresse", "signature_exceeds_max_limit": "Die Unterschrift darf 2000 Zeichen nicht überschreiten", "ticket_not_linked": "Arbeitsaufträge sind nicht verknüpft.", "ticket_already_linked": "Arbeitsaufträge sind bereits verknüpft.", "invalid_ticket_id": "Work Order ID ist ungültig", "invalid_event_creation_date": "Der Zeitpunkt für die Erstellung des Arbeitsauftrags kann nicht in der Vergangenheit liegen", "bulk_upload_ticket_already_exists": "Ticket mit eingegebener ID existiert bereits", "invalid_connection_id": "Ungültige Verbindungs-ID", "invalid_contact_id": "Ungültige Kontakt-ID", "invitation_expired": "Einladung abgelaufen", "already_exists_serialNumber": "mit Seriennummer {serialNumber} existiert bereits", "not_found": "{} nicht gefunden", "invalid_product_access": "Ungültiger Produktzugang", "oem_not_owns_product": "<PERSON>e können ein Produkt nur aktivieren, wenn Sie den Produktplan erworben haben.", "already_linked_email": "Diese E-Mail-Adresse ist bereits mit der Verbindung {} verbunden. Bitte verwenden Sie eine andere E-Mail-Adresse oder einen anderen Kontakt für die Einladung.", "plan_limit_reached": "Das Planlimit wurde erreicht", "invalid": "Ung<PERSON><PERSON>ig {}", "invite_not_allowed": "<PERSON>nn den Kontakt {} nicht e<PERSON><PERSON>n, da er zu einer anderen Organisation gehört", "email_change_not_allowed": "E-Mail kann nicht geändert werden, sobald ein Kontakt eingeladen wurde", "email_already_in_oem": "<PERSON>e können keine E-Mails von Ihrem eigenen OEM einladen", "request_already_sent": "Verbindungsanfrage bereits gesendet", "technician_role_forbidden": "Die Technikerrolle kann keine Produkte anfordern.", "already_invited": "Sie sind bereits eingeladen, bitte prüfen Sie Ihren E-Mail-Posteingang auf die Einladung", "already_accessible": "Sie haben bereits Zugang zu den Produkten dieser Organisation", "request_declined_recently": "Die Verbindungsanfrage wurde kürzlich abgelehnt, bitte versuchen Si<PERSON> es später noch einmal"}, "procedure_pdf": {"facility_name": "Name der Einrichtung", "facility_address": "Adresse der Einrichtung", "connection_name": "Name der Verbindung", "deleted_connection": "Gelöschte Verbindung", "connection_address": "Adresse der Verbindung", "machine": "<PERSON><PERSON><PERSON>", "id": "ID", "deleted_facility": "Gelöschte Einrichtung", "work_order_title": "Titel des Arbeitsauftrags", "work_order_id": "ID des Arbeitsauftrags", "n_a": "K.A.", "asset": "Name des Assets", "work_order_creation_date": "Erstellungsdatum des Arbeitsauftrags", "deleted_asset": "Gelöschter Vermögenswert"}, "work_order": {"service_request_created": "Serviceanfrage erstellt", "spare_part_order_created": "Ersatzteilbestellung erstellt", "not_found": "Arbeitsauftrag nicht gefunden"}, "work_order_pdf": {"facility": "Einrichtung", "connection": "Verbindung", "machine": "<PERSON><PERSON><PERSON>", "work_order_id": "ID des Arbeitsauftrags", "reporter": "Reporter", "work_order_description": "Beschreibung des Arbeitsa: ", "facility_name": "Name der Einrichtung", "facility_address": "Adresse der Einrichtung", "id": "ID", "deleted_facility": "Gelöschte Einrichtung", "deleted_connection": "Gelöschte Verbindung", "n_a": "K.A.", "work_order_title": "Titel des Arbeitsauftrags", "asset": "Asset-Name", "scheduled_date_and_time": "Geplantes Datum und Uhrzeit", "work_order_creation_date": "Erstellungsdatum des Arbeitsauftrags", "work_order_created": "Arbeitsauftrag erstellt", "deleted_asset": "Gelöschter Vermögenswert", "connection_name": "Name der Verbindung", "connection_address": "Adresse der Verbindung"}, "asset": {"asset": "Vermögenswert", "assign_team": "<PERSON>e müssen dem erstellten Asset mindestens ein Team zuweisen.", "cannot_assign_asset": "<PERSON><PERSON> Si<PERSON> die Zuordnung des angehängten Assets von einer Anlage auf, um es einem Asset zuzuordnen", "cannot_assign_facility": "<PERSON><PERSON> Si<PERSON> die Zuordnung des angehängten Assets von einem anderen Asset auf, um es einer Einrichtung zuzuordnen", "cannot_assign_to_both": "Asset kann nicht sowohl der Anlage als auch einem anderen Asset zugeordnet werden", "created": "Asset erfolgreich erstellt!", "name_serial_number_required": "Name und Seriennummer sind erforderlich", "not_allowed_to_delete": "<PERSON>e sind nicht berechtigt, dies<PERSON> zu löschen", "not_allowed_to_delete_image": "<PERSON>e sind nicht berechtigt, das Bild dieses Assets zu löschen", "not_found": "Asset nicht gefunden", "parent_not_found": "Übergeordnetes Asset nicht gefunden", "updated": "Asset erfolgreich aktualisiert!", "cannot_delete_asset_with_parent_and_children": "Asset mit übergeordnetem Asset und Unter-Assets kann nicht gelöscht werden", "cannot_unassign_facility": "Die Zuordnung eines Assets zu einer Anlage kann nicht aufgehoben werden, da es sich nicht um ein übergeordnetes Asset handelt."}, "asset_template": {"dont_own": "Sie sind nicht Eigentümer dieser Asset-Vorlage", "not_found": "Asset-Vorlage nicht gefunden", "template_in_use": "Produkte können nur gelöscht werden, wenn vorher alle Assets, die von diesem Produkt erstellt wurden, gelö<PERSON>t werden."}, "asset_type": {"already_exists": "Asset-Typ existiert bereits", "cannot_delete": "<PERSON><PERSON> ist einem oder mehreren Assets zugeordnet. Si<PERSON> können diese Assetart zur Zeit nicht löschen. Um dieses Asset zu löschen, ändern Sie bitte zuerst die Asset-Typen von allen Assets.", "not_found": "Asset-<PERSON><PERSON> nicht gefunden"}, "facility": {"not_found": "Einrichtung nicht gefunden"}, "inventory_parts": {"already_assigned": "Ein oder mehrere Inventarteile sind bereits zugewiesen", "invalid": "Ein oder mehrere angegebene Inventarteile sind ungültig"}, "oem": {"not_found": "Oem nicht gefunden"}, "team": {"not_found": "Team nicht gefunden"}, "signup": {"already_exists": "Benutzer mit dieser E-Mail existiert bereits", "invalid_token": "Ungültiges Token", "email_mismatch": "E-Mail Unstimmigkeiten", "invitation_expired": "Einladung abgelaufen", "invalid_invitation": "Ungültige Einladung", "invitation_accepted": "Erfolgreich angenommene Einladung"}, "ticket": {"not_found": "Ticket nicht gefunden"}, "document_label": {"already_exists": "Dokumentenlabel existiert bereits", "cannot_delete": "Dieses Dokumentetikett ist einem oder mehreren Dokumenten zugeordnet. Sie können dieses Dokument-Etikett zur Zeit nicht löschen. Um dieses Dokument zu löschen, ändern Sie bitte zuerst die Dokumentbeschriftungen von allen Dokumenten.", "not_found": "Dokumentenlabel nicht gefunden"}, "ai_assistant_chat": {"not_found": "AI Assistant <PERSON><PERSON> nicht gefunden"}, "ai_notetaker": {"max_limit_reached": "Übersetzung fehlgeschlagen, da sie die maximal zulässige Länge überschritten hat"}, "schedule": {"created": "Zeitplan erfolgreich erstellt!", "no_access": "Scheduler ist für diese Organisation nicht verfügbar", "invalid_date_time": "Die Uhrzeit des Enddatums muss nach der Uhrzeit des Startdatums liegen."}, "user": {"not_found": "<PERSON>utzer nicht gefunden!"}}
import { GENERATE_DOCUMENT_TRANSLATIONS_JOB } from "~/agenda/constants";
import runJob from "~/agenda/run";
import { db } from "~/dataconnectors";
import { buildDataModels } from "~/datamodels";

(async function () {
  try {
    const dataModels = buildDataModels(db);

    const { Oem, Document } = dataModels;

    const oems = await Oem.find({});

    for (const oem of oems) {
      try {
        const documents = await Document.find({
          oem: oem._id,
        });
        console.log(
          "Generating translations for oem: ",
          oem.name,
          documents.length,
          "documents found",
        );
        for (const document of documents) {
          await runJob({
            jobName: GENERATE_DOCUMENT_TRANSLATIONS_JOB,
            oemId: oem._id,
            documentId: document._id,
          });
        }
      } catch (err) {
        console.log("Error syncing oem: ", oem.name, err);
      }
    }
  } catch (err) {
    console.log(err);
    process.exit(1);
  } finally {
    process.exit(1);
  }
})();

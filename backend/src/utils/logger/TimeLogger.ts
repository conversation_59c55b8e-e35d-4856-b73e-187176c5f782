import type { Logger } from "winston";
import cloudLogger from "~/utils/logger";

export default class TimeLogger {
  private logger: Logger;

  private timers: Map<string, number>;

  constructor(logger: Logger) {
    this.logger = logger;
    this.timers = new Map();
  }

  time(label: string) {
    this.timers.set(label, Date.now());
  }

  timeEnd(label: string) {
    const startTime = this.timers.get(label);
    if (!startTime) {
      this.logger.log("info", `Timer not found: ${label}`);
      return;
    }

    const duration = Date.now() - startTime;
    this.timers.delete(label);
    this.logger.log("info", `${label} - ${duration}ms`);
    return duration;
  }
}

export const timeLogger = new TimeLogger(cloudLogger);

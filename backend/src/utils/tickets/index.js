import moment from "moment-timezone";
import throwIfError from "~/utils/_throw-error";
import logger from "~/utils/logger";
import { mapColorsFromPalette } from "~/utils/_colors";
import {
  getOemChannelGroupName,
  getOemChannelId,
  getUserChatUUID,
  getFacilityChannelId,
  getFacilityChannelGroupName,
} from "~/utils/chat";
import { prepareProcedureForExport } from "~/utils/procedures";
import { sendEmail } from "~/config";
import { belongsToTicketTeams } from "./_helper";
import { OEM_ROLES, generateInitials } from "~/utils/user";
import { EnumRoles } from "~/graphql/types";
import TICKET_TYPE from "$/settings/enums/ticket/_types.json";
import { getEnums } from "~/utils/_get-enums";
import { CONTACT_ACCESS_STATUS } from "~/constants/contacts";
import PROCEDURE_STATES from "$/settings/enums/procedure/states.json";
import ForbiddenError from "~/errors/ForbiddenError";

const procedureStateEnum = getEnums(PROCEDURE_STATES, "reference");
const ticketTypeReference = getEnums(TICKET_TYPE, "reference");

const OEM_WORK_ORDER_DELETED_TEMPLATE = "work-order-deleted";
const WORK_ORDER_UPDATE_TEMPLATE = "work-order-update";

const PRIMARY_SECONDARY_COLOR_MAP = mapColorsFromPalette();

export async function getEligibleUsersForNewTicketMembership({
  dataSources,
  teams,
  oemId,
  customerId,
  getSeparately = false,
}) {
  const freeAndTeamUsersQuery = {
    $or: [{ "teams.0": { $exists: false } }],
  };

  if (teams.length) {
    freeAndTeamUsersQuery.$or.push({ teams: { $in: teams } });
  }

  const teamUsersQuery = {
    oem: oemId,
    $and: [freeAndTeamUsersQuery, { $or: [...OEM_ROLES].map(role => ({ role })) }],
    deleted: false,
  };

  const facilityUsersQuery = {
    oem: oemId,
    facility: customerId,
    $or: [{ role: EnumRoles.User }],
    deleted: false,
  };

  const [oemTicketTeamUsers, facilityUsers, linkedConnectionContacts] = await Promise.all([
    dataSources.User.getMany(teamUsersQuery),
    dataSources.User.getMany(facilityUsersQuery),
    dataSources.Contact.getMany({
      accessStatus: CONTACT_ACCESS_STATUS.ACTIVE,
      connection: customerId,
      oem: oemId,
    }),
  ]);

  if (getSeparately) {
    return {
      oem: oemTicketTeamUsers.map(u => getUserChatUUID(u._id.toString())),
      facility: facilityUsers.map(u => getUserChatUUID(u._id.toString())),
      connectionContacts: linkedConnectionContacts
        .filter(c => c.user)
        .map(c => getUserChatUUID(c.user.toString())),
    };
  }

  const users = [
    ...oemTicketTeamUsers,
    ...facilityUsers,
    linkedConnectionContacts.filter(c => c.user),
  ];

  const uuids = users.map(u => getUserChatUUID(u._id.toString()));

  return uuids;
}

export async function initializeChannelsOnTicketCreation({
  dataSources,
  oem,
  matchedTicketType,
  customer,
  newTicket,
  teams = [],
  user = {},
  createdByCustomer = false,
  isExternalTicketType,
}) {
  if (!dataSources || !oem || !matchedTicketType || !customer || !newTicket) return;

  const ticketChannel = `channel-${createdByCustomer ? customer.oem : user.organization}-${
    customer._id
  }-${newTicket._id}`;

  const internalTicketChannel = `channel-${createdByCustomer ? customer.oem : user.organization}-${
    newTicket._id
  }-internal-notes`;

  const oemChannelsGroupName = getOemChannelGroupName(oem._id);
  const facilityChannelsGroupName = getFacilityChannelGroupName(customer._id);

  const memberUuids = await getEligibleUsersForNewTicketMembership({
    dataSources,
    teams,
    oemId: oem._id,
    customerId: customer._id,
    getSeparately: true,
  });

  await Promise.all([
    ...(isExternalTicketType
      ? [
          dataSources.PubnubApi.setChannelMembers(
            ticketChannel,
            [...memberUuids.oem, ...memberUuids.facility, ...memberUuids.connectionContacts],
            new Date(newTicket.created_at).getTime() * 10000,
          ),
        ]
      : []),
    dataSources.PubnubApi.setChannelMembers(
      internalTicketChannel,
      memberUuids.oem,
      new Date(newTicket.created_at).getTime() * 10000,
    ),
    dataSources.PubnubApi.addChannelsToChannelGroup(
      isExternalTicketType ? [ticketChannel, internalTicketChannel] : [internalTicketChannel],
      oemChannelsGroupName,
    ),
    ...(isExternalTicketType
      ? [
          dataSources.PubnubApi.addChannelsToChannelGroup(
            [ticketChannel],
            facilityChannelsGroupName,
          ),
        ]
      : []),
  ]);
}

export async function informOemAndFacilityOfTicketCreation(
  dataSources,
  oemId,
  ticketId,
  userId,
  customerId,
  isExternalTicketType,
  isRequest = false,
) {
  const data = {
    message: {
      text: "ticketCreated",
      success: true,
      payload: { oemId, ticketId, userId, customerId },
    },
    meta: { isTicket: !isRequest, isRequest: true },
    channel: getOemChannelId(oemId),
  };

  await Promise.all([
    dataSources.PubnubApi.publishMessage(data),
    ...(isExternalTicketType
      ? [
          dataSources.PubnubApi.publishMessage({
            ...data,
            channel: getFacilityChannelId(customerId),
          }),
          dataSources.PubnubApi.publishMessage({
            ...data,
            channel: getFacilityChannelGroupName(customerId),
          }),
        ]
      : []),
  ]);
}

export async function informOrganizationOfTicketStatusUpdate(
  dataSources,
  oemId,
  customerId,
  ticketId,
  userId,
  ticketStatus,
) {
  const data = {
    message: {
      text: "ticketUpdated",
      success: true,
      payload: { oemId, ticketId, userId, ticketStatus },
    },
    meta: { isTicket: true },
    channel: getOemChannelId(oemId),
  };

  const customerData = {
    ...data,
    channel: getFacilityChannelId(customerId),
  };

  await Promise.all([
    dataSources.PubnubApi.publishMessage(data),
    dataSources.PubnubApi.publishMessage(customerData),
  ]);
}

export async function informOrganizationOfTicketDeletion(
  dataSources,
  oemId,
  customerId,
  ticketId,
  userId,
) {
  const data = {
    message: {
      text: "ticketDeleted",
      success: true,
      payload: { oemId, ticketId, userId },
    },
    meta: { isTicket: true },
    channel: getOemChannelId(oemId),
  };

  const customerData = {
    ...data,
    channel: getFacilityChannelId(customerId),
  };

  await Promise.all([
    dataSources.PubnubApi.publishMessage(data),
    dataSources.PubnubApi.publishMessage(customerData),
  ]);
}

export const getEmailInfoLine = ({
  isNewAssignee,
  hasScheduleChanged,
  hasStatusChanged,
  haveAssigneesChanged,
  haveResourcesChanged,
  hasEnabledWOEmailReminders,
}) => {
  if (isNewAssignee) {
    return "A new Work Order has been assigned to you";
  }

  const changes = [
    { condition: haveAssigneesChanged, line: "Assignee(s) of your Work Order have been updated" },
    { condition: haveResourcesChanged, line: "Resource(s) of your Work Order have been updated" },
    { condition: hasScheduleChanged, line: "Schedule of your Work Order has been updated" },
    { condition: hasStatusChanged, line: "Status of your Work Order has been updated" },
    { condition: hasEnabledWOEmailReminders, line: "Reminder" },
  ];

  const activeChanges = changes.filter(change => change.condition);

  if (activeChanges.length === 1) {
    return activeChanges[0].line;
  }

  return "Your Work Order has been updated";
};

export const getMachineAndFacilityIds = ({ ticket }) => {
  let facilityId = ticket.facility;
  let machineId = ticket.machine;

  if (ticket.ticketType === ticketTypeReference.FormSubmission) {
    facilityId = ticket.metadata?.facilityId;
    machineId = ticket.metadata?.machineId;
  }

  return {
    facilityId,
    machineId,
  };
};

export async function sendEmailToAssigneesForTicketUpdate({
  assignees = null,
  beforeUpdateStatus = "",
  dataSources,
  hasScheduleChanged = false,
  hasStatusChanged = false,
  haveAssigneesChanged = false,
  haveResourcesChanged = false,
  isNewAssignee = false,
  oem,
  ticket,
}) {
  const { facilityId, machineId } = getMachineAndFacilityIds({ ticket });
  const [facility, machine, resources, assigneeUsers] = await Promise.all([
    dataSources.Customer.loadOne(facilityId),
    dataSources.Machine.loadOne(machineId),
    dataSources.Resource.getMany({
      _id: { $in: ticket.resources },
    }),
    dataSources.User.getMany({
      _id: { $in: ticket.assignees },
    }),
  ]);

  const machineString = machine
    ? `${machine.name}${machine.serialNumber ? ` - ${machine.serialNumber}` : ""}`
    : "Deleted Machine";

  const facilityString = facility ? `${facility.name}` : "Deleted Facility";

  const assigneeString = assigneeUsers?.length
    ? `${assigneeUsers.map(assignee => assignee.name).join(" • ")}`
    : "-";

  let scheduleString = "Unscheduled";
  if (ticket.schedule) {
    if (ticket.schedule.isAllDay) {
      const startAt = moment(ticket?.schedule?.startTime);
      const endAt = moment(ticket?.schedule?.endTime);
      const isScheduleSameDay = endAt.diff(startAt, "day") === 0;

      scheduleString = `${moment(ticket.schedule.startTime).format("YYYY-MM-DD")}${
        !isScheduleSameDay ? ` - ${moment(ticket.schedule.endTime).format("YYYY-MM-DD")}` : ""
      } (All Day)`;
    } else {
      scheduleString = `${moment(ticket.schedule.startTime).format("YYYY-MM-DD")} • ${moment(
        ticket.schedule.startTime,
      ).format("h:mm A")} - ${moment(ticket.schedule.endTime).format("YYYY-MM-DD")} • ${moment(
        ticket.schedule.endTime,
      ).format("h:mm A")}`;
    }
  }

  const resourceString = resources?.length
    ? resources
        .map(({ name, resourceId }) => `${name}${resourceId ? ` - ${resourceId}` : ""}`)
        .join(" • ")
    : "No resources";

  let previousStatus = null;
  if (beforeUpdateStatus)
    previousStatus = oem?.statuses?.find(status => status._id.toString() === beforeUpdateStatus);
  const currentStatus = oem?.statuses?.find(
    status => status._id.toString() === ticket.status.toString(),
  );

  const actualAssignees = assignees?.length
    ? assigneeUsers.filter(({ _id }) => assignees.includes(_id.toString()))
    : assigneeUsers;

  actualAssignees.forEach(assigneeUser => {
    const isAssigneeNotificationEnabled = isNewAssignee
      ? assigneeUser?.notification?.email?.onNewTicketAssigned
      : assigneeUser?.notification?.email?.onAssignedWorkOrderUpdate;

    if (assigneeUser && isAssigneeNotificationEnabled) {
      const templateData = {
        workOrderUrl: `${process.env.OEM_APP_URI}/app/work-orders/${ticket._id}`,
        title: ticket.title,

        emailInfoLine: getEmailInfoLine({
          isNewAssignee,
          hasScheduleChanged,
          hasStatusChanged,
          haveAssigneesChanged,
          haveResourcesChanged,
        }),

        machineString,
        facilityString,

        haveAssigneesChanged,
        assigneeString,

        ...(hasStatusChanged
          ? {
              previousStatusBackgroundColor:
                PRIMARY_SECONDARY_COLOR_MAP[previousStatus?.color?.toLowerCase() || ""],
              previousStatusColor: previousStatus?.color,
              previousStatusLabel: previousStatus?.label?.toUpperCase(),
            }
          : {}),
        currentStatusBackgroundColor:
          PRIMARY_SECONDARY_COLOR_MAP[currentStatus?.color?.toLowerCase() || ""],
        currentStatusColor: currentStatus?.color,
        currentStatusLabel: currentStatus?.label?.toUpperCase(),

        hasScheduleChanged,
        scheduleString,

        haveResourcesChanged,
        resourceString,
      };

      const data = {
        to: assigneeUser.email,
        template: WORK_ORDER_UPDATE_TEMPLATE,
        subject: ticket.title,
        data: templateData,
      };

      sendEmail(data).catch(err => {
        console.log("[MAIL][ERROR]", err);
      });
    }
  });
}

export async function sendEmailToInformOfTicketDeletion({
  dataSources,
  oem,
  ticket,
  user,
  reason,
}) {
  const matchedTicketType = oem.ticketTypes?.find(tt => tt._id.toString() === ticket.ticketType);
  const ticketTypeIcon = matchedTicketType?.icon || "";

  const notificationUsers = oem.notification.email.workOrderCreationNotifyTo
    .filter(
      notificationUser =>
        notificationUser.workOrderTypes === null ||
        Boolean(
          notificationUser.workOrderTypes?.find(type =>
            ticket.ticketType.toString().includes(type),
          ),
        ),
    )
    .map(notificationUser => notificationUser.user);

  const oemUsersToNotify = [
    ...(oem?.notification?.email?.notifyOnWorkOrderCreation ? notificationUsers : []),
    ...(ticket.assignees || []),
  ];

  const $or = [{ _id: { $in: oemUsersToNotify }, ...belongsToTicketTeams(ticket) }];
  if (ticket.user) {
    $or.push({ _id: ticket.user });
  }

  const usersToNotify = await dataSources.User.getMany({ $or });
  if (usersToNotify?.length) {
    const serviceRequestTicket = oem?.ticketTypes?.find(t => t?.name === "Service Request");

    const isServiceRequest =
      !!serviceRequestTicket &&
      serviceRequestTicket._id?.toString() === ticket.ticketType?.toString();

    const templateData = {
      workOrderName: ticket.title,
      title: "Work order is deleted",
      isServiceRequest,
      icon: ticketTypeIcon,
      deletedBy: user.name,
      deletedAt: moment().format("YYYY/MM/DD"),
      reason,
    };

    usersToNotify.forEach(u => {
      const data = {
        to: u.email,
        template: OEM_WORK_ORDER_DELETED_TEMPLATE,
        subject: templateData.title,
        data: templateData,
      };

      sendEmail(data).catch(err => {
        logger.error("sendEmail", err);
      });
    });
  }
}

export async function removeTicketChannelFromGroups(dataSources, ticket) {
  const oemChannelsGroupName = getOemChannelGroupName(ticket.oem);
  const facilityChannelsGroupName = getFacilityChannelGroupName(ticket.facility);
  const ticketChannel = `channel-${ticket.oem?.toString()}-${ticket.facility?.toString()}-${
    ticket?._id
  }`;

  dataSources.PubnubApi.removeChannelsFromChannelGroup([ticketChannel], oemChannelsGroupName);
  dataSources.PubnubApi.removeChannelsFromChannelGroup([ticketChannel], facilityChannelsGroupName);
}

export async function addTicketChannelToGroups(dataSources, ticket) {
  const oemChannelsGroupName = getOemChannelGroupName(ticket.oem);
  const facilityChannelsGroupName = getFacilityChannelGroupName(ticket.facility);

  const ticketChannel = `channel-${ticket.oem?.toString()}-${ticket.facility?.toString()}-${
    ticket?._id
  }`;
  dataSources.PubnubApi.addChannelsToChannelGroup([ticketChannel], oemChannelsGroupName);
  dataSources.PubnubApi.addChannelsToChannelGroup([ticketChannel], facilityChannelsGroupName);
}

export async function validateAssignees({ dataSources, assignees, user, ticket }) {
  if (assignees?.length) {
    const validAssigneesCount = await dataSources.User.countDocuments({
      _id: { $in: assignees },
      oem: user.organization,
      ...belongsToTicketTeams(ticket),
      deleted: false,
    });

    if (validAssigneesCount !== assignees.length) {
      return throwIfError("One or more invalid assignees");
    }
  }
}

export const validateTicketType = async ({ dataSources, ticketType, user }) => {
  const teams = await dataSources.Team.getManyByQuery(
    {
      oem: user.organization,
      _id: { $in: user.teams },
      deleted: false,
    },
    { disallowedTicketTypes: 1, _id: 0 },
  );

  const disAllowedTicketTypes = [
    ...new Set(teams.flatMap(team => (team.disallowedTicketTypes || []).map(id => id.toString()))),
  ];

  if (disAllowedTicketTypes.includes(ticketType.toString())) {
    throw new ForbiddenError("Ticket type not allowed");
  }
};

export const secondsToHourMinute = (seconds, includeSecs = false) => {
  if (!seconds) {
    return includeSecs ? "00:00:00" : "00:00";
  }
  const hours = String(Math.floor(seconds / (60 * 60))).padStart(2, "0");
  const minutes = String(Math.floor((seconds / 60) % 60)).padStart(2, "0");
  const secs = String(seconds % 60).padStart(2, "0");

  return includeSecs ? `${hours}:${minutes}:${secs}` : `${hours}:${minutes}`;
};

const formatTimeRange = (start, end) =>
  `${moment(start).format("hh:mm A")} - ${moment(end).format("hh:mm A")}`;

const formatTotalTime = logs => {
  const totalSeconds = logs.reduce((sum, log) => sum + log.timeElapsedInSeconds, 0);
  return secondsToHourMinute(totalSeconds, true);
};

const mapCustomFields = async ({ ticket, dataSources }) => {
  const customFields = await dataSources.CustomAdditionalField.loadMany(
    ticket.customFields.map(cf => cf.fieldId),
  );
  const customFieldValues = ticket.customFields
    .map(cf => ({
      label:
        customFields.find(field => field._id.toString() === cf.fieldId.toString())?.label || "",
      value: cf.value,
    }))
    .filter(cf => cf.value);

  return customFieldValues;
};

const mapInventoryParts = async ({ inventoryParts, dataSources }) => {
  const parts = await dataSources.InventoryPart.loadMany(inventoryParts.map(part => part.part));
  const partsValues = parts.map(part => ({
    quantity:
      inventoryParts.find(ticketPart => part._id.toString() === ticketPart.part.toString())
        ?.quantity || 0,
    ...part,
  }));

  return partsValues;
};

const groupTimeTrackerLogs = logValues => {
  const data = {};
  for (const log of logValues) {
    const date = moment(log.startDateTime).format("YYYY-MM-DD");
    if (Object.hasOwnProperty.call(data, date)) {
      data[date].push({
        ...log,
        formattedRange: formatTimeRange(log.startDateTime, log.endDateTime),
        formattedDuration: secondsToHourMinute(log.timeElapsedInSeconds, true),
      });
    } else {
      data[date] = [
        {
          ...log,
          formattedRange: formatTimeRange(log.startDateTime, log.endDateTime),
          formattedDuration: secondsToHourMinute(log.timeElapsedInSeconds, true),
        },
      ];
    }
  }
  const sortedDates = Object.keys(data)
    .sort((a, b) => (a < b ? 1 : -1))
    .map(date => {
      const logs = data[date];
      const totalFormattedTime = formatTotalTime(logs);
      return { date, logs, totalFormattedTime };
    });

  return sortedDates;
};

const mapTimeTrackerLogs = async ({ timeTrackerLogs, dataSources }) => {
  const [timeTracker, users] = await Promise.all([
    dataSources.TimeTracker.loadMany(timeTrackerLogs.map(log => log.ticketTag)),
    dataSources.User.loadMany(timeTrackerLogs.map(log => log.createdBy)),
  ]);

  const logValues = timeTrackerLogs.map(ticketLog => ({
    color:
      timeTracker.find(log => log._id.toString() === ticketLog.ticketTag.toString())?.color || "",
    label:
      timeTracker.find(log => log._id.toString() === ticketLog.ticketTag.toString())?.label || "",
    createdByNameInitials: generateInitials(
      users.find(user => user._id.toString() === ticketLog.createdBy.toString())?.name,
    ),
    createdByName:
      users.find(user => user._id.toString() === ticketLog.createdBy.toString())?.name || "",
    ...ticketLog,
  }));

  return logValues;
};

const mapProcedures = async ({ dataSources, language, ticket, procedureIdsToInclude }) => {
  const procedures = await dataSources.Procedure.loadMany(
    ticket.procedures
      .map(mapLog => mapLog.procedure)
      .filter(filterLog => procedureIdsToInclude.includes(filterLog.toString())),
  );

  let finalizedProcedures = procedures
    ?.slice()
    ?.reverse()
    ?.filter(procedure => procedure.state === procedureStateEnum.FINALIZED);

  if (finalizedProcedures.length > 0) {
    finalizedProcedures = await Promise.all(
      finalizedProcedures.map(procedure =>
        prepareProcedureForExport({ dataSources, language, procedure, ticket }),
      ),
    );
  }
  return finalizedProcedures;
};

export const populateTicketForPDFExport = async ({
  dataSources,
  language,
  procedureIdsToInclude,
  sectionsToShow,
  ticket,
  timezone,
}) => {
  const [facility, machine, reporter] = await Promise.all([
    dataSources.Customer.getOneByQuery({ _id: ticket.facility }, { name: 1 }),
    dataSources.Machine.getOneByQuery({ _id: ticket.machine }, { name: 1, serialNumber: 1 }),
    dataSources.User.getOneByQuery({ _id: ticket.user }, { name: 1 }),
  ]);

  const createdAt = moment.tz(ticket.created_at, timezone).format("YYYY-MM-DDTHH:mm:ss.SSS");

  ticket.facilityObj = facility;
  ticket.machineObj = machine;
  ticket.reporter = reporter;

  ticket.created_at = createdAt;

  ticket.sectionsToShow = sectionsToShow;

  if (
    sectionsToShow.includes("customFields") &&
    Array.isArray(ticket.customFields) &&
    ticket.customFields.length > 0
  ) {
    ticket.customFields = await mapCustomFields({ ticket, dataSources });
  }

  if (
    sectionsToShow.includes("inventoryParts") &&
    Array.isArray(ticket.inventoryParts) &&
    ticket.inventoryParts.length > 0
  ) {
    ticket.inventoryParts = await mapInventoryParts({
      inventoryParts: ticket.inventoryParts,
      dataSources,
    });
  }

  if (
    sectionsToShow.includes("timeTrackerLogs") &&
    Array.isArray(ticket.timeTrackerLogs) &&
    ticket.timeTrackerLogs.length > 0
  ) {
    const logValues = await mapTimeTrackerLogs({
      timeTrackerLogs: ticket.timeTrackerLogs,
      dataSources,
    });
    ticket.timeTrackerLogs = logValues;
    ticket.timeTrackerLogsGrouped = groupTimeTrackerLogs(logValues);
  }

  if (
    sectionsToShow.includes("procedures") &&
    Array.isArray(ticket.procedures) &&
    ticket.procedures.length > 0
  ) {
    ticket.finalizedProcedures = await mapProcedures({
      dataSources,
      language,
      procedureIdsToInclude,
      ticket,
    });
  }

  let scheduleString = "";
  if (ticket.schedule) {
    if (ticket.schedule.isAllDay) {
      const startAt = moment.tz(ticket?.schedule?.startTime, timezone);
      const endAt = moment.tz(ticket?.schedule?.endTime, timezone);
      const isScheduleSameDay = endAt.diff(startAt, "day") === 0;

      scheduleString = `${startAt.format("YYYY-MM-DD")}${
        !isScheduleSameDay ? ` - ${endAt.format("YYYY-MM-DD")}` : ""
      } (All Day)`;
    } else {
      const startTime = moment.tz(ticket.schedule.startTime, timezone);
      const endTime = moment.tz(ticket.schedule.endTime, timezone);

      scheduleString = `${startTime.format("YYYY-MM-DD")} • ${startTime.format(
        "h:mm A",
      )} - ${endTime.format("YYYY-MM-DD")} • ${endTime.format("h:mm A")}`;
    }
  }

  ticket.scheduleString = scheduleString;

  return ticket;
};

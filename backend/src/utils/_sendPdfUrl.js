import path from "path";
import moment from "moment-timezone";
import puppeteer from "puppeteer";
import PDFMerger from "pdf-merger-js";
import fs from "fs";
import pug from "pug";
import { getSections, TEMPLATE_TYPES } from "~/templates/procedures/partials/constants";
import { getOemUserChannelId } from "~/utils/chat";
import logger from "~/utils/logger";
import { getDatedRandomStringForTempUrl, getInitials } from "~/utils/_util-methods";
import { NOTIFICATION_IDENTIFIERS } from "~/constants/notification-identifiers";
import { uploadToS3 } from "~/datasources/db/_utils/_s3";
import APP_FEATURES from "$/settings/app-features.json";
import { getEnums } from "~/utils/_get-enums";

import { isDevelopment } from "~/environment";

const appFeaturesEnum = getEnums(APP_FEATURES, "reference");

const mergePdfPages = async ({ pdfPages }) => {
  const merger = new PDFMerger();
  for (const page of pdfPages) {
    await merger.add(page);
  }

  const mergedPdfBuffer = await merger.saveAsBuffer();

  return mergedPdfBuffer;
};

const makeNestedArray = array => {
  const finalArray = [];
  let currentNested = [];

  for (const obj of array) {
    if (obj.type === "TABLE_FIELD" && obj.showInLandscape) {
      if (currentNested.length) {
        finalArray.push(currentNested);
      }
      currentNested = [];
      finalArray.push([obj]);
    } else {
      currentNested.push(obj);
    }
  }

  if (currentNested.length) {
    finalArray.push(currentNested);
  }

  return finalArray;
};

const processProcedure = procedure => {
  const flatArr = [];

  procedure.children.forEach(rc => {
    flatArr.push({
      ...rc,
      children: [],
      parent: null,
    });

    if (rc.children?.length) {
      rc.children.forEach(c => {
        flatArr.push({
          ...c,
          parent: rc?._id,
        });
      });
    }
  });

  const finalNestedArray = makeNestedArray(flatArr);
  return finalNestedArray;
};

const splitPages = (data, templateType) => {
  if (templateType === TEMPLATE_TYPES.PROCEDURES) return processProcedure(data);
  return [data];
};

const compileToHtml = async ({
  pageData,
  templateData,
  templateDir,
  language,
  templateType,
  oemLogo,
  actualTemplateType,
  generatedTime,
}) =>
  new Promise((resolve, reject) => {
    try {
      const fileLocation = path.join(templateDir, "procedures");
      const template = path.join(fileLocation, "mainTemplate.pug");
      const defaultPartImage = path.join(fileLocation, "parts-default-img.svg");
      const defaultAssetImage = path.join(fileLocation, "thumbnail.svg");
      const onePage = pageData.length <= 1;
      const compiledHtmls = [];
      const htmlCompiler = pug.compile(fs.readFileSync(template, "utf-8"), {
        basedir: templateDir,
        filename: template,
      });

      const sections = getSections(templateData, language, templateType);

      (pageData.length ? pageData : [[]]).forEach((page, index) => {
        templateData.children = page;
        const isFirstPage = index === 0;
        const isLastPage = !onePage && index === pageData.length - 1;

        const htmlFile = htmlCompiler({
          oemLogo,
          defaultPartImage,
          onePage,
          firstPage: isFirstPage,
          lastPage: isLastPage,
          moment,
          getInitials,
          language: language || {},
          data: templateData,
          sections,
          templateType,
          actualTemplateType,
          defaultAssetImage,
          generatedTime,
        });
        compiledHtmls.push(htmlFile);
      });

      resolve(compiledHtmls);
    } catch (err) {
      reject(err);
    }
  });

const generatePDF = async ({ template, templateDir, browser }) => {
  const fileLocation = path.join(templateDir, "procedures");
  const blankHtml = path.join(fileLocation, "blank.html");

  try {
    const page = await browser.newPage();
    await page.goto(`file:${blankHtml}`);
    await page.setContent(template, {
      waitUntil: ["load", "domcontentloaded"],
    });

    const pdfBuffer = await page.pdf({
      format: "A4",
      margin: {
        bottom: "24px",
        top: "24px",
        left: "24px",
        right: "24px",
      },
      printBackground: true,
      preferCSSPageSize: true,
    });

    return pdfBuffer;
  } catch (error) {
    logger.error("generatePDF", error);
  }
};

export const uploadAndGetPDFUrl = async ({
  pdf,
  oemSlug,
  suffix,
  templateType,
  download = false,
}) => {
  const uploadPath = `${download ? "downloads/" : ""}oem/${oemSlug}/${templateType}/${suffix}.pdf`;
  const url = await uploadToS3({ bufferData: pdf, uploadPath });
  return url;
};

export const getGeneratedPdf = async ({
  data,
  templateDir,
  language = {},
  templateType,
  oemLogo,
  generatedTime,
}) => {
  const options = {
    headless: "new",
    ...(isDevelopment
      ? {}
      : {
          executablePath: "/usr/bin/google-chrome",
          args: ["--no-sandbox", "--disable-setuid-sandbox"],
        }),
  };
  const browser = await puppeteer.launch(options);
  try {
    const templateData = JSON.parse(JSON.stringify(data));
    const pages = splitPages(data, templateType);

    const compiledHtmls = await compileToHtml({
      pageData: pages,
      templateData,
      templateDir,
      language: language || {},
      templateType,
      oemLogo,
      actualTemplateType: templateType,
      generatedTime,
    });

    if (templateType === TEMPLATE_TYPES.WORK_ORDER && Array.isArray(data.finalizedProcedures)) {
      const workOrderProcedureHtmls = (
        await Promise.all(
          data.finalizedProcedures.map(
            async procedure =>
              await compileToHtml({
                pageData: processProcedure(procedure),
                templateData: procedure,
                templateDir,
                language: language || {},
                actualTemplateType: templateType,
                templateType: TEMPLATE_TYPES.PROCEDURES,
                oemLogo,
                generatedTime,
              }),
          ),
        )
      ).flat();

      compiledHtmls.push(...workOrderProcedureHtmls);
    }

    const pdfBuffers = await Promise.all(
      compiledHtmls.map(async html =>
        generatePDF({
          template: html,
          templateDir,
          browser,
        }),
      ),
    );

    if (pdfBuffers.length > 1) {
      const mergedPdf = await mergePdfPages({ pdfPages: pdfBuffers });
      return mergedPdf;
    }

    return pdfBuffers[0];
  } catch (error) {
    logger.error("getGeneratedPdf", error);
    return Promise.reject(error);
  } finally {
    browser.close();
  }
};

export const sendPDFUrl = async ({
  data,
  oem,
  userId,
  dataSources,
  uuid,
  language,
  templateType,
  timezone,
}) => {
  const templateDir = path.join(__dirname, "..", "templates");
  const channel = getOemUserChannelId(userId);
  const meta = {
    isProcedure: true,
    uuid,
  };

  const generatedTimeTimezoneAdjusted = moment().tz(timezone)?.format("YYYY-MM-DD");

  try {
    const pdf = await getGeneratedPdf({
      data,
      templateDir,
      language,
      templateType,
      oemLogo: oem.brandLogo ?? "",
      generatedTime: generatedTimeTimezoneAdjusted,
    });

    const pdfUrl = await uploadAndGetPDFUrl({
      oemSlug: oem.urlOem,
      pdf,
      suffix: `${data._id}/${(data.name ?? data.title)
        ?.toLowerCase()
        ?.replace(" ", "-")}-${getDatedRandomStringForTempUrl()}`,
      templateType,
      download: true,
    });

    const publishPayload = {
      message: {
        text: NOTIFICATION_IDENTIFIERS.PROCEDURE_PDF_DOWNLOAD,
        success: true,
        payload: { name: data.name ?? data.title, _id: data._id, url: pdfUrl },
      },
      meta,
      channel,
    };

    dataSources.PubnubApi.publishMessage(publishPayload);
  } catch (error) {
    const publishPayload = {
      message: {
        text: NOTIFICATION_IDENTIFIERS.PROCEDURE_PDF_DOWNLOAD,
        success: false,
        error,
      },
      meta,
      channel,
    };

    dataSources.PubnubApi.publishMessage(publishPayload);
    logger.error("sendPDFUrl", error);
  }
};
